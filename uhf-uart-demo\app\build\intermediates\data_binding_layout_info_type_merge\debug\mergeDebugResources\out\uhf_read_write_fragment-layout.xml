<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="uhf_read_write_fragment" modulePackage="com.example.uhf" filePath="app\src\main\res\layout\uhf_read_write_fragment.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/uhf_read_write_fragment_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="279" endOffset="12"/></Target><Target id="@+id/cb_filter" view="CheckBox"><Expressions/><location startLine="26" startOffset="12" endLine="32" endOffset="41"/></Target><Target id="@+id/etPtr_filter" view="EditText"><Expressions/><location startLine="43" startOffset="16" endLine="49" endOffset="39"/></Target><Target id="@+id/etLen_filter" view="EditText"><Expressions/><location startLine="62" startOffset="16" endLine="68" endOffset="38"/></Target><Target id="@+id/etData_filter" view="EditText"><Expressions/><location startLine="85" startOffset="16" endLine="91" endOffset="47"/></Target><Target id="@+id/rbEPC_filter" view="RadioButton"><Expressions/><location startLine="100" startOffset="16" endLine="110" endOffset="68"/></Target><Target id="@+id/rbTID_filter" view="RadioButton"><Expressions/><location startLine="112" startOffset="16" endLine="123" endOffset="68"/></Target><Target id="@+id/rbUser_filter" view="RadioButton"><Expressions/><location startLine="125" startOffset="16" endLine="136" endOffset="68"/></Target><Target id="@+id/SpinnerBank" view="Spinner"><Expressions/><location startLine="154" startOffset="12" endLine="158" endOffset="52"/></Target><Target id="@+id/EtPtr" view="EditText"><Expressions/><location startLine="172" startOffset="12" endLine="181" endOffset="41"/></Target><Target id="@+id/EtLen" view="EditText"><Expressions/><location startLine="195" startOffset="12" endLine="204" endOffset="41"/></Target><Target id="@+id/EtAccessPwd" view="EditText"><Expressions/><location startLine="223" startOffset="12" endLine="227" endOffset="41"/></Target><Target id="@+id/EtData" view="EditText"><Expressions/><location startLine="242" startOffset="12" endLine="248" endOffset="44"/></Target><Target id="@+id/BtWrite" view="Button"><Expressions/><location startLine="257" startOffset="12" endLine="264" endOffset="50"/></Target><Target id="@+id/BtRead" view="Button"><Expressions/><location startLine="266" startOffset="12" endLine="274" endOffset="50"/></Target></Targets></Layout>