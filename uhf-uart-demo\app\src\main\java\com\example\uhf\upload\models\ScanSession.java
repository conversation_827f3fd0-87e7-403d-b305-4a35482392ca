package com.example.uhf.upload.models;

import com.google.gson.annotations.SerializedName;

/**
 * Data model for a scanning session
 * Contains metadata about when and where the scanning took place
 */
public class ScanSession {
    
    @SerializedName("sessionId")
    private String sessionId;
    
    @SerializedName("startTime")
    private String startTime;
    
    @SerializedName("endTime")
    private String endTime;
    
    @SerializedName("location")
    private String location;
    
    @SerializedName("deviceInfo")
    private String deviceInfo;
    
    @SerializedName("appVersion")
    private String appVersion;
    
    // Default constructor
    public ScanSession() {}
    
    // Constructor with required fields
    public ScanSession(String sessionId, String startTime, String endTime) {
        this.sessionId = sessionId;
        this.startTime = startTime;
        this.endTime = endTime;
    }
    
    // Constructor with all fields
    public ScanSession(String sessionId, String startTime, String endTime, 
                      String location, String deviceInfo, String appVersion) {
        this.sessionId = sessionId;
        this.startTime = startTime;
        this.endTime = endTime;
        this.location = location;
        this.deviceInfo = deviceInfo;
        this.appVersion = appVersion;
    }
    
    // Getters and Setters
    public String getSessionId() {
        return sessionId;
    }
    
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
    
    public String getStartTime() {
        return startTime;
    }
    
    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }
    
    public String getEndTime() {
        return endTime;
    }
    
    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
    
    public String getLocation() {
        return location;
    }
    
    public void setLocation(String location) {
        this.location = location;
    }
    
    public String getDeviceInfo() {
        return deviceInfo;
    }
    
    public void setDeviceInfo(String deviceInfo) {
        this.deviceInfo = deviceInfo;
    }
    
    public String getAppVersion() {
        return appVersion;
    }
    
    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }
    
    @Override
    public String toString() {
        return "ScanSession{" +
                "sessionId='" + sessionId + '\'' +
                ", startTime='" + startTime + '\'' +
                ", endTime='" + endTime + '\'' +
                ", location='" + location + '\'' +
                ", deviceInfo='" + deviceInfo + '\'' +
                ", appVersion='" + appVersion + '\'' +
                '}';
    }
}
