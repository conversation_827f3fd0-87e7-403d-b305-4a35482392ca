# 📱 Android Emulator Setup & Testing Guide

## 🔧 **Prerequisites Setup**

### **1. Install Android Studio (if not already installed)**
- Download from: https://developer.android.com/studio
- Install with default settings
- This includes Android SDK, emulator, and ADB tools

### **2. Set Environment Variables**
Add these to your system PATH:
```
C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools
C:\Users\<USER>\AppData\Local\Android\Sdk\emulator
C:\Users\<USER>\AppData\Local\Android\Sdk\tools\bin
```

**To add to PATH on Windows:**
1. Press `Win + R`, type `sysdm.cpl`, press Enter
2. Click "Environment Variables"
3. Edit "Path" in System Variables
4. Add the paths above

### **3. Verify Installation**
Open a new PowerShell/Command Prompt and test:
```bash
adb version
emulator -version
```

## 📱 **Create Android Emulator**

### **Option 1: Using Android Studio (Recommended)**
1. Open Android Studio
2. Go to `Tools` → `AVD Manager`
3. Click `Create Virtual Device`
4. Choose a device (e.g., Pixel 4)
5. Select API Level 28+ (Android 9+)
6. Click `Finish`

### **Option 2: Using Command Line**
```bash
# List available system images
avdmanager list target

# Create AVD
avdmanager create avd -n "UHF_Test" -k "system-images;android-30;google_apis;x86_64"
```

## 🚀 **Start Emulator & Deploy App**

### **1. Start the Emulator**
```bash
# List available AVDs
emulator -list-avds

# Start specific emulator
emulator -avd UHF_Test
```

### **2. Wait for Emulator to Boot**
- Wait until you see the Android home screen
- This may take 2-5 minutes on first boot

### **3. Verify Connection**
```bash
adb devices
# Should show: emulator-5554    device
```

### **4. Install the UHF App**
```bash
adb install UHF-serial_v1.4.5.apk
```

## 🧪 **Testing the Upload Functionality**

### **1. Start Test Server**
In a separate terminal:
```bash
cd uhf-uart-demo
python test_server.py
```

### **2. Get Your PC's IP Address**
```bash
# Windows
ipconfig | findstr IPv4

# Should show something like: *************
```

### **3. Configure App Settings**
1. Open the UHF app on emulator
2. Go to "Upload Settings" tab
3. Configure:
   - Server URL: `http://*************:8080/upload` (use your PC's IP)
   - API Key: `test-key`
   - Enable "Auto Upload"
4. Test connection

### **4. Test Upload Functionality**
1. Go to "Scan" tab
2. Since emulator doesn't have RFID hardware, we'll simulate data
3. Check if upload settings work
4. Monitor test server console for any requests

## 🔧 **Emulator-Specific Testing**

### **Simulate RFID Data (for testing)**
Since the emulator doesn't have RFID hardware, you can:

1. **Test Upload Settings Only**:
   - Configure server URL
   - Test connection button
   - Check network connectivity

2. **Mock Data Testing**:
   - The app may have demo/test data features
   - Look for any "Test" or "Demo" buttons in the app

3. **Log Monitoring**:
   ```bash
   adb logcat | findstr "com.maspects.uhftool"
   ```

## 📊 **VS Code Integration**

### **Update VS Code Tasks for Emulator**
The existing tasks in `.vscode/tasks.json` should work with emulator:

1. **Ctrl+Shift+P** → "Tasks: Run Task"
2. Select "Install Debug APK"
3. Select "View App Logs"

## 🐛 **Troubleshooting**

### **Common Issues & Solutions**

**1. Emulator won't start:**
```bash
# Enable hardware acceleration
emulator -avd UHF_Test -gpu host
```

**2. ADB not found:**
- Restart PowerShell after adding to PATH
- Or use full path: `C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools\adb.exe`

**3. App won't install:**
```bash
# Check if app is already installed
adb shell pm list packages | findstr uhftool

# Uninstall if needed
adb uninstall com.maspects.uhftool
```

**4. Network issues:**
- Emulator uses ******** to access host PC
- Try: `http://********:8080/upload` instead of your PC's IP

**5. Slow emulator:**
- Increase RAM: `emulator -avd UHF_Test -memory 4096`
- Enable hardware acceleration in BIOS

## 🎯 **Testing Checklist**

### **Emulator Setup**
- [ ] Android Studio installed
- [ ] Environment variables set
- [ ] ADB working (`adb version`)
- [ ] Emulator created
- [ ] Emulator boots successfully

### **App Deployment**
- [ ] APK installs without errors
- [ ] App launches successfully
- [ ] All tabs visible and functional
- [ ] No immediate crashes

### **Upload Testing**
- [ ] Upload Settings tab accessible
- [ ] Server URL can be configured
- [ ] API key can be set
- [ ] Connection test works (or shows appropriate error)
- [ ] Test server receives requests

### **Network Testing**
- [ ] Test server running on PC
- [ ] Emulator can reach PC network
- [ ] Upload requests appear in server logs
- [ ] App shows appropriate success/error messages

## 🚀 **Quick Start Commands**

```bash
# 1. Start emulator (replace with your AVD name)
emulator -avd Pixel_4_API_30

# 2. Install app
adb install UHF-serial_v1.4.5.apk

# 3. Start test server (separate terminal)
python test_server.py

# 4. Monitor app logs (separate terminal)
adb logcat | findstr "uhftool"
```

## 📝 **Expected Results**

**✅ Success Indicators:**
- Emulator boots and shows Android home screen
- UHF app installs and launches
- Upload Settings tab is accessible
- Connection test shows network activity
- Test server logs show incoming requests
- App doesn't crash during basic navigation

**⚠️ Limitations in Emulator:**
- No actual RFID scanning (hardware not available)
- Focus on testing upload service configuration
- Network connectivity testing
- UI/UX validation
- Settings persistence

---

**Next Steps:** Once emulator testing is complete, you can test on a real device with RFID hardware for full functionality validation.
