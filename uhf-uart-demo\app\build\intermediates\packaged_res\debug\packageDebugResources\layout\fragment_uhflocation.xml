<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
   >

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="EPC:" />
            <EditText
                android:id="@+id/etEPC"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dip"
            android:layout_weight="1">
            <com.example.uhf.view.UhfLocationCanvasView
                android:orientation="horizontal"
                android:id="@+id/llChart"
                android:layout_width="fill_parent"
                android:layout_height="fill_parent"
                android:gravity="center"
                android:background="@color/white"
                />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            tools:ignore="MissingConstraints">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="+"
                android:textSize="20sp"
                android:textStyle="bold" />

            <com.example.uhf.view.CircleSeekBar
                android:id="@+id/seekBarPower"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:max="30"
                android:min="5"
                android:progressDrawable="@drawable/seekbar_bg"
                android:splitTrack="false"
                android:thumb="@drawable/shape_seekbar_circle" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="-"
                android:textSize="20sp"
                android:textStyle="bold" />
        </LinearLayout>

        <TextView
            android:id="@+id/tvSearchRange"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/searchRange"
            android:textColor="@color/blue"
            android:textSize="16sp"
            android:textStyle="bold"
            tools:ignore="MissingConstraints" />
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <Button
                    android:id="@+id/btStart"
                    android:text="@string/start_location"
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="match_parent" />
                <Button
                    android:id="@+id/btStop"
                    android:text="@string/stop_location"
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="match_parent" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</FrameLayout>