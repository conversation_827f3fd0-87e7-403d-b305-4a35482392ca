<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="@color/white"
    android:orientation="vertical"
    android:fitsSystemWindows="true"
    android:clipToPadding="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="5dp"
        android:orientation="horizontal">
        <RadioGroup
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <RadioButton
                android:id="@+id/rb_uhf_module"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="UHF module"/>
            <RadioButton
                android:id="@+id/rb_ex10"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="50dp"
                android:text="Ex10 SDK firmware"/>

        </RadioGroup>
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/item_list_goods_show_list_bg"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/pop_menu_bg"
            android:focusable="true"
            android:gravity="bottom|center"
            android:orientation="horizontal">

            <EditText
                android:id="@+id/et_file"
                android:layout_width="3dp"
                android:layout_height="wrap_content"
                android:layout_weight="3"
                android:enabled="false"
                android:inputType="textNoSuggestions"
                android:singleLine="true" />

            <Button
                android:id="@+id/btnBrowser"
                android:layout_width="1dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/strBrowser"
                android:textSize="@dimen/text_size_14" />
        </LinearLayout>

        <Button
            android:id="@+id/btnUpgrade"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/strUpgrade" />
    </LinearLayout>



    <TextView
        android:text="..."
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:id="@+id/tvMsg" />

</LinearLayout>