<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_temperature" modulePackage="com.example.uhf" filePath="app\src\main\res\layout\fragment_temperature.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.FrameLayout"><Targets><Target tag="layout/fragment_temperature_0" view="FrameLayout"><Expressions/><location startLine="0" startOffset="0" endLine="95" endOffset="13"/></Target><Target id="@+id/SpinnerBank_T" view="Spinner"><Expressions/><location startLine="22" startOffset="8" endLine="26" endOffset="48"/></Target><Target id="@+id/etMSA" view="EditText"><Expressions/><location startLine="40" startOffset="12" endLine="44" endOffset="53"/></Target><Target id="@+id/etMDL" view="EditText"><Expressions/><location startLine="58" startOffset="12" endLine="62" endOffset="54"/></Target><Target id="@+id/etData" view="EditText"><Expressions/><location startLine="76" startOffset="12" endLine="79" endOffset="54"/></Target><Target id="@+id/btOK" view="Button"><Expressions/><location startLine="81" startOffset="8" endLine="85" endOffset="35"/></Target><Target id="@+id/tvData" view="TextView"><Expressions/><location startLine="86" startOffset="8" endLine="92" endOffset="37"/></Target></Targets></Layout>