package com.example.uhf.upload.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * Tracks upload status and history for monitoring and debugging purposes
 * Stores upload attempts, successes, and failures locally
 */
public class UploadStatusTracker {
    
    private static final String TAG = "UploadStatusTracker";
    private static final String PREFS_NAME = "upload_status";
    private static final String PREF_UPLOAD_HISTORY = "upload_history";
    private static final String PREF_LAST_UPLOAD_TIME = "last_upload_time";
    private static final String PREF_TOTAL_UPLOADS = "total_uploads";
    private static final String PREF_SUCCESSFUL_UPLOADS = "successful_uploads";
    private static final String PREF_FAILED_UPLOADS = "failed_uploads";
    
    private static final int MAX_HISTORY_ENTRIES = 50; // Keep last 50 upload attempts
    
    private Context context;
    private SharedPreferences preferences;
    private Gson gson;
    
    /**
     * Represents a single upload attempt
     */
    public static class UploadAttempt {
        public String timestamp;
        public boolean success;
        public int tagCount;
        public String errorMessage;
        public String uploadId;
        public long duration; // Upload duration in milliseconds
        
        public UploadAttempt() {}
        
        public UploadAttempt(boolean success, int tagCount, String errorMessage, String uploadId, long duration) {
            this.timestamp = getCurrentTimestamp();
            this.success = success;
            this.tagCount = tagCount;
            this.errorMessage = errorMessage;
            this.uploadId = uploadId;
            this.duration = duration;
        }
        
        private String getCurrentTimestamp() {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
            return sdf.format(new Date());
        }
        
        @Override
        public String toString() {
            return "UploadAttempt{" +
                    "timestamp='" + timestamp + '\'' +
                    ", success=" + success +
                    ", tagCount=" + tagCount +
                    ", errorMessage='" + errorMessage + '\'' +
                    ", uploadId='" + uploadId + '\'' +
                    ", duration=" + duration +
                    '}';
        }
    }
    
    public UploadStatusTracker(Context context) {
        this.context = context;
        this.preferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        this.gson = new Gson();
    }
    
    /**
     * Record a successful upload
     */
    public void recordSuccessfulUpload(int tagCount, String uploadId, long duration) {
        UploadAttempt attempt = new UploadAttempt(true, tagCount, null, uploadId, duration);
        addUploadAttempt(attempt);
        
        // Update counters
        int totalUploads = preferences.getInt(PREF_TOTAL_UPLOADS, 0) + 1;
        int successfulUploads = preferences.getInt(PREF_SUCCESSFUL_UPLOADS, 0) + 1;
        
        preferences.edit()
                .putInt(PREF_TOTAL_UPLOADS, totalUploads)
                .putInt(PREF_SUCCESSFUL_UPLOADS, successfulUploads)
                .putLong(PREF_LAST_UPLOAD_TIME, System.currentTimeMillis())
                .apply();
        
        Log.i(TAG, "Recorded successful upload: " + attempt);
    }
    
    /**
     * Record a failed upload
     */
    public void recordFailedUpload(int tagCount, String errorMessage, long duration) {
        UploadAttempt attempt = new UploadAttempt(false, tagCount, errorMessage, null, duration);
        addUploadAttempt(attempt);
        
        // Update counters
        int totalUploads = preferences.getInt(PREF_TOTAL_UPLOADS, 0) + 1;
        int failedUploads = preferences.getInt(PREF_FAILED_UPLOADS, 0) + 1;
        
        preferences.edit()
                .putInt(PREF_TOTAL_UPLOADS, totalUploads)
                .putInt(PREF_FAILED_UPLOADS, failedUploads)
                .apply();
        
        Log.w(TAG, "Recorded failed upload: " + attempt);
    }
    
    /**
     * Add upload attempt to history
     */
    private void addUploadAttempt(UploadAttempt attempt) {
        List<UploadAttempt> history = getUploadHistory();
        history.add(0, attempt); // Add to beginning of list
        
        // Limit history size
        if (history.size() > MAX_HISTORY_ENTRIES) {
            history = history.subList(0, MAX_HISTORY_ENTRIES);
        }
        
        // Save updated history
        String historyJson = gson.toJson(history);
        preferences.edit().putString(PREF_UPLOAD_HISTORY, historyJson).apply();
    }
    
    /**
     * Get upload history
     */
    public List<UploadAttempt> getUploadHistory() {
        String historyJson = preferences.getString(PREF_UPLOAD_HISTORY, "[]");
        Type listType = new TypeToken<List<UploadAttempt>>(){}.getType();
        List<UploadAttempt> history = gson.fromJson(historyJson, listType);
        return history != null ? history : new ArrayList<>();
    }
    
    /**
     * Get upload statistics
     */
    public UploadStatistics getUploadStatistics() {
        return new UploadStatistics(
                preferences.getInt(PREF_TOTAL_UPLOADS, 0),
                preferences.getInt(PREF_SUCCESSFUL_UPLOADS, 0),
                preferences.getInt(PREF_FAILED_UPLOADS, 0),
                preferences.getLong(PREF_LAST_UPLOAD_TIME, 0)
        );
    }
    
    /**
     * Clear all upload history and statistics
     */
    public void clearHistory() {
        preferences.edit()
                .remove(PREF_UPLOAD_HISTORY)
                .remove(PREF_LAST_UPLOAD_TIME)
                .remove(PREF_TOTAL_UPLOADS)
                .remove(PREF_SUCCESSFUL_UPLOADS)
                .remove(PREF_FAILED_UPLOADS)
                .apply();
        
        Log.i(TAG, "Upload history cleared");
    }
    
    /**
     * Get recent failed uploads (for retry purposes)
     */
    public List<UploadAttempt> getRecentFailedUploads(int maxCount) {
        List<UploadAttempt> history = getUploadHistory();
        List<UploadAttempt> failedUploads = new ArrayList<>();
        
        for (UploadAttempt attempt : history) {
            if (!attempt.success) {
                failedUploads.add(attempt);
                if (failedUploads.size() >= maxCount) {
                    break;
                }
            }
        }
        
        return failedUploads;
    }
    
    /**
     * Upload statistics container
     */
    public static class UploadStatistics {
        public final int totalUploads;
        public final int successfulUploads;
        public final int failedUploads;
        public final long lastUploadTime;
        
        public UploadStatistics(int totalUploads, int successfulUploads, int failedUploads, long lastUploadTime) {
            this.totalUploads = totalUploads;
            this.successfulUploads = successfulUploads;
            this.failedUploads = failedUploads;
            this.lastUploadTime = lastUploadTime;
        }
        
        public double getSuccessRate() {
            return totalUploads > 0 ? (double) successfulUploads / totalUploads * 100 : 0;
        }
        
        public String getLastUploadTimeFormatted() {
            if (lastUploadTime == 0) return "Never";
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
            return sdf.format(new Date(lastUploadTime));
        }
        
        @Override
        public String toString() {
            return "UploadStatistics{" +
                    "totalUploads=" + totalUploads +
                    ", successfulUploads=" + successfulUploads +
                    ", failedUploads=" + failedUploads +
                    ", successRate=" + String.format("%.1f%%", getSuccessRate()) +
                    ", lastUploadTime=" + getLastUploadTimeFormatted() +
                    '}';
        }
    }
}
