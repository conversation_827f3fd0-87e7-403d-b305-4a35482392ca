package com.example.uhf.upload.worker;

import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.work.Data;
import androidx.work.Worker;
import androidx.work.WorkerParameters;

import com.example.uhf.upload.models.UploadResponse;
import com.example.uhf.upload.service.UploadService;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.rscja.deviceapi.entity.UHFTAGInfo;

import java.lang.reflect.Type;
import java.util.List;

/**
 * Background worker for uploading RFID scan data
 * Uses WorkManager to handle background uploads with retry logic
 */
public class UploadWorker extends Worker {
    
    private static final String TAG = "UploadWorker";
    
    // Input data keys
    public static final String KEY_TAG_DATA = "tag_data";
    public static final String KEY_SCAN_START_TIME = "scan_start_time";
    public static final String KEY_SCAN_END_TIME = "scan_end_time";
    
    // Output data keys
    public static final String KEY_UPLOAD_SUCCESS = "upload_success";
    public static final String KEY_UPLOAD_MESSAGE = "upload_message";
    public static final String KEY_UPLOAD_ID = "upload_id";
    
    public UploadWorker(@NonNull Context context, @NonNull WorkerParameters workerParams) {
        super(context, workerParams);
    }
    
    @NonNull
    @Override
    public Result doWork() {
        Log.i(TAG, "Starting background upload work");
        
        try {
            // Get input data
            Data inputData = getInputData();
            String tagDataJson = inputData.getString(KEY_TAG_DATA);
            long scanStartTime = inputData.getLong(KEY_SCAN_START_TIME, System.currentTimeMillis());
            long scanEndTime = inputData.getLong(KEY_SCAN_END_TIME, System.currentTimeMillis());
            
            if (tagDataJson == null || tagDataJson.isEmpty()) {
                Log.e(TAG, "No tag data provided for upload");
                return Result.failure(createErrorOutput("No tag data provided"));
            }
            
            // Deserialize tag data
            Gson gson = new Gson();
            Type listType = new TypeToken<List<UHFTAGInfo>>(){}.getType();
            List<UHFTAGInfo> tagList = gson.fromJson(tagDataJson, listType);
            
            if (tagList == null || tagList.isEmpty()) {
                Log.e(TAG, "Failed to parse tag data or no tags found");
                return Result.failure(createErrorOutput("Failed to parse tag data"));
            }
            
            // Create upload service
            UploadService uploadService = new UploadService(getApplicationContext());
            
            // Check if service is configured
            if (!uploadService.isConfigured()) {
                Log.e(TAG, "Upload service not configured");
                return Result.failure(createErrorOutput("Upload service not configured. Please check server settings."));
            }
            
            // Perform synchronous upload
            final Object lock = new Object();
            final boolean[] uploadComplete = {false};
            final boolean[] uploadSuccess = {false};
            final String[] uploadMessage = {""};
            final String[] uploadId = {""};
            
            uploadService.uploadScanData(tagList, scanStartTime, scanEndTime, new UploadService.UploadCallback() {
                @Override
                public void onUploadSuccess(UploadResponse response) {
                    synchronized (lock) {
                        uploadComplete[0] = true;
                        uploadSuccess[0] = true;
                        uploadMessage[0] = response.getMessage();
                        uploadId[0] = response.getUploadId();
                        lock.notify();
                    }
                }
                
                @Override
                public void onUploadError(String error) {
                    synchronized (lock) {
                        uploadComplete[0] = true;
                        uploadSuccess[0] = false;
                        uploadMessage[0] = error;
                        lock.notify();
                    }
                }
                
                @Override
                public void onUploadProgress(int progress) {
                    // Update progress if needed
                    setProgressAsync(new Data.Builder()
                            .putInt("progress", progress)
                            .build());
                }
            });
            
            // Wait for upload to complete (with timeout)
            synchronized (lock) {
                if (!uploadComplete[0]) {
                    try {
                        lock.wait(60000); // 60 second timeout
                    } catch (InterruptedException e) {
                        Log.e(TAG, "Upload interrupted", e);
                        return Result.failure(createErrorOutput("Upload interrupted"));
                    }
                }
            }
            
            // Check result
            if (!uploadComplete[0]) {
                Log.e(TAG, "Upload timed out");
                return Result.failure(createErrorOutput("Upload timed out"));
            }
            
            if (uploadSuccess[0]) {
                Log.i(TAG, "Upload completed successfully: " + uploadMessage[0]);
                Data outputData = new Data.Builder()
                        .putBoolean(KEY_UPLOAD_SUCCESS, true)
                        .putString(KEY_UPLOAD_MESSAGE, uploadMessage[0])
                        .putString(KEY_UPLOAD_ID, uploadId[0])
                        .build();
                return Result.success(outputData);
            } else {
                Log.e(TAG, "Upload failed: " + uploadMessage[0]);
                return Result.retry(); // Retry on failure
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error during upload work", e);
            return Result.failure(createErrorOutput("Unexpected error: " + e.getMessage()));
        }
    }
    
    /**
     * Create error output data
     */
    private Data createErrorOutput(String errorMessage) {
        return new Data.Builder()
                .putBoolean(KEY_UPLOAD_SUCCESS, false)
                .putString(KEY_UPLOAD_MESSAGE, errorMessage)
                .build();
    }
    
    /**
     * Helper method to create work data for scheduling uploads
     */
    public static Data createInputData(List<UHFTAGInfo> tagList, long scanStartTime, long scanEndTime) {
        Gson gson = new Gson();
        String tagDataJson = gson.toJson(tagList);
        
        return new Data.Builder()
                .putString(KEY_TAG_DATA, tagDataJson)
                .putLong(KEY_SCAN_START_TIME, scanStartTime)
                .putLong(KEY_SCAN_END_TIME, scanEndTime)
                .build();
    }
}
