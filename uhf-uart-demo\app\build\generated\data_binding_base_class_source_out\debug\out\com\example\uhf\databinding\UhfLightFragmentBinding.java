// Generated by view binder compiler. Do not edit!
package com.example.uhf.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.RadioButton;
import android.widget.ScrollView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.uhf.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class UhfLightFragmentBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button btnLightContinuous;

  @NonNull
  public final Button btnLightSingle;

  @NonNull
  public final CheckBox cbLightFilter;

  @NonNull
  public final EditText etDataLightFilter;

  @NonNull
  public final EditText etLenLightFilter;

  @NonNull
  public final EditText etPtrLightFilter;

  @NonNull
  public final RadioButton rbEPCLightFilter;

  @NonNull
  public final RadioButton rbTIDLightFilter;

  @NonNull
  public final RadioButton rbUserLightFilter;

  private UhfLightFragmentBinding(@NonNull ScrollView rootView, @NonNull Button btnLightContinuous,
      @NonNull Button btnLightSingle, @NonNull CheckBox cbLightFilter,
      @NonNull EditText etDataLightFilter, @NonNull EditText etLenLightFilter,
      @NonNull EditText etPtrLightFilter, @NonNull RadioButton rbEPCLightFilter,
      @NonNull RadioButton rbTIDLightFilter, @NonNull RadioButton rbUserLightFilter) {
    this.rootView = rootView;
    this.btnLightContinuous = btnLightContinuous;
    this.btnLightSingle = btnLightSingle;
    this.cbLightFilter = cbLightFilter;
    this.etDataLightFilter = etDataLightFilter;
    this.etLenLightFilter = etLenLightFilter;
    this.etPtrLightFilter = etPtrLightFilter;
    this.rbEPCLightFilter = rbEPCLightFilter;
    this.rbTIDLightFilter = rbTIDLightFilter;
    this.rbUserLightFilter = rbUserLightFilter;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static UhfLightFragmentBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static UhfLightFragmentBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.uhf_light_fragment, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static UhfLightFragmentBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_light_continuous;
      Button btnLightContinuous = ViewBindings.findChildViewById(rootView, id);
      if (btnLightContinuous == null) {
        break missingId;
      }

      id = R.id.btn_light_single;
      Button btnLightSingle = ViewBindings.findChildViewById(rootView, id);
      if (btnLightSingle == null) {
        break missingId;
      }

      id = R.id.cb_light_filter;
      CheckBox cbLightFilter = ViewBindings.findChildViewById(rootView, id);
      if (cbLightFilter == null) {
        break missingId;
      }

      id = R.id.etData_light_filter;
      EditText etDataLightFilter = ViewBindings.findChildViewById(rootView, id);
      if (etDataLightFilter == null) {
        break missingId;
      }

      id = R.id.etLen_light_filter;
      EditText etLenLightFilter = ViewBindings.findChildViewById(rootView, id);
      if (etLenLightFilter == null) {
        break missingId;
      }

      id = R.id.etPtr_light_filter;
      EditText etPtrLightFilter = ViewBindings.findChildViewById(rootView, id);
      if (etPtrLightFilter == null) {
        break missingId;
      }

      id = R.id.rbEPC_light_filter;
      RadioButton rbEPCLightFilter = ViewBindings.findChildViewById(rootView, id);
      if (rbEPCLightFilter == null) {
        break missingId;
      }

      id = R.id.rbTID_light_filter;
      RadioButton rbTIDLightFilter = ViewBindings.findChildViewById(rootView, id);
      if (rbTIDLightFilter == null) {
        break missingId;
      }

      id = R.id.rbUser_light_filter;
      RadioButton rbUserLightFilter = ViewBindings.findChildViewById(rootView, id);
      if (rbUserLightFilter == null) {
        break missingId;
      }

      return new UhfLightFragmentBinding((ScrollView) rootView, btnLightContinuous, btnLightSingle,
          cbLightFilter, etDataLightFilter, etLenLightFilter, etPtrLightFilter, rbEPCLightFilter,
          rbTIDLightFilter, rbUserLightFilter);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
