# 📱 Upload Button & Functionality Guide

## 🎯 **Where to Find Upload Functionality**

### **Upload Button Location**
The Upload button should appear in the **SCAN tab** next to the Clear button:

```
┌─────────────────────────────────────┐
│ SCAN TAB                            │
├─────────────────────────────────────┤
│ [Start] [Clear] [Upload]            │
│                                     │
│ ┌─────────────────────────────────┐ │
│ │ Tag List                        │ │
│ │ EPC: E280...  Count: 3  RSSI:-45│ │
│ │ EPC: E281...  Count: 1  RSSI:-52│ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

### **Upload Settings Tab**
Look for the **"Upload Settings"** tab (usually the last tab):

```
┌─────────────────────────────────────┐
│ UPLOAD SETTINGS TAB                 │
├─────────────────────────────────────┤
│ Server URL: [________________]      │
│ API Key:    [________________]      │
│ □ Auto Upload                       │
│ [Test Connection] [Save Settings]   │
│                                     │
│ Connection Status: ●Connected       │
│ Last Upload: 2024-12-11 14:30:22   │
│ Upload Stats: 25 tags uploaded     │
└─────────────────────────────────────┘
```

## 🚀 **Quick Setup & Test**

### **Step 1: Install Working App**
```bash
.\build_with_upload.bat
```

### **Step 2: Configure Upload Settings**
1. Open app → Go to **"Upload Settings"** tab
2. Set **Server URL**: `http://10.0.2.2:8080/upload`
3. Set **API Key**: `test-key`
4. Check **"Auto Upload"** checkbox
5. Click **"Test Connection"**
6. Click **"Save Settings"**

### **Step 3: Test Upload Functionality**
1. Go to **"Scan"** tab
2. You should see: `[Start] [Clear] [Upload]`
3. Click **"Upload"** button to manually upload
4. Or enable auto-upload and scan tags

## 🔧 **If Upload Button Still Missing**

### **Troubleshooting Steps**

**1. Check App Package**
```bash
adb shell pm list packages | findstr uhf
```
Should show: `com.example.uhf` or `com.maspects.uhftool`

**2. Check App Version**
- Look for version info in app
- Should show upload functionality

**3. Reinstall Clean**
```bash
adb uninstall com.maspects.uhftool
adb uninstall com.example.uhf
adb install UHF-serial_v1.4.5.apk
```

**4. Check Layout Issues**
The button might be hidden due to screen size. Try:
- Rotating device to landscape
- Scrolling horizontally in scan tab
- Checking if button is off-screen

## 📊 **Upload Functionality Features**

### **Manual Upload (Upload Button)**
- Click **"Upload"** in Scan tab
- Uploads all scanned tags
- Shows progress dialog
- Displays success/error message

### **Auto Upload (Background)**
- Enable in Upload Settings
- Automatically uploads after scanning
- Works in background
- Shows notifications

### **Upload Data Format**
```json
{
  "sessionId": "scan_20241211_143022",
  "timestamp": "2024-12-11T14:30:22.123Z",
  "deviceInfo": {
    "model": "Android Device",
    "appVersion": "1.4.5"
  },
  "tags": [
    {
      "epc": "E2801160600002040000001F",
      "rssi": -45,
      "count": 3,
      "timestamp": "2024-12-11T14:30:22.123Z"
    }
  ]
}
```

## 🧪 **Testing Scenarios**

### **Test 1: Manual Upload**
1. Scan some tags (or use demo data)
2. Click **"Upload"** button
3. Check test server console
4. Verify success message

### **Test 2: Auto Upload**
1. Enable auto-upload in settings
2. Scan tags
3. Upload should happen automatically
4. Check notifications

### **Test 3: Connection Test**
1. Go to Upload Settings
2. Click **"Test Connection"**
3. Should show "Connected" status
4. Check test server logs

### **Test 4: Error Handling**
1. Set wrong server URL
2. Try to upload
3. Should show error message
4. Check retry mechanism

## 📱 **App Tabs Overview**

Expected tabs in the app:
1. **Scan** - Main scanning interface with Upload button
2. **Radar Location** - RFID location tracking
3. **Location** - Tag location features  
4. **Set** - Device settings
5. **Read/Write** - Tag read/write operations
6. **Light** - Tag light/beep functions
7. **Lock** - Tag locking features
8. **Kill** - Tag kill operations
9. **BlockWrite** - Block write operations
10. **BlockPermalock** - Block permalock operations
11. **Upgrader** - Firmware upgrade
12. **Upload Settings** - Upload configuration ⭐

## 🔍 **Verification Checklist**

- [ ] App installs without errors
- [ ] All 12 tabs are visible
- [ ] Upload Settings tab exists
- [ ] Scan tab shows [Start] [Clear] [Upload] buttons
- [ ] Upload Settings can be configured
- [ ] Test connection works
- [ ] Manual upload works
- [ ] Auto upload works
- [ ] Test server receives data
- [ ] Notifications appear
- [ ] Error handling works

## 🎉 **Success Indicators**

✅ **Upload button visible** in Scan tab
✅ **Upload Settings tab** accessible
✅ **Test connection** succeeds
✅ **Manual upload** works
✅ **Auto upload** triggers
✅ **Test server** receives data
✅ **Notifications** appear
✅ **Error handling** functions

---

**If you still don't see the Upload button after running the setup script, please let me know what tabs and buttons you see, and I'll provide additional troubleshooting steps.**
