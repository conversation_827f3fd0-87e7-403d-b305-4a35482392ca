# PowerShell script to setup and test UHF app on Android emulator

Write-Host "🚀 UHF App Emulator Testing Setup" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

# Function to check if command exists
function Test-Command($command) {
    try {
        Get-Command $command -ErrorAction Stop
        return $true
    }
    catch {
        return $false
    }
}

# Function to find Android SDK
function Find-AndroidSDK {
    $possiblePaths = @(
        "$env:ANDROID_HOME",
        "$env:LOCALAPPDATA\Android\Sdk",
        "$env:USERPROFILE\AppData\Local\Android\Sdk",
        "C:\Android\Sdk"
    )
    
    foreach ($path in $possiblePaths) {
        if ($path -and (Test-Path "$path\platform-tools\adb.exe")) {
            return $path
        }
    }
    return $null
}

# Step 1: Check Android SDK
Write-Host "`n📱 Step 1: Checking Android SDK..." -ForegroundColor Yellow

$androidSDK = Find-AndroidSDK
if (-not $androidSDK) {
    Write-Host "❌ Android SDK not found!" -ForegroundColor Red
    Write-Host "Please install Android Studio from: https://developer.android.com/studio" -ForegroundColor Yellow
    Write-Host "Or set ANDROID_HOME environment variable to your SDK path" -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ Android SDK found at: $androidSDK" -ForegroundColor Green

# Set up paths
$adbPath = "$androidSDK\platform-tools\adb.exe"
$emulatorPath = "$androidSDK\emulator\emulator.exe"
$avdManagerPath = "$androidSDK\cmdline-tools\latest\bin\avdmanager.bat"

# Step 2: Check ADB
Write-Host "`n🔧 Step 2: Checking ADB..." -ForegroundColor Yellow

if (Test-Path $adbPath) {
    Write-Host "✅ ADB found" -ForegroundColor Green
    $adbVersion = & $adbPath version 2>$null
    Write-Host "ADB Version: $($adbVersion[0])" -ForegroundColor Cyan
} else {
    Write-Host "❌ ADB not found at expected location" -ForegroundColor Red
    exit 1
}

# Step 3: Check emulator
Write-Host "`n📱 Step 3: Checking Emulator..." -ForegroundColor Yellow

if (Test-Path $emulatorPath) {
    Write-Host "✅ Emulator found" -ForegroundColor Green
} else {
    Write-Host "❌ Emulator not found at expected location" -ForegroundColor Red
    exit 1
}

# Step 4: List available AVDs
Write-Host "`n📋 Step 4: Checking Available AVDs..." -ForegroundColor Yellow

try {
    $avds = & $emulatorPath -list-avds 2>$null
    if ($avds) {
        Write-Host "✅ Available AVDs:" -ForegroundColor Green
        foreach ($avd in $avds) {
            Write-Host "  - $avd" -ForegroundColor Cyan
        }
        $selectedAVD = $avds[0]
    } else {
        Write-Host "⚠️ No AVDs found. You need to create one first." -ForegroundColor Yellow
        Write-Host "Please open Android Studio and create a virtual device." -ForegroundColor Yellow
        Write-Host "Or run: avdmanager create avd -n UHF_Test -k 'system-images;android-30;google_apis;x86_64'" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "❌ Could not list AVDs" -ForegroundColor Red
    exit 1
}

# Step 5: Check if emulator is already running
Write-Host "`n🔍 Step 5: Checking for running devices..." -ForegroundColor Yellow

$devices = & $adbPath devices 2>$null | Select-String "device$"
if ($devices) {
    Write-Host "✅ Found running devices:" -ForegroundColor Green
    foreach ($device in $devices) {
        Write-Host "  - $device" -ForegroundColor Cyan
    }
    $useExisting = Read-Host "Use existing device? (y/n)"
    if ($useExisting -eq 'n') {
        $startNew = $true
    } else {
        $startNew = $false
    }
} else {
    Write-Host "No devices running. Will start emulator." -ForegroundColor Yellow
    $startNew = $true
}

# Step 6: Start emulator if needed
if ($startNew) {
    Write-Host "`n🚀 Step 6: Starting emulator..." -ForegroundColor Yellow
    Write-Host "Starting AVD: $selectedAVD" -ForegroundColor Cyan
    Write-Host "This may take 2-5 minutes..." -ForegroundColor Yellow
    
    Start-Process -FilePath $emulatorPath -ArgumentList "-avd", $selectedAVD -WindowStyle Normal
    
    Write-Host "Waiting for emulator to boot..." -ForegroundColor Yellow
    do {
        Start-Sleep -Seconds 5
        $bootComplete = & $adbPath shell getprop sys.boot_completed 2>$null
        Write-Host "." -NoNewline -ForegroundColor Cyan
    } while ($bootComplete -ne "1")
    
    Write-Host "`n✅ Emulator is ready!" -ForegroundColor Green
}

# Step 7: Check for APK
Write-Host "`n📦 Step 7: Checking for APK..." -ForegroundColor Yellow

$apkPath = "UHF-serial_v1.4.5.apk"
if (Test-Path $apkPath) {
    Write-Host "✅ APK found: $apkPath" -ForegroundColor Green
} else {
    Write-Host "❌ APK not found: $apkPath" -ForegroundColor Red
    Write-Host "Please ensure the APK file is in the current directory" -ForegroundColor Yellow
    exit 1
}

# Step 8: Install APK
Write-Host "`n📲 Step 8: Installing APK..." -ForegroundColor Yellow

try {
    $installResult = & $adbPath install $apkPath 2>&1
    if ($installResult -match "Success") {
        Write-Host "✅ APK installed successfully!" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Install result: $installResult" -ForegroundColor Yellow
        if ($installResult -match "INSTALL_FAILED_ALREADY_EXISTS") {
            Write-Host "App already installed. Trying to reinstall..." -ForegroundColor Yellow
            & $adbPath install -r $apkPath
        }
    }
} catch {
    Write-Host "❌ Failed to install APK: $_" -ForegroundColor Red
    exit 1
}

# Step 9: Start test server
Write-Host "`n🌐 Step 9: Starting test server..." -ForegroundColor Yellow

if (Test-Path "test_server.py") {
    Write-Host "Starting Python test server..." -ForegroundColor Cyan
    Start-Process -FilePath "python" -ArgumentList "test_server.py" -WindowStyle Normal
    Write-Host "✅ Test server started in separate window" -ForegroundColor Green
} else {
    Write-Host "⚠️ test_server.py not found. You'll need to start it manually." -ForegroundColor Yellow
}

# Step 10: Get network info
Write-Host "`n🌐 Step 10: Network Information..." -ForegroundColor Yellow

$ipAddress = (Get-NetIPAddress -AddressFamily IPv4 | Where-Object {$_.IPAddress -like "192.168.*" -or $_.IPAddress -like "10.*"} | Select-Object -First 1).IPAddress
if ($ipAddress) {
    Write-Host "✅ Your PC IP: $ipAddress" -ForegroundColor Green
    Write-Host "Use this URL in the app: http://$ipAddress:8080/upload" -ForegroundColor Cyan
} else {
    Write-Host "⚠️ Could not determine IP address" -ForegroundColor Yellow
    Write-Host "For emulator, try: http://********:8080/upload" -ForegroundColor Cyan
}

# Step 11: Launch app
Write-Host "`n📱 Step 11: Launching app..." -ForegroundColor Yellow

try {
    & $adbPath shell am start -n com.maspects.uhftool/.activity.UHFMainActivity
    Write-Host "✅ App launched!" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Could not auto-launch app. Please open it manually." -ForegroundColor Yellow
}

# Final instructions
Write-Host "`n🎉 Setup Complete!" -ForegroundColor Green
Write-Host "==================" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next Steps:" -ForegroundColor Yellow
Write-Host "1. Open the UHF app on the emulator" -ForegroundColor White
Write-Host "2. Go to 'Upload Settings' tab" -ForegroundColor White
Write-Host "3. Configure:" -ForegroundColor White
Write-Host "   - Server URL: http://$ipAddress:8080/upload (or http://********:8080/upload)" -ForegroundColor Cyan
Write-Host "   - API Key: test-key" -ForegroundColor Cyan
Write-Host "   - Enable Auto Upload" -ForegroundColor Cyan
Write-Host "4. Test the connection" -ForegroundColor White
Write-Host "5. Check the test server console for requests" -ForegroundColor White
Write-Host ""
Write-Host "🔍 Monitor logs with:" -ForegroundColor Yellow
Write-Host "adb logcat | findstr uhftool" -ForegroundColor Cyan
Write-Host ""
Write-Host "Happy testing! 🚀" -ForegroundColor Green
