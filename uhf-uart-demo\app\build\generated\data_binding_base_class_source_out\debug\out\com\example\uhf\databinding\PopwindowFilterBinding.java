// Generated by view binder compiler. Do not edit!
package com.example.uhf.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.uhf.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class PopwindowFilterBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btSet;

  @NonNull
  public final Button btnDisable;

  @NonNull
  public final Button btnOk;

  @NonNull
  public final CheckBox cbFilter;

  @NonNull
  public final EditText etData;

  @NonNull
  public final EditText etLen;

  @NonNull
  public final EditText etPtr;

  @NonNull
  public final RadioButton rbEPC;

  @NonNull
  public final RadioButton rbRESERVED;

  @NonNull
  public final RadioButton rbTID;

  @NonNull
  public final RadioButton rbUser;

  private PopwindowFilterBinding(@NonNull LinearLayout rootView, @NonNull Button btSet,
      @NonNull Button btnDisable, @NonNull Button btnOk, @NonNull CheckBox cbFilter,
      @NonNull EditText etData, @NonNull EditText etLen, @NonNull EditText etPtr,
      @NonNull RadioButton rbEPC, @NonNull RadioButton rbRESERVED, @NonNull RadioButton rbTID,
      @NonNull RadioButton rbUser) {
    this.rootView = rootView;
    this.btSet = btSet;
    this.btnDisable = btnDisable;
    this.btnOk = btnOk;
    this.cbFilter = cbFilter;
    this.etData = etData;
    this.etLen = etLen;
    this.etPtr = etPtr;
    this.rbEPC = rbEPC;
    this.rbRESERVED = rbRESERVED;
    this.rbTID = rbTID;
    this.rbUser = rbUser;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static PopwindowFilterBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static PopwindowFilterBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.popwindow_filter, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static PopwindowFilterBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btSet;
      Button btSet = ViewBindings.findChildViewById(rootView, id);
      if (btSet == null) {
        break missingId;
      }

      id = R.id.btnDisable;
      Button btnDisable = ViewBindings.findChildViewById(rootView, id);
      if (btnDisable == null) {
        break missingId;
      }

      id = R.id.btnOk;
      Button btnOk = ViewBindings.findChildViewById(rootView, id);
      if (btnOk == null) {
        break missingId;
      }

      id = R.id.cb_filter;
      CheckBox cbFilter = ViewBindings.findChildViewById(rootView, id);
      if (cbFilter == null) {
        break missingId;
      }

      id = R.id.etData;
      EditText etData = ViewBindings.findChildViewById(rootView, id);
      if (etData == null) {
        break missingId;
      }

      id = R.id.etLen;
      EditText etLen = ViewBindings.findChildViewById(rootView, id);
      if (etLen == null) {
        break missingId;
      }

      id = R.id.etPtr;
      EditText etPtr = ViewBindings.findChildViewById(rootView, id);
      if (etPtr == null) {
        break missingId;
      }

      id = R.id.rbEPC;
      RadioButton rbEPC = ViewBindings.findChildViewById(rootView, id);
      if (rbEPC == null) {
        break missingId;
      }

      id = R.id.rbRESERVED;
      RadioButton rbRESERVED = ViewBindings.findChildViewById(rootView, id);
      if (rbRESERVED == null) {
        break missingId;
      }

      id = R.id.rbTID;
      RadioButton rbTID = ViewBindings.findChildViewById(rootView, id);
      if (rbTID == null) {
        break missingId;
      }

      id = R.id.rbUser;
      RadioButton rbUser = ViewBindings.findChildViewById(rootView, id);
      if (rbUser == null) {
        break missingId;
      }

      return new PopwindowFilterBinding((LinearLayout) rootView, btSet, btnDisable, btnOk, cbFilter,
          etData, etLen, etPtr, rbEPC, rbRESERVED, rbTID, rbUser);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
