# UHF Enhanced - RFID Scanner with Upload Service

This is an enhanced version of the original UHF RFID scanning application with added upload functionality.

## 🆕 What's New

### Enhanced Features
- **Upload Service**: Upload scanned RFID data to remote servers
- **Auto-Upload**: Automatic upload after scanning completion
- **Upload Settings**: Configure server URL, API key, and preferences
- **Upload Statistics**: Track upload history and success rates
- **Background Upload**: Reliable background uploads using WorkManager
- **Notifications**: Progress and status notifications for uploads

### App Changes
- **Package Name**: `com.example.uhf.enhanced` (can run alongside original)
- **App Name**: "UHF Enhanced"
- **Version**: 1.4.5-enhanced

## 🚀 Installation

### Using VS Code
1. Open project in VS Code
2. Run task: `Ctrl+Shift+P` → "Tasks: Run Task" → "Install Debug APK"
3. Or use: `./gradlew installDebug`

### Manual Installation
```bash
cd uhf-uart-demo
./gradlew assembleDebug
adb install app/build/outputs/apk/debug/UHF-serial-enhanced_v1.4.5-enhanced.apk
```

## 📱 Usage

### 1. Configure Upload Settings
- Open app → "Upload Settings" tab
- Enter server URL (e.g., `https://your-server.com/`)
- Enter API key for authentication
- Test connection to verify settings
- Enable auto-upload if desired

### 2. Scan RFID Tags
- Use the "Scan" tab as usual
- Scan RFID tags normally
- Data is collected in the tag list

### 3. Upload Data
- **Manual Upload**: Click "Upload" button in scan tab
- **Auto Upload**: Enable in settings for automatic upload after scanning
- **Background Upload**: Uses WorkManager for reliable uploads

## 🔧 Server Requirements

Your server needs to implement these endpoints:

### Upload Endpoint: `POST /api/rfid/upload`
```json
{
  "deviceId": "unique-device-id",
  "scanSession": {
    "sessionId": "uuid",
    "startTime": "2025-01-08T10:30:00Z",
    "endTime": "2025-01-08T10:35:00Z"
  },
  "tags": [
    {
      "epc": "E2001234567890123456",
      "tid": "E200123456789012",
      "userData": "custom-data",
      "rssi": "-45",
      "count": 5
    }
  ]
}
```

### Test Endpoint: `GET /api/rfid/ping`
```json
{
  "success": true,
  "message": "Server is running"
}
```

## 📊 Features

### Upload Statistics
- Total uploads attempted
- Success/failure counts
- Success rate percentage
- Last upload timestamp

### Notifications
- Upload progress notifications
- Success/failure notifications
- Background upload status

### Error Handling
- Network timeout handling
- Retry logic for failed uploads
- Detailed error messages
- Upload history tracking

## 🔍 Debugging

### View Logs
```bash
# View all app logs
adb logcat | grep "com.example.uhf"

# View upload service logs
adb logcat | grep "UploadService"

# View upload worker logs
adb logcat | grep "UploadWorker"
```

### Check Upload Status
- Open "Upload Settings" tab
- View upload statistics
- Check connection status
- Review upload history

## 🛠️ Development

### Project Structure
```
app/src/main/java/com/example/uhf/
├── upload/
│   ├── api/           # Retrofit API interfaces
│   ├── models/        # Data models for JSON
│   ├── service/       # Upload service logic
│   ├── utils/         # Notifications and tracking
│   └── worker/        # Background upload worker
├── fragment/
│   └── UploadSettingsFragment.java  # Settings UI
└── activity/
    └── UHFMainActivity.java         # Enhanced main activity
```

### Key Classes
- `UploadService`: Main upload functionality
- `UploadWorker`: Background upload processing
- `UploadSettingsFragment`: Configuration UI
- `UploadStatusTracker`: Upload history tracking
- `UploadNotificationHelper`: Notification management

## 🔒 Security

- API key authentication
- HTTPS support
- Device ID generation
- Secure data transmission

## 📝 Notes

- This enhanced version can run alongside the original app
- Upload settings are stored locally on the device
- Background uploads continue even when app is closed
- All upload attempts are logged for debugging

## 🆘 Troubleshooting

### Upload Fails
1. Check network connection
2. Verify server URL and API key
3. Test connection in Upload Settings
4. Check server logs for errors

### App Installation Issues
1. Ensure device has USB debugging enabled
2. Check ADB connection: `adb devices`
3. Try clean build: `./gradlew clean assembleDebug`

### Permission Issues
1. Grant storage permissions when prompted
2. Allow notifications for upload status
3. Check network permissions in settings
