// Generated by view binder compiler. Do not edit!
package com.example.uhf.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.uhf.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class UhfReadtagFragmentBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button BtClear;

  @NonNull
  public final Button BtInventory;

  @NonNull
  public final Button BtUpload;

  @NonNull
  public final ListView LvTags;

  @NonNull
  public final RadioButton RbInventoryLoop;

  @NonNull
  public final RadioButton RbInventorySingle;

  @NonNull
  public final RadioGroup RgInventory;

  @NonNull
  public final Button btSet;

  @NonNull
  public final Button btnDisable;

  @NonNull
  public final Button btnOk;

  @NonNull
  public final CheckBox cbEPCTam;

  @NonNull
  public final CheckBox cbFilter;

  @NonNull
  public final CheckBox cbFilter1;

  @NonNull
  public final CheckBox cbPhase;

  @NonNull
  public final EditText etData;

  @NonNull
  public final EditText etLen;

  @NonNull
  public final EditText etPtr;

  @NonNull
  public final EditText etTime;

  @NonNull
  public final LinearLayout layout12;

  @NonNull
  public final LinearLayout layout4;

  @NonNull
  public final LinearLayout layoutFilter;

  @NonNull
  public final LinearLayout llContinuous;

  @NonNull
  public final RadioButton rbEPC;

  @NonNull
  public final RadioButton rbRESERVED;

  @NonNull
  public final RadioButton rbTID;

  @NonNull
  public final RadioButton rbUser;

  @NonNull
  public final TextView tvContinuous;

  @NonNull
  public final TextView tvCount;

  @NonNull
  public final TextView tvMS;

  @NonNull
  public final TextView tvTime;

  @NonNull
  public final TextView tvTotal;

  private UhfReadtagFragmentBinding(@NonNull LinearLayout rootView, @NonNull Button BtClear,
      @NonNull Button BtInventory, @NonNull Button BtUpload, @NonNull ListView LvTags,
      @NonNull RadioButton RbInventoryLoop, @NonNull RadioButton RbInventorySingle,
      @NonNull RadioGroup RgInventory, @NonNull Button btSet, @NonNull Button btnDisable,
      @NonNull Button btnOk, @NonNull CheckBox cbEPCTam, @NonNull CheckBox cbFilter,
      @NonNull CheckBox cbFilter1, @NonNull CheckBox cbPhase, @NonNull EditText etData,
      @NonNull EditText etLen, @NonNull EditText etPtr, @NonNull EditText etTime,
      @NonNull LinearLayout layout12, @NonNull LinearLayout layout4,
      @NonNull LinearLayout layoutFilter, @NonNull LinearLayout llContinuous,
      @NonNull RadioButton rbEPC, @NonNull RadioButton rbRESERVED, @NonNull RadioButton rbTID,
      @NonNull RadioButton rbUser, @NonNull TextView tvContinuous, @NonNull TextView tvCount,
      @NonNull TextView tvMS, @NonNull TextView tvTime, @NonNull TextView tvTotal) {
    this.rootView = rootView;
    this.BtClear = BtClear;
    this.BtInventory = BtInventory;
    this.BtUpload = BtUpload;
    this.LvTags = LvTags;
    this.RbInventoryLoop = RbInventoryLoop;
    this.RbInventorySingle = RbInventorySingle;
    this.RgInventory = RgInventory;
    this.btSet = btSet;
    this.btnDisable = btnDisable;
    this.btnOk = btnOk;
    this.cbEPCTam = cbEPCTam;
    this.cbFilter = cbFilter;
    this.cbFilter1 = cbFilter1;
    this.cbPhase = cbPhase;
    this.etData = etData;
    this.etLen = etLen;
    this.etPtr = etPtr;
    this.etTime = etTime;
    this.layout12 = layout12;
    this.layout4 = layout4;
    this.layoutFilter = layoutFilter;
    this.llContinuous = llContinuous;
    this.rbEPC = rbEPC;
    this.rbRESERVED = rbRESERVED;
    this.rbTID = rbTID;
    this.rbUser = rbUser;
    this.tvContinuous = tvContinuous;
    this.tvCount = tvCount;
    this.tvMS = tvMS;
    this.tvTime = tvTime;
    this.tvTotal = tvTotal;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static UhfReadtagFragmentBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static UhfReadtagFragmentBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.uhf_readtag_fragment, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static UhfReadtagFragmentBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.BtClear;
      Button BtClear = ViewBindings.findChildViewById(rootView, id);
      if (BtClear == null) {
        break missingId;
      }

      id = R.id.BtInventory;
      Button BtInventory = ViewBindings.findChildViewById(rootView, id);
      if (BtInventory == null) {
        break missingId;
      }

      id = R.id.BtUpload;
      Button BtUpload = ViewBindings.findChildViewById(rootView, id);
      if (BtUpload == null) {
        break missingId;
      }

      id = R.id.LvTags;
      ListView LvTags = ViewBindings.findChildViewById(rootView, id);
      if (LvTags == null) {
        break missingId;
      }

      id = R.id.RbInventoryLoop;
      RadioButton RbInventoryLoop = ViewBindings.findChildViewById(rootView, id);
      if (RbInventoryLoop == null) {
        break missingId;
      }

      id = R.id.RbInventorySingle;
      RadioButton RbInventorySingle = ViewBindings.findChildViewById(rootView, id);
      if (RbInventorySingle == null) {
        break missingId;
      }

      id = R.id.RgInventory;
      RadioGroup RgInventory = ViewBindings.findChildViewById(rootView, id);
      if (RgInventory == null) {
        break missingId;
      }

      id = R.id.btSet;
      Button btSet = ViewBindings.findChildViewById(rootView, id);
      if (btSet == null) {
        break missingId;
      }

      id = R.id.btnDisable;
      Button btnDisable = ViewBindings.findChildViewById(rootView, id);
      if (btnDisable == null) {
        break missingId;
      }

      id = R.id.btnOk;
      Button btnOk = ViewBindings.findChildViewById(rootView, id);
      if (btnOk == null) {
        break missingId;
      }

      id = R.id.cbEPC_Tam;
      CheckBox cbEPCTam = ViewBindings.findChildViewById(rootView, id);
      if (cbEPCTam == null) {
        break missingId;
      }

      id = R.id.cbFilter;
      CheckBox cbFilter = ViewBindings.findChildViewById(rootView, id);
      if (cbFilter == null) {
        break missingId;
      }

      id = R.id.cb_filter;
      CheckBox cbFilter1 = ViewBindings.findChildViewById(rootView, id);
      if (cbFilter1 == null) {
        break missingId;
      }

      id = R.id.cbPhase;
      CheckBox cbPhase = ViewBindings.findChildViewById(rootView, id);
      if (cbPhase == null) {
        break missingId;
      }

      id = R.id.etData;
      EditText etData = ViewBindings.findChildViewById(rootView, id);
      if (etData == null) {
        break missingId;
      }

      id = R.id.etLen;
      EditText etLen = ViewBindings.findChildViewById(rootView, id);
      if (etLen == null) {
        break missingId;
      }

      id = R.id.etPtr;
      EditText etPtr = ViewBindings.findChildViewById(rootView, id);
      if (etPtr == null) {
        break missingId;
      }

      id = R.id.etTime;
      EditText etTime = ViewBindings.findChildViewById(rootView, id);
      if (etTime == null) {
        break missingId;
      }

      id = R.id.layout12;
      LinearLayout layout12 = ViewBindings.findChildViewById(rootView, id);
      if (layout12 == null) {
        break missingId;
      }

      id = R.id.layout4;
      LinearLayout layout4 = ViewBindings.findChildViewById(rootView, id);
      if (layout4 == null) {
        break missingId;
      }

      id = R.id.layout_filter;
      LinearLayout layoutFilter = ViewBindings.findChildViewById(rootView, id);
      if (layoutFilter == null) {
        break missingId;
      }

      id = R.id.llContinuous;
      LinearLayout llContinuous = ViewBindings.findChildViewById(rootView, id);
      if (llContinuous == null) {
        break missingId;
      }

      id = R.id.rbEPC;
      RadioButton rbEPC = ViewBindings.findChildViewById(rootView, id);
      if (rbEPC == null) {
        break missingId;
      }

      id = R.id.rbRESERVED;
      RadioButton rbRESERVED = ViewBindings.findChildViewById(rootView, id);
      if (rbRESERVED == null) {
        break missingId;
      }

      id = R.id.rbTID;
      RadioButton rbTID = ViewBindings.findChildViewById(rootView, id);
      if (rbTID == null) {
        break missingId;
      }

      id = R.id.rbUser;
      RadioButton rbUser = ViewBindings.findChildViewById(rootView, id);
      if (rbUser == null) {
        break missingId;
      }

      id = R.id.tvContinuous;
      TextView tvContinuous = ViewBindings.findChildViewById(rootView, id);
      if (tvContinuous == null) {
        break missingId;
      }

      id = R.id.tv_count;
      TextView tvCount = ViewBindings.findChildViewById(rootView, id);
      if (tvCount == null) {
        break missingId;
      }

      id = R.id.tvMS;
      TextView tvMS = ViewBindings.findChildViewById(rootView, id);
      if (tvMS == null) {
        break missingId;
      }

      id = R.id.tvTime;
      TextView tvTime = ViewBindings.findChildViewById(rootView, id);
      if (tvTime == null) {
        break missingId;
      }

      id = R.id.tv_total;
      TextView tvTotal = ViewBindings.findChildViewById(rootView, id);
      if (tvTotal == null) {
        break missingId;
      }

      return new UhfReadtagFragmentBinding((LinearLayout) rootView, BtClear, BtInventory, BtUpload,
          LvTags, RbInventoryLoop, RbInventorySingle, RgInventory, btSet, btnDisable, btnOk,
          cbEPCTam, cbFilter, cbFilter1, cbPhase, etData, etLen, etPtr, etTime, layout12, layout4,
          layoutFilter, llContinuous, rbEPC, rbRESERVED, rbTID, rbUser, tvContinuous, tvCount, tvMS,
          tvTime, tvTotal);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
