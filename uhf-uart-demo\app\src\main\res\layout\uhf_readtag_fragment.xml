<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:orientation="vertical">

    <RadioGroup
        android:id="@+id/RgInventory"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="5dp">

        <CheckBox
            android:id="@+id/cbFilter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5dp"
            android:text="@string/uhf_title_filter" />

        <RadioButton
            android:id="@+id/RbInventorySingle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_weight="1"
            android:text="@string/rbInventorySingle"
            android:textColor="@drawable/check_text_color2"
            android:textSize="15sp" />

        <RadioButton
            android:id="@+id/RbInventoryLoop"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:checked="true"
            android:text="@string/rbInventoryLoop"
            android:textColor="@drawable/check_text_color2"
            android:textSize="15sp" />
    </RadioGroup>

    <LinearLayout
        android:id="@+id/layout_filter"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/rectangle_bg"
        android:orientation="vertical"
        android:padding="8dp">

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tvPtr" />

            <EditText
                android:id="@+id/etPtr"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:inputType="numberSigned"
                android:text="32" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="5dp"
                android:text="(bit)" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="长度" />

            <EditText
                android:id="@+id/etLen"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:inputType="numberSigned"
                android:text="0" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="(bit)" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tvData_Read" />

            <EditText
                android:id="@+id/etData"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:hint=""
                android:inputType="textNoSuggestions"
                android:singleLine="true" />
        </LinearLayout>

        <RadioGroup
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <RadioButton
                android:id="@+id/rbEPC"
                android:layout_width="0dp"
                android:layout_height="30dp"
                android:layout_weight="1"
                android:background="@drawable/rb_bg"
                android:button="@null"
                android:checked="true"
                android:gravity="center"
                android:text="EPC"
                android:textColor="@drawable/check_text_color" />

            <RadioButton
                android:id="@+id/rbTID"
                android:layout_width="0dp"
                android:layout_height="30dp"
                android:layout_marginLeft="10dp"
                android:layout_weight="1"
                android:background="@drawable/rb_bg"
                android:button="@null"
                android:checked="false"
                android:gravity="center"
                android:text="TID"
                android:textColor="@drawable/check_text_color" />

            <RadioButton
                android:id="@+id/rbUser"
                android:layout_width="0dp"
                android:layout_height="30dp"
                android:layout_marginLeft="10dp"
                android:layout_weight="1"
                android:background="@drawable/rb_bg"
                android:button="@null"
                android:checked="false"
                android:gravity="center"
                android:text="USER"
                android:textColor="@drawable/check_text_color" />

            <RadioButton
                android:id="@+id/rbRESERVED"
                android:layout_width="0dp"
                android:layout_height="30dp"
                android:layout_marginLeft="10dp"
                android:layout_weight="1"
                android:background="@drawable/rb_bg"
                android:button="@null"
                android:checked="false"
                android:gravity="center"
                android:text="RESERVED"
                android:textColor="@drawable/check_text_color"
                android:visibility="gone" />
        </RadioGroup>

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:gravity="right"
            android:paddingTop="10dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/uhf_msg_r2000_tip" />

            <Button
                android:id="@+id/btnDisable"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:background="@drawable/button_bg"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:text="@string/button_disable"
                android:textColor="@color/white"
                android:textSize="15sp"
                android:visibility="gone" />

            <Button
                android:id="@+id/btnOk"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/button_bg"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:text="@string/button_enable"
                android:textColor="@color/white"
                android:textSize="15sp"
                android:visibility="gone" />

            <CheckBox
                android:id="@+id/cb_filter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="10dp"
                android:text="@string/button_enable"
                android:textColor="@drawable/check_text_color"
                android:textSize="19sp"
                android:visibility="gone" />

            <Button
                android:id="@+id/btSet"
                android:layout_width="150dp"
                android:layout_height="wrap_content"
                android:text="设置" />
        </LinearLayout>
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="@color/gray1" />

    <LinearLayout
        android:id="@+id/layout12"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="5dp">

        <CheckBox
            android:id="@+id/cbPhase"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/tv_phase"
            android:textSize="16sp" />

        <Button
            android:id="@+id/BtInventory"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="10dp"
            android:layout_weight="1"
            android:background="@drawable/button_bg"
            android:text="@string/btInventory"
            android:textColor="@color/white" />

        <Button
            android:id="@+id/BtClear"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="5dp"
            android:background="@drawable/button_bg"
            android:text="@string/btClear"
            android:textColor="@color/white"
            android:textSize="15sp" />
    </LinearLayout>

    <!--
    <LinearLayout
        android:id="@+id/layout0"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:padding="5dp" >

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/tvCountOfTags"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_count"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:text="0"
            android:textSize="15sp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/BtClear"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/btClear"
            android:textColor="@color/white"
            android:textSize="15sp"
            android:background="@drawable/button_bg"
            android:layout_marginRight="5dp" />
    </LinearLayout>
    -->

    <LinearLayout
        android:id="@+id/llContinuous"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingLeft="4dp"
        android:paddingRight="4dp">

        <TextView
            android:id="@+id/tvContinuous"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/workTime"
            android:visibility="visible" />

        <EditText
            android:id="@+id/etTime"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:hint="36000000"
            android:inputType="numberDecimal"
            android:maxLength="6"
            android:singleLine="true"
            android:visibility="visible" />

        <TextView
            android:id="@+id/tvMS"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="20dp"
            android:text="@string/second"
            android:visibility="visible" />


        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingStart="10dp"
            android:paddingEnd="6dp"
            android:text="Time:" />

        <TextView
            android:id="@+id/tvTime"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:paddingLeft="10dp"
            android:text="@string/time_d_s" />

        <CheckBox
            android:id="@+id/cbEPC_Tam"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_marginRight="12dp"
            android:text="EPC+TamperAlarm"
            android:visibility="gone" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="5dp"
        android:background="@color/gray1" />


    <HorizontalScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/layout4"
                android:layout_width="410dp"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingHorizontal="5dp"
                android:paddingVertical="5dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="5"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="TAG"
                        android:textSize="15sp" />

                    <TextView
                        android:id="@+id/tv_count"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:layout_weight="1"
                        android:text="0"
                        android:textColor="@color/red1"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/tv_all"
                        android:textSize="15sp" />

                    <TextView
                        android:id="@+id/tv_total"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="0"
                        android:textColor="@color/red1"
                        android:textSize="16sp"
                        android:textStyle="bold" />
                </LinearLayout>

                <TextView
                    android:layout_width="0dip"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/tvTagCount"
                    android:textSize="15sp" />

                <TextView
                    android:layout_width="0dip"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="RSSI"
                    android:textSize="15sp" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/tv_phase"
                    android:textSize="15sp" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:background="@color/gray1" />

            <ListView
                android:id="@+id/LvTags"
                android:layout_width="fill_parent"
                android:layout_height="fill_parent"
                android:paddingHorizontal="5dp" />

        </LinearLayout>
    </HorizontalScrollView>
</LinearLayout>