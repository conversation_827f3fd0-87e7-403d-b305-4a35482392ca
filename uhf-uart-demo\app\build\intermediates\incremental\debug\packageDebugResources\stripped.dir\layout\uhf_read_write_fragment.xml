<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:scrollbars="vertical">

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#AAAAAA"
            android:padding="4dp"
            android:text="@string/uhf_title_filter"
            android:textSize="16sp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/rectangle_bg"
            android:orientation="vertical"
            android:padding="8dp">

            <CheckBox
                android:id="@+id/cb_filter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/button_enable"
                android:textColor="@drawable/check_text_color"
                android:textSize="19sp" />

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/tvPtr" />

                <EditText
                    android:id="@+id/etPtr_filter"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:inputType="numberSigned"
                    android:text="32" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="30dp"
                    android:text="(bit)" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="长度:" />

                <EditText
                    android:id="@+id/etLen_filter"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:inputType="numberSigned"
                    android:text="0" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="(bit)" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/tvData_Read" />

                <EditText
                    android:id="@+id/etData_filter"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:hint=""
                    android:inputType="textNoSuggestions"
                    android:singleLine="true" />
            </LinearLayout>

            <RadioGroup
                android:layout_width="match_parent"
                android:layout_height="wrap_content"

                android:orientation="horizontal">

                <RadioButton
                    android:id="@+id/rbEPC_filter"
                    android:layout_width="0dp"
                    android:layout_height="30dp"
                    android:layout_weight="1"
                    android:background="@drawable/rb_bg"
                    android:button="@null"
                    android:checked="true"
                    android:gravity="center"
                    android:text="EPC"
                    android:textColor="@drawable/check_text_color" />

                <RadioButton
                    android:id="@+id/rbTID_filter"
                    android:layout_width="0dp"
                    android:layout_height="30dp"
                    android:layout_marginLeft="10dp"
                    android:layout_weight="1"
                    android:background="@drawable/rb_bg"
                    android:button="@null"
                    android:checked="false"
                    android:gravity="center"
                    android:text="TID"
                    android:textColor="@drawable/check_text_color" />

                <RadioButton
                    android:id="@+id/rbUser_filter"
                    android:layout_width="0dp"
                    android:layout_height="30dp"
                    android:layout_marginLeft="10dp"
                    android:layout_weight="1"
                    android:background="@drawable/rb_bg"
                    android:button="@null"
                    android:checked="false"
                    android:gravity="center"
                    android:text="USER"
                    android:textColor="@drawable/check_text_color" />

            </RadioGroup>


        </LinearLayout>

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:padding="5dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tvBank"
                android:textSize="16sp" />

            <Spinner
                android:id="@+id/SpinnerBank"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:entries="@array/arrayBank" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:padding="5dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tvPtr"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/EtPtr"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:inputType="numberSigned"
                android:text="0"
                android:textColor="@color/red1"
                android:textSize="15sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="30dp"
                android:text="(word)" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tvLen"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/EtLen"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:inputType="numberSigned"
                android:text="4"
                android:textColor="@color/red1"
                android:textSize="15sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="(word)" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:padding="5dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tvAccessPwd"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/EtAccessPwd"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:hint="00000000" />
        </LinearLayout>


        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:padding="5dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tvData_Read"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/EtData"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:hint=""
                android:inputType="textMultiLine"
                android:singleLine="false" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="10dp"
            android:layout_marginTop="10dp">

            <Button
                android:id="@+id/BtWrite"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/button_bg"
                android:text="@string/btWrite"
                android:textColor="@color/white" />

            <Button
                android:id="@+id/BtRead"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_weight="1"
                android:background="@drawable/button_bg"
                android:text="@string/btRead"
                android:textColor="@color/white" />

        </LinearLayout>
    </LinearLayout>

</ScrollView>
