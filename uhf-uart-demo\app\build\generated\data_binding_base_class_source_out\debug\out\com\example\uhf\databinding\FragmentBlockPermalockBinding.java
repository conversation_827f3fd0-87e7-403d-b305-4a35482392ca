// Generated by view binder compiler. Do not edit!
package com.example.uhf.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.RadioButton;
import android.widget.Spinner;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.uhf.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentBlockPermalockBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final EditText EtAccessPwd;

  @NonNull
  public final EditText EtPtr;

  @NonNull
  public final EditText EtRange;

  @NonNull
  public final Spinner SpinnerBank;

  @NonNull
  public final Spinner SpinnerReadLock;

  @NonNull
  public final Button btnOK;

  @NonNull
  public final CheckBox cbBlock1;

  @NonNull
  public final CheckBox cbBlock10;

  @NonNull
  public final CheckBox cbBlock11;

  @NonNull
  public final CheckBox cbBlock12;

  @NonNull
  public final CheckBox cbBlock13;

  @NonNull
  public final CheckBox cbBlock14;

  @NonNull
  public final CheckBox cbBlock15;

  @NonNull
  public final CheckBox cbBlock16;

  @NonNull
  public final CheckBox cbBlock2;

  @NonNull
  public final CheckBox cbBlock3;

  @NonNull
  public final CheckBox cbBlock4;

  @NonNull
  public final CheckBox cbBlock5;

  @NonNull
  public final CheckBox cbBlock6;

  @NonNull
  public final CheckBox cbBlock7;

  @NonNull
  public final CheckBox cbBlock8;

  @NonNull
  public final CheckBox cbBlock9;

  @NonNull
  public final CheckBox cbFilter2;

  @NonNull
  public final EditText etDataFilterPerm;

  @NonNull
  public final EditText etLenFilterPerm;

  @NonNull
  public final EditText etPtrFilterPerm;

  @NonNull
  public final TextView maskbuf;

  @NonNull
  public final RadioButton rbEPCFilterPerm;

  @NonNull
  public final RadioButton rbTIDFilterPerm;

  @NonNull
  public final RadioButton rbUserFilterPerm;

  private FragmentBlockPermalockBinding(@NonNull FrameLayout rootView,
      @NonNull EditText EtAccessPwd, @NonNull EditText EtPtr, @NonNull EditText EtRange,
      @NonNull Spinner SpinnerBank, @NonNull Spinner SpinnerReadLock, @NonNull Button btnOK,
      @NonNull CheckBox cbBlock1, @NonNull CheckBox cbBlock10, @NonNull CheckBox cbBlock11,
      @NonNull CheckBox cbBlock12, @NonNull CheckBox cbBlock13, @NonNull CheckBox cbBlock14,
      @NonNull CheckBox cbBlock15, @NonNull CheckBox cbBlock16, @NonNull CheckBox cbBlock2,
      @NonNull CheckBox cbBlock3, @NonNull CheckBox cbBlock4, @NonNull CheckBox cbBlock5,
      @NonNull CheckBox cbBlock6, @NonNull CheckBox cbBlock7, @NonNull CheckBox cbBlock8,
      @NonNull CheckBox cbBlock9, @NonNull CheckBox cbFilter2, @NonNull EditText etDataFilterPerm,
      @NonNull EditText etLenFilterPerm, @NonNull EditText etPtrFilterPerm,
      @NonNull TextView maskbuf, @NonNull RadioButton rbEPCFilterPerm,
      @NonNull RadioButton rbTIDFilterPerm, @NonNull RadioButton rbUserFilterPerm) {
    this.rootView = rootView;
    this.EtAccessPwd = EtAccessPwd;
    this.EtPtr = EtPtr;
    this.EtRange = EtRange;
    this.SpinnerBank = SpinnerBank;
    this.SpinnerReadLock = SpinnerReadLock;
    this.btnOK = btnOK;
    this.cbBlock1 = cbBlock1;
    this.cbBlock10 = cbBlock10;
    this.cbBlock11 = cbBlock11;
    this.cbBlock12 = cbBlock12;
    this.cbBlock13 = cbBlock13;
    this.cbBlock14 = cbBlock14;
    this.cbBlock15 = cbBlock15;
    this.cbBlock16 = cbBlock16;
    this.cbBlock2 = cbBlock2;
    this.cbBlock3 = cbBlock3;
    this.cbBlock4 = cbBlock4;
    this.cbBlock5 = cbBlock5;
    this.cbBlock6 = cbBlock6;
    this.cbBlock7 = cbBlock7;
    this.cbBlock8 = cbBlock8;
    this.cbBlock9 = cbBlock9;
    this.cbFilter2 = cbFilter2;
    this.etDataFilterPerm = etDataFilterPerm;
    this.etLenFilterPerm = etLenFilterPerm;
    this.etPtrFilterPerm = etPtrFilterPerm;
    this.maskbuf = maskbuf;
    this.rbEPCFilterPerm = rbEPCFilterPerm;
    this.rbTIDFilterPerm = rbTIDFilterPerm;
    this.rbUserFilterPerm = rbUserFilterPerm;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentBlockPermalockBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentBlockPermalockBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_block_permalock, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentBlockPermalockBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.EtAccessPwd;
      EditText EtAccessPwd = ViewBindings.findChildViewById(rootView, id);
      if (EtAccessPwd == null) {
        break missingId;
      }

      id = R.id.EtPtr;
      EditText EtPtr = ViewBindings.findChildViewById(rootView, id);
      if (EtPtr == null) {
        break missingId;
      }

      id = R.id.EtRange;
      EditText EtRange = ViewBindings.findChildViewById(rootView, id);
      if (EtRange == null) {
        break missingId;
      }

      id = R.id.SpinnerBank;
      Spinner SpinnerBank = ViewBindings.findChildViewById(rootView, id);
      if (SpinnerBank == null) {
        break missingId;
      }

      id = R.id.SpinnerReadLock;
      Spinner SpinnerReadLock = ViewBindings.findChildViewById(rootView, id);
      if (SpinnerReadLock == null) {
        break missingId;
      }

      id = R.id.btnOK;
      Button btnOK = ViewBindings.findChildViewById(rootView, id);
      if (btnOK == null) {
        break missingId;
      }

      id = R.id.cbBlock1;
      CheckBox cbBlock1 = ViewBindings.findChildViewById(rootView, id);
      if (cbBlock1 == null) {
        break missingId;
      }

      id = R.id.cbBlock10;
      CheckBox cbBlock10 = ViewBindings.findChildViewById(rootView, id);
      if (cbBlock10 == null) {
        break missingId;
      }

      id = R.id.cbBlock11;
      CheckBox cbBlock11 = ViewBindings.findChildViewById(rootView, id);
      if (cbBlock11 == null) {
        break missingId;
      }

      id = R.id.cbBlock12;
      CheckBox cbBlock12 = ViewBindings.findChildViewById(rootView, id);
      if (cbBlock12 == null) {
        break missingId;
      }

      id = R.id.cbBlock13;
      CheckBox cbBlock13 = ViewBindings.findChildViewById(rootView, id);
      if (cbBlock13 == null) {
        break missingId;
      }

      id = R.id.cbBlock14;
      CheckBox cbBlock14 = ViewBindings.findChildViewById(rootView, id);
      if (cbBlock14 == null) {
        break missingId;
      }

      id = R.id.cbBlock15;
      CheckBox cbBlock15 = ViewBindings.findChildViewById(rootView, id);
      if (cbBlock15 == null) {
        break missingId;
      }

      id = R.id.cbBlock16;
      CheckBox cbBlock16 = ViewBindings.findChildViewById(rootView, id);
      if (cbBlock16 == null) {
        break missingId;
      }

      id = R.id.cbBlock2;
      CheckBox cbBlock2 = ViewBindings.findChildViewById(rootView, id);
      if (cbBlock2 == null) {
        break missingId;
      }

      id = R.id.cbBlock3;
      CheckBox cbBlock3 = ViewBindings.findChildViewById(rootView, id);
      if (cbBlock3 == null) {
        break missingId;
      }

      id = R.id.cbBlock4;
      CheckBox cbBlock4 = ViewBindings.findChildViewById(rootView, id);
      if (cbBlock4 == null) {
        break missingId;
      }

      id = R.id.cbBlock5;
      CheckBox cbBlock5 = ViewBindings.findChildViewById(rootView, id);
      if (cbBlock5 == null) {
        break missingId;
      }

      id = R.id.cbBlock6;
      CheckBox cbBlock6 = ViewBindings.findChildViewById(rootView, id);
      if (cbBlock6 == null) {
        break missingId;
      }

      id = R.id.cbBlock7;
      CheckBox cbBlock7 = ViewBindings.findChildViewById(rootView, id);
      if (cbBlock7 == null) {
        break missingId;
      }

      id = R.id.cbBlock8;
      CheckBox cbBlock8 = ViewBindings.findChildViewById(rootView, id);
      if (cbBlock8 == null) {
        break missingId;
      }

      id = R.id.cbBlock9;
      CheckBox cbBlock9 = ViewBindings.findChildViewById(rootView, id);
      if (cbBlock9 == null) {
        break missingId;
      }

      id = R.id.cb_filter_2;
      CheckBox cbFilter2 = ViewBindings.findChildViewById(rootView, id);
      if (cbFilter2 == null) {
        break missingId;
      }

      id = R.id.etData_filter_perm;
      EditText etDataFilterPerm = ViewBindings.findChildViewById(rootView, id);
      if (etDataFilterPerm == null) {
        break missingId;
      }

      id = R.id.etLen_filter_perm;
      EditText etLenFilterPerm = ViewBindings.findChildViewById(rootView, id);
      if (etLenFilterPerm == null) {
        break missingId;
      }

      id = R.id.etPtr_filter_perm;
      EditText etPtrFilterPerm = ViewBindings.findChildViewById(rootView, id);
      if (etPtrFilterPerm == null) {
        break missingId;
      }

      id = R.id.maskbuf;
      TextView maskbuf = ViewBindings.findChildViewById(rootView, id);
      if (maskbuf == null) {
        break missingId;
      }

      id = R.id.rbEPC_filter_perm;
      RadioButton rbEPCFilterPerm = ViewBindings.findChildViewById(rootView, id);
      if (rbEPCFilterPerm == null) {
        break missingId;
      }

      id = R.id.rbTID_filter_perm;
      RadioButton rbTIDFilterPerm = ViewBindings.findChildViewById(rootView, id);
      if (rbTIDFilterPerm == null) {
        break missingId;
      }

      id = R.id.rbUser_filter_perm;
      RadioButton rbUserFilterPerm = ViewBindings.findChildViewById(rootView, id);
      if (rbUserFilterPerm == null) {
        break missingId;
      }

      return new FragmentBlockPermalockBinding((FrameLayout) rootView, EtAccessPwd, EtPtr, EtRange,
          SpinnerBank, SpinnerReadLock, btnOK, cbBlock1, cbBlock10, cbBlock11, cbBlock12, cbBlock13,
          cbBlock14, cbBlock15, cbBlock16, cbBlock2, cbBlock3, cbBlock4, cbBlock5, cbBlock6,
          cbBlock7, cbBlock8, cbBlock9, cbFilter2, etDataFilterPerm, etLenFilterPerm,
          etPtrFilterPerm, maskbuf, rbEPCFilterPerm, rbTIDFilterPerm, rbUserFilterPerm);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
