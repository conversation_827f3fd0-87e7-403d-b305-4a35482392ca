plugins {
    id 'com.android.application'
}

android {
    namespace 'com.maspects.uhftool'
    compileSdk 34

    defaultConfig {
        applicationId "com.maspects.uhftool"
        minSdk 26
        targetSdk 33
        versionCode 2
        versionName "1.4.5"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    signingConfigs {
        debug {
            File strFile = new File("/uhf-serial_release.jks")
            storeFile file(strFile)
            storePassword "123456"
            keyPassword "123456"
            keyAlias "uhf-serial-key"
        }
        release {
            File strFile = new File("/uhf-serial_release.jks")
            storeFile file(strFile)
            storePassword "123456"
            keyPassword "123456"
            keyAlias "uhf-serial-key"
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }

        debug {
            signingConfig signingConfigs.debug
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }

        // 自定义apk名称
        android.applicationVariants.all { variant ->
            variant.outputs.all {
                outputFileName = "Maspects-UHF-Tool_v${defaultConfig.versionName}.apk"
            }
        }
    }

    buildFeatures {
        viewBinding false
        dataBinding false
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.*'])
    implementation files('libs/jxl.jar')

    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'

    // Network dependencies for upload service
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
    implementation 'com.google.code.gson:gson:2.10.1'

    // Work Manager for background uploads
    implementation 'androidx.work:work-runtime:2.9.0'

    // Preferences for configuration
    implementation 'androidx.preference:preference:1.2.1'

    testImplementation 'junit:junit:4.13.2'
    testImplementation 'org.mockito:mockito-core:4.11.0'
    testImplementation 'org.robolectric:robolectric:4.10.3'
    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'
}