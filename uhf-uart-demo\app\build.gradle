plugins {
    id 'com.android.application'
}

android {
    namespace 'com.example.uhf'
    compileSdk 34

    defaultConfig {
        applicationId "com.example.uhf"
        minSdk 26
        targetSdk 33
        versionCode 2
        versionName "1.4.5"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    signingConfigs {
        debug {
            File strFile = new File("/uhf-serial_release.jks")
            storeFile file(strFile)
            storePassword "123456"
            keyPassword "123456"
            keyAlias "uhf-serial-key"
        }
        release {
            File strFile = new File("/uhf-serial_release.jks")
            storeFile file(strFile)
            storePassword "123456"
            keyPassword "123456"
            keyAlias "uhf-serial-key"
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }

        debug {
            signingConfig signingConfigs.debug
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }

        // 自定义apk名称
        android.applicationVariants.all { variant ->
            variant.outputs.all {
                outputFileName = "UHF-serial_v${defaultConfig.versionName}.apk"
            }
        }
    }

    buildFeatures {
        viewBinding true
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.*'])
    implementation files('libs/jxl.jar')

    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'
}