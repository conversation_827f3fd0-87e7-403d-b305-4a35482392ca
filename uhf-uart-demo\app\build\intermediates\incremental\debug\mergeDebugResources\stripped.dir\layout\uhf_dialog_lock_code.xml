<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="left"
    android:orientation="vertical"
    android:padding="10dp">

    <RadioGroup
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/rgFileType"
        android:gravity="left"
        android:orientation="horizontal">

        <RadioButton
            android:id="@+id/rbOpen"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="true"
            android:text="@string/uhf_title_open" />
        <RadioButton
            android:id="@+id/rbLock"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/uhf_title_lock" />
        <CheckBox
            android:id="@+id/cbPerm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/uhf_title_perm" />
    </RadioGroup>


    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">


        <CheckBox
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/uhf_title_kill"
            android:id="@+id/cbKill" />
        <CheckBox
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/uhf_title_access"
            android:id="@+id/cbAccess" />
        <CheckBox
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="EPC:"
            android:id="@+id/cbEPC" />
        <CheckBox
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/uhf_title_tid"
            android:id="@+id/cbTid" />
        <CheckBox
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/uhf_title_user"
            android:id="@+id/cbUser" />
    </LinearLayout>

</LinearLayout>
