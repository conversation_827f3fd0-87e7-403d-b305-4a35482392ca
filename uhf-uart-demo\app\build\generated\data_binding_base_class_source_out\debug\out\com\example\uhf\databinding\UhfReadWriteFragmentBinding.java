// Generated by view binder compiler. Do not edit!
package com.example.uhf.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.RadioButton;
import android.widget.ScrollView;
import android.widget.Spinner;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.uhf.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class UhfReadWriteFragmentBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button BtRead;

  @NonNull
  public final Button BtWrite;

  @NonNull
  public final EditText EtAccessPwd;

  @NonNull
  public final EditText EtData;

  @NonNull
  public final EditText EtLen;

  @NonNull
  public final EditText EtPtr;

  @NonNull
  public final Spinner SpinnerBank;

  @NonNull
  public final CheckBox cbFilter;

  @NonNull
  public final EditText etDataFilter;

  @NonNull
  public final EditText etLenFilter;

  @NonNull
  public final EditText etPtrFilter;

  @NonNull
  public final RadioButton rbEPCFilter;

  @NonNull
  public final RadioButton rbTIDFilter;

  @NonNull
  public final RadioButton rbUserFilter;

  private UhfReadWriteFragmentBinding(@NonNull ScrollView rootView, @NonNull Button BtRead,
      @NonNull Button BtWrite, @NonNull EditText EtAccessPwd, @NonNull EditText EtData,
      @NonNull EditText EtLen, @NonNull EditText EtPtr, @NonNull Spinner SpinnerBank,
      @NonNull CheckBox cbFilter, @NonNull EditText etDataFilter, @NonNull EditText etLenFilter,
      @NonNull EditText etPtrFilter, @NonNull RadioButton rbEPCFilter,
      @NonNull RadioButton rbTIDFilter, @NonNull RadioButton rbUserFilter) {
    this.rootView = rootView;
    this.BtRead = BtRead;
    this.BtWrite = BtWrite;
    this.EtAccessPwd = EtAccessPwd;
    this.EtData = EtData;
    this.EtLen = EtLen;
    this.EtPtr = EtPtr;
    this.SpinnerBank = SpinnerBank;
    this.cbFilter = cbFilter;
    this.etDataFilter = etDataFilter;
    this.etLenFilter = etLenFilter;
    this.etPtrFilter = etPtrFilter;
    this.rbEPCFilter = rbEPCFilter;
    this.rbTIDFilter = rbTIDFilter;
    this.rbUserFilter = rbUserFilter;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static UhfReadWriteFragmentBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static UhfReadWriteFragmentBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.uhf_read_write_fragment, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static UhfReadWriteFragmentBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.BtRead;
      Button BtRead = ViewBindings.findChildViewById(rootView, id);
      if (BtRead == null) {
        break missingId;
      }

      id = R.id.BtWrite;
      Button BtWrite = ViewBindings.findChildViewById(rootView, id);
      if (BtWrite == null) {
        break missingId;
      }

      id = R.id.EtAccessPwd;
      EditText EtAccessPwd = ViewBindings.findChildViewById(rootView, id);
      if (EtAccessPwd == null) {
        break missingId;
      }

      id = R.id.EtData;
      EditText EtData = ViewBindings.findChildViewById(rootView, id);
      if (EtData == null) {
        break missingId;
      }

      id = R.id.EtLen;
      EditText EtLen = ViewBindings.findChildViewById(rootView, id);
      if (EtLen == null) {
        break missingId;
      }

      id = R.id.EtPtr;
      EditText EtPtr = ViewBindings.findChildViewById(rootView, id);
      if (EtPtr == null) {
        break missingId;
      }

      id = R.id.SpinnerBank;
      Spinner SpinnerBank = ViewBindings.findChildViewById(rootView, id);
      if (SpinnerBank == null) {
        break missingId;
      }

      id = R.id.cb_filter;
      CheckBox cbFilter = ViewBindings.findChildViewById(rootView, id);
      if (cbFilter == null) {
        break missingId;
      }

      id = R.id.etData_filter;
      EditText etDataFilter = ViewBindings.findChildViewById(rootView, id);
      if (etDataFilter == null) {
        break missingId;
      }

      id = R.id.etLen_filter;
      EditText etLenFilter = ViewBindings.findChildViewById(rootView, id);
      if (etLenFilter == null) {
        break missingId;
      }

      id = R.id.etPtr_filter;
      EditText etPtrFilter = ViewBindings.findChildViewById(rootView, id);
      if (etPtrFilter == null) {
        break missingId;
      }

      id = R.id.rbEPC_filter;
      RadioButton rbEPCFilter = ViewBindings.findChildViewById(rootView, id);
      if (rbEPCFilter == null) {
        break missingId;
      }

      id = R.id.rbTID_filter;
      RadioButton rbTIDFilter = ViewBindings.findChildViewById(rootView, id);
      if (rbTIDFilter == null) {
        break missingId;
      }

      id = R.id.rbUser_filter;
      RadioButton rbUserFilter = ViewBindings.findChildViewById(rootView, id);
      if (rbUserFilter == null) {
        break missingId;
      }

      return new UhfReadWriteFragmentBinding((ScrollView) rootView, BtRead, BtWrite, EtAccessPwd,
          EtData, EtLen, EtPtr, SpinnerBank, cbFilter, etDataFilter, etLenFilter, etPtrFilter,
          rbEPCFilter, rbTIDFilter, rbUserFilter);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
