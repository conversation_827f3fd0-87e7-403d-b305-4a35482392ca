// Generated by view binder compiler. Do not edit!
package com.example.uhf.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.uhf.R;
import com.example.uhf.view.CircleSeekBar;
import com.example.uhf.view.RadarView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentUhfRadarLocationBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout-land/</li>
   * </ul>
   */
  @Nullable
  public final LinearLayout LlSearchRange;

  @NonNull
  public final Button btRadarStart;

  @NonNull
  public final Button btRadarStop;

  @NonNull
  public final LinearLayout epcLayout;

  @NonNull
  public final EditText etRadarEPC;

  @NonNull
  public final LinearLayout llButton;

  @NonNull
  public final RadarView radarView;

  @NonNull
  public final CircleSeekBar seekBarPower;

  @NonNull
  public final TextView tvSearchRange;

  private FragmentUhfRadarLocationBinding(@NonNull ConstraintLayout rootView,
      @Nullable LinearLayout LlSearchRange, @NonNull Button btRadarStart,
      @NonNull Button btRadarStop, @NonNull LinearLayout epcLayout, @NonNull EditText etRadarEPC,
      @NonNull LinearLayout llButton, @NonNull RadarView radarView,
      @NonNull CircleSeekBar seekBarPower, @NonNull TextView tvSearchRange) {
    this.rootView = rootView;
    this.LlSearchRange = LlSearchRange;
    this.btRadarStart = btRadarStart;
    this.btRadarStop = btRadarStop;
    this.epcLayout = epcLayout;
    this.etRadarEPC = etRadarEPC;
    this.llButton = llButton;
    this.radarView = radarView;
    this.seekBarPower = seekBarPower;
    this.tvSearchRange = tvSearchRange;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentUhfRadarLocationBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentUhfRadarLocationBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_uhf_radar_location, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentUhfRadarLocationBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id._llSearchRange;
      LinearLayout LlSearchRange = ViewBindings.findChildViewById(rootView, id);

      id = R.id.btRadarStart;
      Button btRadarStart = ViewBindings.findChildViewById(rootView, id);
      if (btRadarStart == null) {
        break missingId;
      }

      id = R.id.btRadarStop;
      Button btRadarStop = ViewBindings.findChildViewById(rootView, id);
      if (btRadarStop == null) {
        break missingId;
      }

      id = R.id.epcLayout;
      LinearLayout epcLayout = ViewBindings.findChildViewById(rootView, id);
      if (epcLayout == null) {
        break missingId;
      }

      id = R.id.etRadarEPC;
      EditText etRadarEPC = ViewBindings.findChildViewById(rootView, id);
      if (etRadarEPC == null) {
        break missingId;
      }

      id = R.id.llButton;
      LinearLayout llButton = ViewBindings.findChildViewById(rootView, id);
      if (llButton == null) {
        break missingId;
      }

      id = R.id.radarView;
      RadarView radarView = ViewBindings.findChildViewById(rootView, id);
      if (radarView == null) {
        break missingId;
      }

      id = R.id.seekBarPower;
      CircleSeekBar seekBarPower = ViewBindings.findChildViewById(rootView, id);
      if (seekBarPower == null) {
        break missingId;
      }

      id = R.id.tvSearchRange;
      TextView tvSearchRange = ViewBindings.findChildViewById(rootView, id);
      if (tvSearchRange == null) {
        break missingId;
      }

      return new FragmentUhfRadarLocationBinding((ConstraintLayout) rootView, LlSearchRange,
          btRadarStart, btRadarStop, epcLayout, etRadarEPC, llButton, radarView, seekBarPower,
          tvSearchRange);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
