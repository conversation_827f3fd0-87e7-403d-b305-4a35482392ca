<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Header -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Upload Settings"
            android:textSize="24sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="24dp" />

        <!-- Server Configuration Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Server Configuration"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp" />

        <!-- Server URL -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="Server URL">

            <EditText
                android:id="@+id/etServerUrl"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textUri"
                android:text="https://your-server.com/" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- API Key -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="API Key">

            <EditText
                android:id="@+id/etApiKey"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textPassword" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Upload Endpoint -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="Upload Endpoint (optional)">

            <EditText
                android:id="@+id/etUploadEndpoint"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"
                android:text="api/rfid/upload" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Upload Preferences Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Upload Preferences"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginTop="24dp"
            android:layout_marginBottom="16dp" />

        <!-- Auto Upload Checkbox -->
        <CheckBox
            android:id="@+id/cbAutoUpload"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Auto-upload after scanning"
            android:layout_marginBottom="16dp" />

        <!-- Device Information Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Device Information"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginTop="24dp"
            android:layout_marginBottom="16dp" />

        <!-- Device ID -->
        <TextView
            android:id="@+id/tvDeviceId"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Device ID: Loading..."
            android:textSize="14sp"
            android:layout_marginBottom="8dp" />

        <!-- Connection Status -->
        <TextView
            android:id="@+id/tvConnectionStatus"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Status: Not Configured"
            android:textSize="14sp"
            android:layout_marginBottom="16dp" />

        <!-- Upload Statistics Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Upload Statistics"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="16dp" />

        <!-- Upload Stats -->
        <TextView
            android:id="@+id/tvUploadStats"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Total: 0 | Success: 0 | Failed: 0 | Rate: 0%"
            android:textSize="14sp"
            android:layout_marginBottom="8dp" />

        <!-- Last Upload -->
        <TextView
            android:id="@+id/tvLastUpload"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Last Upload: Never"
            android:textSize="14sp"
            android:layout_marginBottom="24dp" />

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="16dp">

            <Button
                android:id="@+id/btnTestConnection"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Test Connection"
                android:layout_marginEnd="8dp"
                style="@style/Widget.Material3.Button.OutlinedButton" />

            <Button
                android:id="@+id/btnSaveSettings"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Save Settings"
                android:layout_marginStart="8dp" />

        </LinearLayout>

        <!-- Help Text -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Configure your server settings to enable automatic uploading of scanned RFID data. Test the connection to verify your settings before saving."
            android:textSize="12sp"
            android:textColor="@android:color/darker_gray"
            android:layout_marginTop="24dp"
            android:gravity="center" />

    </LinearLayout>

</ScrollView>
