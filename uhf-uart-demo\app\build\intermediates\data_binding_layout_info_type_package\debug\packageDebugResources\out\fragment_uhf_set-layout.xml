<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_uhf_set" modulePackage="com.example.uhf" filePath="app\src\main\res\layout\fragment_uhf_set.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_uhf_set_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="719" endOffset="12"/></Target><Target id="@+id/spFrequency" view="Spinner"><Expressions/><location startLine="45" startOffset="20" endLine="50" endOffset="61"/></Target><Target id="@+id/btnSetFrequency" view="Button"><Expressions/><location startLine="58" startOffset="20" endLine="66" endOffset="49"/></Target><Target id="@+id/btnGetFrequency" view="Button"><Expressions/><location startLine="68" startOffset="20" endLine="77" endOffset="49"/></Target><Target id="@+id/spPower" view="Spinner"><Expressions/><location startLine="102" startOffset="20" endLine="108" endOffset="61"/></Target><Target id="@+id/btnSetPower" view="Button"><Expressions/><location startLine="121" startOffset="20" endLine="129" endOffset="49"/></Target><Target id="@+id/btnGetPower" view="Button"><Expressions/><location startLine="131" startOffset="20" endLine="140" endOffset="49"/></Target><Target id="@+id/ll_freHop" view="LinearLayout"><Expressions/><location startLine="144" startOffset="12" endLine="249" endOffset="26"/></Target><Target id="@+id/rb_china" view="RadioButton"><Expressions/><location startLine="172" startOffset="24" endLine="179" endOffset="55"/></Target><Target id="@+id/rb_Europe" view="RadioButton"><Expressions/><location startLine="181" startOffset="24" endLine="189" endOffset="55"/></Target><Target id="@+id/rb_America" view="RadioButton"><Expressions/><location startLine="191" startOffset="24" endLine="200" endOffset="53"/></Target><Target id="@+id/rb_Others" view="RadioButton"><Expressions/><location startLine="202" startOffset="24" endLine="210" endOffset="53"/></Target><Target id="@+id/spFreHop" view="Spinner"><Expressions/><location startLine="226" startOffset="20" endLine="231" endOffset="61"/></Target><Target id="@+id/btnSetFreHop" view="Button"><Expressions/><location startLine="239" startOffset="20" endLine="247" endOffset="49"/></Target><Target id="@+id/SpinnerAgreement" view="Spinner"><Expressions/><location startLine="383" startOffset="20" endLine="387" endOffset="64"/></Target><Target id="@+id/btnSetProtocol" view="Button"><Expressions/><location startLine="390" startOffset="16" endLine="397" endOffset="45"/></Target><Target id="@+id/splinkParams" view="Spinner"><Expressions/><location startLine="421" startOffset="20" endLine="426" endOffset="60"/></Target><Target id="@+id/btnSetLinkParams" view="Button"><Expressions/><location startLine="434" startOffset="20" endLine="442" endOffset="49"/></Target><Target id="@+id/btnGetLinkParams" view="Button"><Expressions/><location startLine="444" startOffset="20" endLine="453" endOffset="49"/></Target><Target id="@+id/spMemoryBank" view="Spinner"><Expressions/><location startLine="477" startOffset="20" endLine="482" endOffset="66"/></Target><Target id="@+id/llMemoryBankParams" view="LinearLayout"><Expressions/><location startLine="485" startOffset="16" endLine="529" endOffset="30"/></Target><Target id="@+id/etOffset" view="EditText"><Expressions/><location startLine="497" startOffset="20" endLine="503" endOffset="42"/></Target><Target id="@+id/etLength" view="EditText"><Expressions/><location startLine="517" startOffset="20" endLine="523" endOffset="42"/></Target><Target id="@+id/btnSetMemoryBank" view="Button"><Expressions/><location startLine="536" startOffset="20" endLine="544" endOffset="49"/></Target><Target id="@+id/btnGetMemoryBank" view="Button"><Expressions/><location startLine="546" startOffset="20" endLine="555" endOffset="49"/></Target><Target id="@+id/llSession" view="LinearLayout"><Expressions/><location startLine="562" startOffset="12" endLine="591" endOffset="26"/></Target><Target id="@+id/spSessionID" view="Spinner"><Expressions/><location startLine="575" startOffset="16" endLine="579" endOffset="59"/></Target><Target id="@+id/spInventoried" view="Spinner"><Expressions/><location startLine="586" startOffset="16" endLine="590" endOffset="63"/></Target><Target id="@+id/btnSetSession" view="Button"><Expressions/><location startLine="599" startOffset="16" endLine="606" endOffset="54"/></Target><Target id="@+id/btnGetSession" view="Button"><Expressions/><location startLine="608" startOffset="16" endLine="616" endOffset="54"/></Target><Target id="@+id/rbFastInventoryOpen" view="RadioButton"><Expressions/><location startLine="632" startOffset="20" endLine="637" endOffset="60"/></Target><Target id="@+id/rbFastInventoryClose" view="RadioButton"><Expressions/><location startLine="639" startOffset="20" endLine="644" endOffset="61"/></Target><Target id="@+id/btnSetFastInventory" view="Button"><Expressions/><location startLine="653" startOffset="16" endLine="660" endOffset="54"/></Target><Target id="@+id/btnGetFastInventory" view="Button"><Expressions/><location startLine="662" startOffset="16" endLine="670" endOffset="54"/></Target><Target id="@+id/btnFactoryReset" view="Button"><Expressions/><location startLine="673" startOffset="12" endLine="683" endOffset="41"/></Target><Target id="@+id/cbTagFocus" view="CheckBox"><Expressions/><location startLine="694" startOffset="16" endLine="702" endOffset="69"/></Target><Target id="@+id/cbFastID" view="CheckBox"><Expressions/><location startLine="704" startOffset="16" endLine="713" endOffset="69"/></Target></Targets></Layout>