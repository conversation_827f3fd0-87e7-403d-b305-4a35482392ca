<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:radar="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginHorizontal="5dp">


    <LinearLayout
        android:id="@+id/epcLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintEnd_toStartOf="@id/radarView"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="EPC：" />

        <EditText
            android:id="@+id/etRadarEPC"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
    </LinearLayout>


    <com.example.uhf.view.RadarView
        android:id="@+id/radarView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:padding="4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@id/epcLayout"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintBottom_toTopOf="@id/tvSearchRange"
        app:layout_constraintEnd_toEndOf="@id/epcLayout"
        app:layout_constraintStart_toStartOf="@id/epcLayout">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="+"
            android:textSize="20sp"
            android:textStyle="bold" />

        <com.example.uhf.view.CircleSeekBar
            android:id="@+id/seekBarPower"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:max="30"
            android:min="5"
            android:progressDrawable="@drawable/seekbar_bg"
            android:splitTrack="false"
            android:thumb="@drawable/shape_seekbar_circle" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="-"
            android:textSize="20sp"
            android:textStyle="bold" />
    </LinearLayout>

    <TextView
        android:id="@+id/tvSearchRange"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/searchRange"
        android:textColor="@color/blue"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/llButton"
        app:layout_constraintEnd_toEndOf="@id/epcLayout"
        app:layout_constraintStart_toStartOf="@id/epcLayout" />

    <LinearLayout
        android:id="@+id/llButton"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        android:orientation="horizontal"
        android:paddingTop="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/epcLayout"
        app:layout_constraintStart_toStartOf="@id/epcLayout">

        <Button
            android:id="@+id/btRadarStart"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:text="@string/start_location" />

        <Button
            android:id="@+id/btRadarStop"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:text="@string/stop_location" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
