@echo off
echo Verifying Upload Functionality
echo ==============================

REM Set Android SDK paths
set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
set PATH=%PATH%;%ANDROID_HOME%\platform-tools

echo.
echo Step 1: Checking installed apps...
adb shell pm list packages | findstr uhf

echo.
echo Step 2: Checking if app is running...
adb shell am start -n com.maspects.uhftool/.activity.UHFMainActivity
if %ERRORLEVEL% equ 0 (
    echo SUCCESS: App launched!
) else (
    echo Trying alternative package name...
    adb shell am start -n com.example.uhf/.activity.UHFMainActivity
)

echo.
echo Step 3: Monitoring app logs...
echo Press Ctrl+C to stop monitoring
echo.
echo Looking for upload-related logs...
adb logcat | findstr "Upload\|uhftool\|UploadService\|UploadWorker"
