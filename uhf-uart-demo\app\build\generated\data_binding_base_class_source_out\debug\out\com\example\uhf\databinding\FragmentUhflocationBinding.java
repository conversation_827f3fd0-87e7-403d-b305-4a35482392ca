// Generated by view binder compiler. Do not edit!
package com.example.uhf.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.uhf.R;
import com.example.uhf.view.CircleSeekBar;
import com.example.uhf.view.UhfLocationCanvasView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentUhflocationBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final Button btStart;

  @NonNull
  public final Button btStop;

  @NonNull
  public final EditText etEPC;

  @NonNull
  public final UhfLocationCanvasView llChart;

  @NonNull
  public final CircleSeekBar seekBarPower;

  @NonNull
  public final TextView tvSearchRange;

  private FragmentUhflocationBinding(@NonNull FrameLayout rootView, @NonNull Button btStart,
      @NonNull Button btStop, @NonNull EditText etEPC, @NonNull UhfLocationCanvasView llChart,
      @NonNull CircleSeekBar seekBarPower, @NonNull TextView tvSearchRange) {
    this.rootView = rootView;
    this.btStart = btStart;
    this.btStop = btStop;
    this.etEPC = etEPC;
    this.llChart = llChart;
    this.seekBarPower = seekBarPower;
    this.tvSearchRange = tvSearchRange;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentUhflocationBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentUhflocationBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_uhflocation, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentUhflocationBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btStart;
      Button btStart = ViewBindings.findChildViewById(rootView, id);
      if (btStart == null) {
        break missingId;
      }

      id = R.id.btStop;
      Button btStop = ViewBindings.findChildViewById(rootView, id);
      if (btStop == null) {
        break missingId;
      }

      id = R.id.etEPC;
      EditText etEPC = ViewBindings.findChildViewById(rootView, id);
      if (etEPC == null) {
        break missingId;
      }

      id = R.id.llChart;
      UhfLocationCanvasView llChart = ViewBindings.findChildViewById(rootView, id);
      if (llChart == null) {
        break missingId;
      }

      id = R.id.seekBarPower;
      CircleSeekBar seekBarPower = ViewBindings.findChildViewById(rootView, id);
      if (seekBarPower == null) {
        break missingId;
      }

      id = R.id.tvSearchRange;
      TextView tvSearchRange = ViewBindings.findChildViewById(rootView, id);
      if (tvSearchRange == null) {
        break missingId;
      }

      return new FragmentUhflocationBinding((FrameLayout) rootView, btStart, btStop, etEPC, llChart,
          seekBarPower, tvSearchRange);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
