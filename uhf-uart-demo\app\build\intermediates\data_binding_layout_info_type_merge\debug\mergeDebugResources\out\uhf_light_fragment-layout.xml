<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="uhf_light_fragment" modulePackage="com.example.uhf" filePath="app\src\main\res\layout\uhf_light_fragment.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/uhf_light_fragment_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="166" endOffset="12"/></Target><Target id="@+id/cb_light_filter" view="CheckBox"><Expressions/><location startLine="26" startOffset="12" endLine="32" endOffset="41"/></Target><Target id="@+id/etPtr_light_filter" view="EditText"><Expressions/><location startLine="43" startOffset="16" endLine="49" endOffset="39"/></Target><Target id="@+id/etLen_light_filter" view="EditText"><Expressions/><location startLine="62" startOffset="16" endLine="68" endOffset="38"/></Target><Target id="@+id/etData_light_filter" view="EditText"><Expressions/><location startLine="85" startOffset="16" endLine="91" endOffset="47"/></Target><Target id="@+id/rbEPC_light_filter" view="RadioButton"><Expressions/><location startLine="99" startOffset="16" endLine="109" endOffset="68"/></Target><Target id="@+id/rbTID_light_filter" view="RadioButton"><Expressions/><location startLine="111" startOffset="16" endLine="122" endOffset="68"/></Target><Target id="@+id/rbUser_light_filter" view="RadioButton"><Expressions/><location startLine="124" startOffset="16" endLine="135" endOffset="68"/></Target><Target id="@+id/btn_light_single" view="Button"><Expressions/><location startLine="147" startOffset="12" endLine="152" endOffset="57"/></Target><Target id="@+id/btn_light_continuous" view="Button"><Expressions/><location startLine="154" startOffset="12" endLine="160" endOffset="61"/></Target></Targets></Layout>