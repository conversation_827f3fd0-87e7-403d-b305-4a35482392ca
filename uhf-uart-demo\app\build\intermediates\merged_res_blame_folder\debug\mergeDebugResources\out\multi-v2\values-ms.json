{"logs": [{"outputFile": "com.example.uhf.app-mergeDebugResources-2:/values-ms/values-ms.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7e858fb0a02ac4fcc5a44c485ded2814\\transformed\\preference-1.2.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,263,346,485,654,735", "endColumns": "68,88,82,138,168,80,78", "endOffsets": "169,258,341,480,649,730,809"}, "to": {"startLines": "48,51,113,115,121,122,123", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4494,4690,9580,9749,10310,10479,10560", "endColumns": "68,88,82,138,168,80,78", "endOffsets": "4558,4774,9658,9883,10474,10555,10634"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\33498c78bdd4f4128ef0bb8451a88732\\transformed\\core-1.13.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,459,565,683,798", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "145,247,344,454,560,678,793,894"}, "to": {"startLines": "38,39,40,41,42,43,44,120", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3449,3544,3646,3743,3853,3959,4077,10209", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "3539,3641,3738,3848,3954,4072,4187,10305"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8a82ccfd0a18eaa7ab7c918e5f6cc0f6\\transformed\\appcompat-1.7.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,810,888,979,1072,1167,1261,1359,1452,1547,1641,1732,1823,1903,2015,2123,2220,2329,2433,2540,2699,2800", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "211,316,424,511,615,726,805,883,974,1067,1162,1256,1354,1447,1542,1636,1727,1818,1898,2010,2118,2215,2324,2428,2535,2694,2795,2876"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "329,440,545,653,740,844,955,1034,1112,1203,1296,1391,1485,1583,1676,1771,1865,1956,2047,2127,2239,2347,2444,2553,2657,2764,2923,9888", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "435,540,648,735,839,950,1029,1107,1198,1291,1386,1480,1578,1671,1766,1860,1951,2042,2122,2234,2342,2439,2548,2652,2759,2918,3019,9964"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aef17b985e4c925ecf4549f65f319b6b\\transformed\\material-1.12.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,279,359,438,525,617,704,807,923,1006,1068,1133,1226,1291,1350,1437,1499,1561,1621,1687,1749,1803,1911,1968,2029,2084,2155,2275,2366,2443,2540,2625,2711,2859,2945,3031,3159,3247,3325,3378,3429,3495,3566,3644,3715,3794,3867,3943,4016,4087,4194,4286,4359,4449,4542,4616,4687,4778,4830,4910,4978,5062,5147,5209,5273,5336,5408,5512,5620,5716,5822,5879,5934,6020,6105,6183", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,79,78,86,91,86,102,115,82,61,64,92,64,58,86,61,61,59,65,61,53,107,56,60,54,70,119,90,76,96,84,85,147,85,85,127,87,77,52,50,65,70,77,70,78,72,75,72,70,106,91,72,89,92,73,70,90,51,79,67,83,84,61,63,62,71,103,107,95,105,56,54,85,84,77,76", "endOffsets": "274,354,433,520,612,699,802,918,1001,1063,1128,1221,1286,1345,1432,1494,1556,1616,1682,1744,1798,1906,1963,2024,2079,2150,2270,2361,2438,2535,2620,2706,2854,2940,3026,3154,3242,3320,3373,3424,3490,3561,3639,3710,3789,3862,3938,4011,4082,4189,4281,4354,4444,4537,4611,4682,4773,4825,4905,4973,5057,5142,5204,5268,5331,5403,5507,5615,5711,5817,5874,5929,6015,6100,6178,6255"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,49,50,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,114,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3024,3104,3183,3270,3362,4192,4295,4411,4563,4625,4779,4872,4937,4996,5083,5145,5207,5267,5333,5395,5449,5557,5614,5675,5730,5801,5921,6012,6089,6186,6271,6357,6505,6591,6677,6805,6893,6971,7024,7075,7141,7212,7290,7361,7440,7513,7589,7662,7733,7840,7932,8005,8095,8188,8262,8333,8424,8476,8556,8624,8708,8793,8855,8919,8982,9054,9158,9266,9362,9468,9525,9663,9969,10054,10132", "endLines": "5,33,34,35,36,37,45,46,47,49,50,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,114,117,118,119", "endColumns": "12,79,78,86,91,86,102,115,82,61,64,92,64,58,86,61,61,59,65,61,53,107,56,60,54,70,119,90,76,96,84,85,147,85,85,127,87,77,52,50,65,70,77,70,78,72,75,72,70,106,91,72,89,92,73,70,90,51,79,67,83,84,61,63,62,71,103,107,95,105,56,54,85,84,77,76", "endOffsets": "324,3099,3178,3265,3357,3444,4290,4406,4489,4620,4685,4867,4932,4991,5078,5140,5202,5262,5328,5390,5444,5552,5609,5670,5725,5796,5916,6007,6084,6181,6266,6352,6500,6586,6672,6800,6888,6966,7019,7070,7136,7207,7285,7356,7435,7508,7584,7657,7728,7835,7927,8000,8090,8183,8257,8328,8419,8471,8551,8619,8703,8788,8850,8914,8977,9049,9153,9261,9357,9463,9520,9575,9744,10049,10127,10204"}}]}]}