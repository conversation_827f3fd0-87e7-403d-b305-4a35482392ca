<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_test" modulePackage="com.example.uhf" filePath="app\src\main\res\layout\activity_test.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_test_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="1" endLine="167" endOffset="14"/></Target><Target id="@+id/layout_filter" view="LinearLayout"><Expressions/><location startLine="8" startOffset="8" endLine="59" endOffset="22"/></Target><Target id="@+id/tvTime" view="TextView"><Expressions/><location startLine="25" startOffset="12" endLine="31" endOffset="34"/></Target><Target id="@+id/tvSpeed" view="TextView"><Expressions/><location startLine="41" startOffset="12" endLine="50" endOffset="34"/></Target><Target id="@+id/layout4" view="LinearLayout"><Expressions/><location startLine="61" startOffset="8" endLine="122" endOffset="22"/></Target><Target id="@+id/tv_count" view="TextView"><Expressions/><location startLine="80" startOffset="12" endLine="88" endOffset="42"/></Target><Target id="@+id/tv_total" view="TextView"><Expressions/><location startLine="90" startOffset="12" endLine="98" endOffset="42"/></Target><Target id="@+id/LvTags" view="ListView"><Expressions/><location startLine="129" startOffset="8" endLine="133" endOffset="38"/></Target><Target id="@+id/layout12" view="LinearLayout"><Expressions/><location startLine="135" startOffset="4" endLine="164" endOffset="18"/></Target><Target id="@+id/BtInventory" view="Button"><Expressions/><location startLine="143" startOffset="8" endLine="152" endOffset="46"/></Target><Target id="@+id/BtClear" view="Button"><Expressions/><location startLine="154" startOffset="8" endLine="163" endOffset="37"/></Target></Targets></Layout>