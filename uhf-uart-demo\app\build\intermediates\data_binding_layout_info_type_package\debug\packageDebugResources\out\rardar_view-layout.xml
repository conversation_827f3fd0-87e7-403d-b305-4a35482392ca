<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="rardar_view" modulePackage="com.example.uhf" filePath="app\src\main\res\layout\rardar_view.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/rardar_view_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="42" endOffset="51"/></Target><Target id="@+id/_radarBackgroundView" view="com.example.uhf.view.RadarBackgroundView"><Expressions/><location startLine="8" startOffset="4" endLine="16" endOffset="51"/></Target><Target id="@+id/_centerImage" view="ImageView"><Expressions/><location startLine="19" startOffset="4" endLine="31" endOffset="51"/></Target><Target id="@+id/_labelPanelView" view="com.example.uhf.view.RadarPanelView"><Expressions/><location startLine="33" startOffset="4" endLine="40" endOffset="69"/></Target></Targets></Layout>