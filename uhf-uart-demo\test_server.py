#!/usr/bin/env python3
"""
Simple test server for UHF Upload functionality
Run this to test the upload service from the Android app
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import datetime
import urllib.parse

class UploadHandler(BaseHTTPRequestHandler):
    def do_POST(self):
        """Handle POST requests from the UHF app"""
        
        # Get the content length
        content_length = int(self.headers.get('Content-Length', 0))
        
        # Read the request body
        post_data = self.rfile.read(content_length)
        
        try:
            # Parse JSON data
            data = json.loads(post_data.decode('utf-8'))
            
            print(f"\n=== Upload Received at {datetime.datetime.now()} ===")
            print(f"Headers: {dict(self.headers)}")
            print(f"Data: {json.dumps(data, indent=2)}")
            
            # Validate required fields
            if 'sessionId' not in data:
                self.send_error_response(400, "Missing sessionId")
                return
                
            if 'tags' not in data:
                self.send_error_response(400, "Missing tags")
                return
            
            # Check API key if provided
            api_key = self.headers.get('X-API-Key')
            if api_key:
                print(f"API Key: {api_key}")
            
            # Simulate processing
            tag_count = len(data.get('tags', []))
            
            # Send success response
            response = {
                "success": True,
                "message": f"Successfully uploaded {tag_count} tags",
                "uploadId": f"upload_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "processedTags": tag_count,
                "timestamp": datetime.datetime.now().isoformat()
            }
            
            self.send_success_response(response)
            print(f"✅ Upload successful: {tag_count} tags processed")
            
        except json.JSONDecodeError as e:
            print(f"❌ JSON decode error: {e}")
            self.send_error_response(400, f"Invalid JSON: {e}")
        except Exception as e:
            print(f"❌ Server error: {e}")
            self.send_error_response(500, f"Server error: {e}")
    
    def do_GET(self):
        """Handle GET requests (for testing connectivity)"""
        if self.path == '/test':
            response = {
                "status": "ok",
                "message": "UHF Upload Test Server is running",
                "timestamp": datetime.datetime.now().isoformat()
            }
            self.send_success_response(response)
            print("✅ Test connection successful")
        else:
            self.send_error_response(404, "Not found")
    
    def send_success_response(self, data):
        """Send a successful JSON response"""
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        response_json = json.dumps(data, indent=2)
        self.wfile.write(response_json.encode('utf-8'))
    
    def send_error_response(self, code, message):
        """Send an error JSON response"""
        self.send_response(code)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        error_response = {
            "success": False,
            "error": message,
            "timestamp": datetime.datetime.now().isoformat()
        }
        
        response_json = json.dumps(error_response, indent=2)
        self.wfile.write(response_json.encode('utf-8'))
    
    def log_message(self, format, *args):
        """Override to customize logging"""
        print(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] {format % args}")

def run_server(port=8080):
    """Run the test server"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, UploadHandler)
    
    print(f"""
🚀 UHF Upload Test Server Starting...
📡 Server running on: http://localhost:{port}
📱 Use this URL in your Android app: http://YOUR_PC_IP:{port}/upload

📋 Test endpoints:
   GET  /test   - Test connectivity
   POST /upload - Upload RFID data

💡 Make sure your Android device and PC are on the same network!
🔑 You can test with any API key or leave it empty

Press Ctrl+C to stop the server
""")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
        httpd.server_close()

if __name__ == '__main__':
    run_server()
