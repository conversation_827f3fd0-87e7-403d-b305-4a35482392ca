<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_block_permalock" modulePackage="com.example.uhf" filePath="app\src\main\res\layout\fragment_block_permalock.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.FrameLayout"><Targets><Target tag="layout/fragment_block_permalock_0" view="FrameLayout"><Expressions/><location startLine="0" startOffset="0" endLine="365" endOffset="13"/></Target><Target id="@+id/cb_filter_2" view="CheckBox"><Expressions/><location startLine="23" startOffset="9" endLine="30" endOffset="14"/></Target><Target id="@+id/etPtr_filter_perm" view="EditText"><Expressions/><location startLine="40" startOffset="13" endLine="46" endOffset="36"/></Target><Target id="@+id/etLen_filter_perm" view="EditText"><Expressions/><location startLine="57" startOffset="13" endLine="63" endOffset="35"/></Target><Target id="@+id/etData_filter_perm" view="EditText"><Expressions/><location startLine="80" startOffset="13" endLine="86" endOffset="44"/></Target><Target id="@+id/rbEPC_filter_perm" view="RadioButton"><Expressions/><location startLine="95" startOffset="13" endLine="105" endOffset="65"/></Target><Target id="@+id/rbTID_filter_perm" view="RadioButton"><Expressions/><location startLine="107" startOffset="13" endLine="118" endOffset="65"/></Target><Target id="@+id/rbUser_filter_perm" view="RadioButton"><Expressions/><location startLine="120" startOffset="13" endLine="131" endOffset="65"/></Target><Target id="@+id/SpinnerBank" view="Spinner"><Expressions/><location startLine="147" startOffset="13" endLine="151" endOffset="53"/></Target><Target id="@+id/EtPtr" view="EditText"><Expressions/><location startLine="162" startOffset="13" endLine="171" endOffset="50"/></Target><Target id="@+id/EtRange" view="EditText"><Expressions/><location startLine="182" startOffset="13" endLine="191" endOffset="50"/></Target><Target id="@+id/SpinnerReadLock" view="Spinner"><Expressions/><location startLine="203" startOffset="13" endLine="207" endOffset="57"/></Target><Target id="@+id/EtAccessPwd" view="EditText"><Expressions/><location startLine="219" startOffset="13" endLine="223" endOffset="42"/></Target><Target id="@+id/cbBlock1" view="CheckBox"><Expressions/><location startLine="229" startOffset="13" endLine="234" endOffset="55"/></Target><Target id="@+id/cbBlock2" view="CheckBox"><Expressions/><location startLine="235" startOffset="13" endLine="240" endOffset="55"/></Target><Target id="@+id/cbBlock3" view="CheckBox"><Expressions/><location startLine="241" startOffset="13" endLine="246" endOffset="55"/></Target><Target id="@+id/cbBlock4" view="CheckBox"><Expressions/><location startLine="247" startOffset="13" endLine="252" endOffset="55"/></Target><Target id="@+id/cbBlock5" view="CheckBox"><Expressions/><location startLine="259" startOffset="13" endLine="264" endOffset="55"/></Target><Target id="@+id/cbBlock6" view="CheckBox"><Expressions/><location startLine="265" startOffset="13" endLine="270" endOffset="55"/></Target><Target id="@+id/cbBlock7" view="CheckBox"><Expressions/><location startLine="271" startOffset="13" endLine="276" endOffset="55"/></Target><Target id="@+id/cbBlock8" view="CheckBox"><Expressions/><location startLine="277" startOffset="13" endLine="282" endOffset="55"/></Target><Target id="@+id/cbBlock9" view="CheckBox"><Expressions/><location startLine="288" startOffset="13" endLine="293" endOffset="55"/></Target><Target id="@+id/cbBlock10" view="CheckBox"><Expressions/><location startLine="294" startOffset="13" endLine="299" endOffset="55"/></Target><Target id="@+id/cbBlock11" view="CheckBox"><Expressions/><location startLine="300" startOffset="13" endLine="305" endOffset="55"/></Target><Target id="@+id/cbBlock12" view="CheckBox"><Expressions/><location startLine="306" startOffset="13" endLine="311" endOffset="55"/></Target><Target id="@+id/cbBlock13" view="CheckBox"><Expressions/><location startLine="317" startOffset="13" endLine="322" endOffset="55"/></Target><Target id="@+id/cbBlock14" view="CheckBox"><Expressions/><location startLine="323" startOffset="13" endLine="328" endOffset="55"/></Target><Target id="@+id/cbBlock15" view="CheckBox"><Expressions/><location startLine="329" startOffset="13" endLine="334" endOffset="55"/></Target><Target id="@+id/cbBlock16" view="CheckBox"><Expressions/><location startLine="335" startOffset="13" endLine="340" endOffset="55"/></Target><Target id="@+id/maskbuf" view="TextView"><Expressions/><location startLine="352" startOffset="15" endLine="355" endOffset="57"/></Target><Target id="@+id/btnOK" view="Button"><Expressions/><location startLine="358" startOffset="11" endLine="362" endOffset="51"/></Target></Targets></Layout>