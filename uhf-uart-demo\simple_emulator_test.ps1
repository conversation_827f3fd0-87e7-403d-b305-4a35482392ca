# Simple PowerShell script to test UHF app on Android emulator

Write-Host "UHF App Emulator Testing Setup" -ForegroundColor Green
Write-Host "===============================" -ForegroundColor Green

# Step 1: Check Android SDK
Write-Host ""
Write-Host "Step 1: Checking Android SDK..." -ForegroundColor Yellow

$androidSDK = $env:ANDROID_HOME
if (-not $androidSDK) {
    $androidSDK = "$env:LOCALAPPDATA\Android\Sdk"
}

if (-not (Test-Path $androidSDK)) {
    Write-Host "ERROR: Android SDK not found!" -ForegroundColor Red
    Write-Host "Please set ANDROID_HOME or install Android Studio" -ForegroundColor Yellow
    exit 1
}

Write-Host "SUCCESS: Android SDK found at: $androidSDK" -ForegroundColor Green

# Set up paths
$adbPath = "$androidSDK\platform-tools\adb.exe"
$emulatorPath = "$androidSDK\emulator\emulator.exe"

# Step 2: Check ADB
Write-Host ""
Write-Host "Step 2: Checking ADB..." -ForegroundColor Yellow

if (Test-Path $adbPath) {
    Write-Host "SUCCESS: ADB found" -ForegroundColor Green
    try {
        $adbVersion = & $adbPath version 2>$null
        Write-Host "ADB Version: $($adbVersion[0])" -ForegroundColor Cyan
    } catch {
        Write-Host "WARNING: Could not get ADB version" -ForegroundColor Yellow
    }
} else {
    Write-Host "ERROR: ADB not found at: $adbPath" -ForegroundColor Red
    exit 1
}

# Step 3: Check emulator
Write-Host ""
Write-Host "Step 3: Checking Emulator..." -ForegroundColor Yellow

if (Test-Path $emulatorPath) {
    Write-Host "SUCCESS: Emulator found" -ForegroundColor Green
} else {
    Write-Host "ERROR: Emulator not found at: $emulatorPath" -ForegroundColor Red
    exit 1
}

# Step 4: List available AVDs
Write-Host ""
Write-Host "Step 4: Checking Available AVDs..." -ForegroundColor Yellow

try {
    $avds = & $emulatorPath -list-avds 2>$null
    if ($avds -and $avds.Count -gt 0) {
        Write-Host "SUCCESS: Available AVDs:" -ForegroundColor Green
        foreach ($avd in $avds) {
            Write-Host "  - $avd" -ForegroundColor Cyan
        }
        $selectedAVD = $avds[0]
        Write-Host "Will use: $selectedAVD" -ForegroundColor Cyan
    } else {
        Write-Host "WARNING: No AVDs found." -ForegroundColor Yellow
        Write-Host "Please create one in Android Studio first." -ForegroundColor Yellow
        Write-Host "Go to Tools -> AVD Manager -> Create Virtual Device" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "ERROR: Could not list AVDs: $_" -ForegroundColor Red
    exit 1
}

# Step 5: Check for running devices
Write-Host ""
Write-Host "Step 5: Checking for running devices..." -ForegroundColor Yellow

try {
    $devices = & $adbPath devices 2>$null
    $runningDevices = $devices | Select-String "device$"
    
    if ($runningDevices) {
        Write-Host "SUCCESS: Found running devices:" -ForegroundColor Green
        foreach ($device in $runningDevices) {
            Write-Host "  - $device" -ForegroundColor Cyan
        }
        $startNew = $false
    } else {
        Write-Host "No devices running. Will start emulator." -ForegroundColor Yellow
        $startNew = $true
    }
} catch {
    Write-Host "WARNING: Could not check devices" -ForegroundColor Yellow
    $startNew = $true
}

# Step 6: Start emulator if needed
if ($startNew) {
    Write-Host ""
    Write-Host "Step 6: Starting emulator..." -ForegroundColor Yellow
    Write-Host "Starting AVD: $selectedAVD" -ForegroundColor Cyan
    Write-Host "This may take 2-5 minutes..." -ForegroundColor Yellow
    
    try {
        Start-Process -FilePath $emulatorPath -ArgumentList "-avd", $selectedAVD -WindowStyle Normal
        Write-Host "Emulator process started. Waiting for boot..." -ForegroundColor Cyan
        
        # Wait for emulator to boot
        $timeout = 300 # 5 minutes
        $elapsed = 0
        do {
            Start-Sleep -Seconds 5
            $elapsed += 5
            try {
                $bootComplete = & $adbPath shell getprop sys.boot_completed 2>$null
                Write-Host "." -NoNewline -ForegroundColor Cyan
            } catch {
                $bootComplete = $null
            }
            
            if ($elapsed -gt $timeout) {
                Write-Host ""
                Write-Host "TIMEOUT: Emulator took too long to boot" -ForegroundColor Red
                exit 1
            }
        } while ($bootComplete -ne "1")
        
        Write-Host ""
        Write-Host "SUCCESS: Emulator is ready!" -ForegroundColor Green
    } catch {
        Write-Host "ERROR: Failed to start emulator: $_" -ForegroundColor Red
        exit 1
    }
}

# Step 7: Check for APK
Write-Host ""
Write-Host "Step 7: Checking for APK..." -ForegroundColor Yellow

$apkPath = "UHF-serial_v1.4.5.apk"
if (Test-Path $apkPath) {
    Write-Host "SUCCESS: APK found: $apkPath" -ForegroundColor Green
} else {
    Write-Host "ERROR: APK not found: $apkPath" -ForegroundColor Red
    Write-Host "Please ensure the APK file is in the current directory" -ForegroundColor Yellow
    exit 1
}

# Step 8: Install APK
Write-Host ""
Write-Host "Step 8: Installing APK..." -ForegroundColor Yellow

try {
    $installResult = & $adbPath install $apkPath 2>&1
    if ($installResult -match "Success") {
        Write-Host "SUCCESS: APK installed successfully!" -ForegroundColor Green
    } elseif ($installResult -match "INSTALL_FAILED_ALREADY_EXISTS") {
        Write-Host "App already installed. Trying to reinstall..." -ForegroundColor Yellow
        $reinstallResult = & $adbPath install -r $apkPath 2>&1
        if ($reinstallResult -match "Success") {
            Write-Host "SUCCESS: APK reinstalled successfully!" -ForegroundColor Green
        } else {
            Write-Host "WARNING: Reinstall result: $reinstallResult" -ForegroundColor Yellow
        }
    } else {
        Write-Host "WARNING: Install result: $installResult" -ForegroundColor Yellow
    }
} catch {
    Write-Host "ERROR: Failed to install APK: $_" -ForegroundColor Red
    exit 1
}

# Step 9: Start test server
Write-Host ""
Write-Host "Step 9: Starting test server..." -ForegroundColor Yellow

if (Test-Path "test_server.py") {
    try {
        Start-Process -FilePath "python" -ArgumentList "test_server.py" -WindowStyle Normal
        Write-Host "SUCCESS: Test server started in separate window" -ForegroundColor Green
    } catch {
        Write-Host "WARNING: Could not start test server automatically" -ForegroundColor Yellow
        Write-Host "Please run: python test_server.py" -ForegroundColor Cyan
    }
} else {
    Write-Host "WARNING: test_server.py not found" -ForegroundColor Yellow
    Write-Host "Please run the test server manually" -ForegroundColor Cyan
}

# Step 10: Get network info
Write-Host ""
Write-Host "Step 10: Network Information..." -ForegroundColor Yellow

try {
    $ipAddress = (Get-NetIPAddress -AddressFamily IPv4 | Where-Object {$_.IPAddress -like "192.168.*" -or $_.IPAddress -like "10.*"} | Select-Object -First 1).IPAddress
    if ($ipAddress) {
        Write-Host "SUCCESS: Your PC IP: $ipAddress" -ForegroundColor Green
        Write-Host "Use this URL in the app: http://$ipAddress:8080/upload" -ForegroundColor Cyan
    } else {
        Write-Host "Could not determine IP address" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Could not get network info" -ForegroundColor Yellow
}

Write-Host "For emulator, you can also try: http://********:8080/upload" -ForegroundColor Cyan

# Step 11: Launch app
Write-Host ""
Write-Host "Step 11: Launching app..." -ForegroundColor Yellow

try {
    & $adbPath shell am start -n com.maspects.uhftool/.activity.UHFMainActivity 2>$null
    Write-Host "SUCCESS: App launched!" -ForegroundColor Green
} catch {
    Write-Host "WARNING: Could not auto-launch app. Please open it manually." -ForegroundColor Yellow
}

# Final instructions
Write-Host ""
Write-Host "Setup Complete!" -ForegroundColor Green
Write-Host "===============" -ForegroundColor Green
Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Yellow
Write-Host "1. Open the UHF app on the emulator" -ForegroundColor White
Write-Host "2. Go to Upload Settings tab" -ForegroundColor White
Write-Host "3. Configure server URL and API key" -ForegroundColor White
Write-Host "4. Test the connection" -ForegroundColor White
Write-Host "5. Check the test server console for requests" -ForegroundColor White
Write-Host ""
Write-Host "Monitor logs with: adb logcat | findstr uhftool" -ForegroundColor Cyan
Write-Host ""
Write-Host "Happy testing!" -ForegroundColor Green
