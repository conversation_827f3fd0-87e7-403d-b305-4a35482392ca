// Generated by view binder compiler. Do not edit!
package com.example.uhf.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.HorizontalScrollView;
import android.widget.TabWidget;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentTabHost;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.uhf.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final FragmentTabHost rootView;

  @NonNull
  public final HorizontalScrollView hScrollerMytabhostactivity;

  @NonNull
  public final FrameLayout realtabcontent;

  @NonNull
  public final FrameLayout tabcontent;

  @NonNull
  public final FragmentTabHost tabhost;

  @NonNull
  public final TabWidget tabs;

  private ActivityMainBinding(@NonNull FragmentTabHost rootView,
      @NonNull HorizontalScrollView hScrollerMytabhostactivity, @NonNull FrameLayout realtabcontent,
      @NonNull FrameLayout tabcontent, @NonNull FragmentTabHost tabhost, @NonNull TabWidget tabs) {
    this.rootView = rootView;
    this.hScrollerMytabhostactivity = hScrollerMytabhostactivity;
    this.realtabcontent = realtabcontent;
    this.tabcontent = tabcontent;
    this.tabhost = tabhost;
    this.tabs = tabs;
  }

  @Override
  @NonNull
  public FragmentTabHost getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.hScroller_mytabhostactivity;
      HorizontalScrollView hScrollerMytabhostactivity = ViewBindings.findChildViewById(rootView, id);
      if (hScrollerMytabhostactivity == null) {
        break missingId;
      }

      id = R.id.realtabcontent;
      FrameLayout realtabcontent = ViewBindings.findChildViewById(rootView, id);
      if (realtabcontent == null) {
        break missingId;
      }

      id = android.R.id.tabcontent;
      FrameLayout tabcontent = ViewBindings.findChildViewById(rootView, id);
      if (tabcontent == null) {
        break missingId;
      }

      FragmentTabHost tabhost = (FragmentTabHost) rootView;

      id = android.R.id.tabs;
      TabWidget tabs = ViewBindings.findChildViewById(rootView, id);
      if (tabs == null) {
        break missingId;
      }

      return new ActivityMainBinding((FragmentTabHost) rootView, hScrollerMytabhostactivity,
          realtabcontent, tabcontent, tabhost, tabs);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
