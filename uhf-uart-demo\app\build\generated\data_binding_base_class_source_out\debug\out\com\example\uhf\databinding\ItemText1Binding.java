// Generated by view binder compiler. Do not edit!
package com.example.uhf.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.example.uhf.R;
import java.lang.NullPointerException;
import java.lang.Override;

public final class ItemText1Binding implements ViewBinding {
  @NonNull
  private final TextView rootView;

  @NonNull
  public final TextView tv;

  private ItemText1Binding(@NonNull TextView rootView, @NonNull TextView tv) {
    this.rootView = rootView;
    this.tv = tv;
  }

  @Override
  @NonNull
  public TextView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemText1Binding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemText1Binding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_text1, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemText1Binding bind(@NonNull View rootView) {
    if (rootView == null) {
      throw new NullPointerException("rootView");
    }

    TextView tv = (TextView) rootView;

    return new ItemText1Binding((TextView) rootView, tv);
  }
}
