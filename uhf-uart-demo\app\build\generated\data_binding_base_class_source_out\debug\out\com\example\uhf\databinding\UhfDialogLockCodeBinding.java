// Generated by view binder compiler. Do not edit!
package com.example.uhf.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.uhf.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class UhfDialogLockCodeBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final CheckBox cbAccess;

  @NonNull
  public final CheckBox cbEPC;

  @NonNull
  public final CheckBox cbKill;

  @NonNull
  public final CheckBox cbPerm;

  @NonNull
  public final CheckBox cbTid;

  @NonNull
  public final CheckBox cbUser;

  @NonNull
  public final RadioButton rbLock;

  @NonNull
  public final RadioButton rbOpen;

  @NonNull
  public final RadioGroup rgFileType;

  private UhfDialogLockCodeBinding(@NonNull LinearLayout rootView, @NonNull CheckBox cbAccess,
      @NonNull CheckBox cbEPC, @NonNull CheckBox cbKill, @NonNull CheckBox cbPerm,
      @NonNull CheckBox cbTid, @NonNull CheckBox cbUser, @NonNull RadioButton rbLock,
      @NonNull RadioButton rbOpen, @NonNull RadioGroup rgFileType) {
    this.rootView = rootView;
    this.cbAccess = cbAccess;
    this.cbEPC = cbEPC;
    this.cbKill = cbKill;
    this.cbPerm = cbPerm;
    this.cbTid = cbTid;
    this.cbUser = cbUser;
    this.rbLock = rbLock;
    this.rbOpen = rbOpen;
    this.rgFileType = rgFileType;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static UhfDialogLockCodeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static UhfDialogLockCodeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.uhf_dialog_lock_code, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static UhfDialogLockCodeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cbAccess;
      CheckBox cbAccess = ViewBindings.findChildViewById(rootView, id);
      if (cbAccess == null) {
        break missingId;
      }

      id = R.id.cbEPC;
      CheckBox cbEPC = ViewBindings.findChildViewById(rootView, id);
      if (cbEPC == null) {
        break missingId;
      }

      id = R.id.cbKill;
      CheckBox cbKill = ViewBindings.findChildViewById(rootView, id);
      if (cbKill == null) {
        break missingId;
      }

      id = R.id.cbPerm;
      CheckBox cbPerm = ViewBindings.findChildViewById(rootView, id);
      if (cbPerm == null) {
        break missingId;
      }

      id = R.id.cbTid;
      CheckBox cbTid = ViewBindings.findChildViewById(rootView, id);
      if (cbTid == null) {
        break missingId;
      }

      id = R.id.cbUser;
      CheckBox cbUser = ViewBindings.findChildViewById(rootView, id);
      if (cbUser == null) {
        break missingId;
      }

      id = R.id.rbLock;
      RadioButton rbLock = ViewBindings.findChildViewById(rootView, id);
      if (rbLock == null) {
        break missingId;
      }

      id = R.id.rbOpen;
      RadioButton rbOpen = ViewBindings.findChildViewById(rootView, id);
      if (rbOpen == null) {
        break missingId;
      }

      id = R.id.rgFileType;
      RadioGroup rgFileType = ViewBindings.findChildViewById(rootView, id);
      if (rgFileType == null) {
        break missingId;
      }

      return new UhfDialogLockCodeBinding((LinearLayout) rootView, cbAccess, cbEPC, cbKill, cbPerm,
          cbTid, cbUser, rbLock, rbOpen, rgFileType);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
