<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:padding="2dp">

    <com.example.uhf.view.RadarBackgroundView
        android:id="@+id/_radarBackgroundView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <ImageView
        android:id="@+id/_centerImage"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_gravity="center"
        android:adjustViewBounds="true"
        android:background="@drawable/phone"
        app:layout_constraintBottom_toBottomOf="@id/_radarBackgroundView"
        app:layout_constraintEnd_toEndOf="@id/_radarBackgroundView"
        app:layout_constraintHeight_percent="0.25"
        app:layout_constraintStart_toStartOf="@id/_radarBackgroundView"
        app:layout_constraintTop_toTopOf="@id/_radarBackgroundView"
        app:layout_constraintWidth_percent="0.12" />

    <com.example.uhf.view.RadarPanelView
        android:id="@+id/_labelPanelView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@id/_radarBackgroundView"
        app:layout_constraintEnd_toEndOf="@id/_radarBackgroundView"
        app:layout_constraintStart_toStartOf="@id/_radarBackgroundView"
        app:layout_constraintTop_toTopOf="@id/_radarBackgroundView" />

</androidx.constraintlayout.widget.ConstraintLayout>