<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:scrollbars="none">

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_margin="2dp"
            android:background="@drawable/rectangle_bg"
            android:orientation="vertical">

            <TextView
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:background="@color/blue1"
                android:padding="5dp"
                android:text="@string/normal_set"
                android:textSize="18sp" />

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:background="@color/gray1"
                android:orientation="vertical"
                android:paddingBottom="5dp">

                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/tvMode"
                        android:textSize="16sp" />

                    <Spinner
                        android:id="@+id/spFrequency"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:entries="@array/arrayMode"
                        android:gravity="center_horizontal" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:padding="5dp">

                    <Button
                        android:id="@+id/btnSetFrequency"
                        android:layout_width="0dip"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/button_bg"
                        android:text="@string/btSetFre"
                        android:textColor="@color/white"
                        android:textSize="16sp" />

                    <Button
                        android:id="@+id/btnGetFrequency"
                        android:layout_width="0dip"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="20dp"
                        android:layout_weight="1"
                        android:background="@drawable/button_bg"
                        android:text="@string/btGetFre"
                        android:textColor="@color/white"
                        android:textSize="16sp" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="vertical"
                android:paddingTop="5dp"
                android:paddingBottom="3dp">

                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/uhf_title_power"
                        android:textSize="16sp" />

                    <Spinner
                        android:id="@+id/spPower"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:entries="@array/arrayPower"
                        android:gravity="center_horizontal" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/uhf_title_dbm" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:padding="5dp">

                    <Button
                        android:id="@+id/btnSetPower"
                        android:layout_width="0dip"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/button_bg"
                        android:text="@string/uhf_btn_setpower"
                        android:textColor="@color/white"
                        android:textSize="16sp" />

                    <Button
                        android:id="@+id/btnGetPower"
                        android:layout_width="0dip"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="20dp"
                        android:layout_weight="1"
                        android:background="@drawable/button_bg"
                        android:text="@string/uhf_btn_getpower"
                        android:textColor="@color/white"
                        android:textSize="16sp" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_freHop"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:background="@color/gray1"
                android:orientation="vertical"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/freHopType"
                        android:textSize="16sp" />

                    <RadioGroup
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <RadioButton
                            android:id="@+id/rb_china"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/China"
                            android:textColor="@drawable/check_text_color"
                            android:textSize="15sp"
                            android:visibility="gone" />

                        <RadioButton
                            android:id="@+id/rb_Europe"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="10dp"
                            android:text="@string/Europe"
                            android:textColor="@drawable/check_text_color"
                            android:textSize="15sp"
                            android:visibility="gone" />

                        <RadioButton
                            android:id="@+id/rb_America"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="10dp"
                            android:checked="true"
                            android:onClick="onClick_rbAmerica"
                            android:text="@string/America"
                            android:textColor="@drawable/check_text_color"
                            android:textSize="15sp" />

                        <RadioButton
                            android:id="@+id/rb_Others"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="10dp"
                            android:onClick="onClick_rbOthers"
                            android:text="@string/Others"
                            android:textColor="@drawable/check_text_color"
                            android:textSize="15sp" />
                    </RadioGroup>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/tvFreHop"
                        android:textSize="16sp" />

                    <Spinner
                        android:id="@+id/spFreHop"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:entries="@array/arrayFreHop_us"
                        android:gravity="center_horizontal" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:padding="5dp">

                    <Button
                        android:id="@+id/btnSetFreHop"
                        android:layout_width="0dip"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/button_bg"
                        android:text="@string/btSetFreHop"
                        android:textColor="@color/white"
                        android:textSize="16sp" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_margin="2dp"
            android:background="@drawable/rectangle_bg2"
            android:orientation="vertical">

            <!--            <TextView-->
            <!--                android:layout_width="fill_parent"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:background="@color/green2"-->
            <!--                android:padding="5dp"-->
            <!--                android:text="@string/R2000_set"-->
            <!--                android:textSize="18sp" />-->

            <!--            <LinearLayout-->
            <!--                android:layout_width="fill_parent"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:background="@color/gray1"-->
            <!--                android:orientation="vertical"-->
            <!--                android:paddingBottom="5dp">-->

            <!--                <LinearLayout-->
            <!--                    android:layout_width="wrap_content"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:orientation="horizontal"-->
            <!--                    android:paddingLeft="10dp"-->
            <!--                    android:paddingRight="10dp">-->

            <!--                    <TextView-->
            <!--                        android:layout_width="wrap_content"-->
            <!--                        android:layout_height="wrap_content"-->
            <!--                        android:text="@string/title_work_time"-->
            <!--                        android:textSize="16sp" />-->

            <!--                    <EditText-->
            <!--                        android:id="@+id/et_worktime"-->
            <!--                        android:layout_width="80dip"-->
            <!--                        android:layout_height="wrap_content"-->
            <!--                        android:layout_weight="1"-->
            <!--                        android:gravity="center_horizontal"-->
            <!--                        android:hint="0"-->
            <!--                        android:inputType="number"-->
            <!--                        android:singleLine="true"-->
            <!--                        android:text="10"-->
            <!--                        android:textColor="@color/red1"-->
            <!--                        android:textSize="15sp"></EditText>-->

            <!--                    <TextView-->
            <!--                        android:layout_width="wrap_content"-->
            <!--                        android:layout_height="wrap_content"-->
            <!--                        android:text="ms"-->
            <!--                        android:textSize="16sp" />-->

            <!--                    <TextView-->
            <!--                        android:layout_width="wrap_content"-->
            <!--                        android:layout_height="wrap_content"-->
            <!--                        android:layout_marginLeft="5dp"-->
            <!--                        android:text="@string/title_wait_time"-->
            <!--                        android:textSize="16sp" />-->

            <!--                    <EditText-->
            <!--                        android:id="@+id/et_waittime"-->
            <!--                        android:layout_width="80dip"-->
            <!--                        android:layout_height="wrap_content"-->
            <!--                        android:layout_weight="1"-->
            <!--                        android:gravity="center_horizontal"-->
            <!--                        android:hint="0"-->
            <!--                        android:inputType="number"-->
            <!--                        android:singleLine="true"-->
            <!--                        android:text="10"-->
            <!--                        android:textColor="@color/red1"-->
            <!--                        android:textSize="15sp"></EditText>-->

            <!--                    <TextView-->
            <!--                        android:layout_width="wrap_content"-->
            <!--                        android:layout_height="wrap_content"-->
            <!--                        android:text="ms"-->
            <!--                        android:textSize="16sp" />-->
            <!--                </LinearLayout>-->

            <!--                <LinearLayout-->
            <!--                    android:layout_width="fill_parent"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:padding="5dp">-->

            <!--                    <Button-->
            <!--                        android:id="@+id/btnWorkWait"-->
            <!--                        android:layout_width="0dip"-->
            <!--                        android:layout_height="wrap_content"-->
            <!--                        android:layout_weight="1"-->
            <!--                        android:background="@drawable/button_bg3"-->
            <!--                        android:text="@string/uhf_btn_workwait"-->
            <!--                        android:textColor="@color/white"-->
            <!--                        android:textSize="16sp" />-->

            <!--                    <Button-->
            <!--                        android:id="@+id/btnGetWait"-->
            <!--                        android:layout_width="0dip"-->
            <!--                        android:layout_height="wrap_content"-->
            <!--                        android:layout_marginLeft="20dp"-->
            <!--                        android:layout_weight="1"-->
            <!--                        android:background="@drawable/button_bg3"-->
            <!--                        android:text="@string/uhf_btn_get_workwait"-->
            <!--                        android:textColor="@color/white"-->
            <!--                        android:textSize="16sp" />-->
            <!--                </LinearLayout>-->
            <!--            </LinearLayout>-->

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="vertical"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/tvProtocol"
                        android:textSize="16sp" />

                    <Spinner
                        android:id="@+id/SpinnerAgreement"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:entries="@array/arrayProtocol" />
                </LinearLayout>

                <Button
                    android:id="@+id/btnSetProtocol"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/button_bg3"
                    android:text="@string/btSetProtocol"
                    android:textColor="@color/white"
                    android:textSize="16sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:background="@color/gray1"
                android:orientation="vertical"
                android:paddingTop="5dp"
                android:paddingBottom="5dp">

                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/uhf_title_link"
                        android:textSize="16sp" />

                    <Spinner
                        android:id="@+id/splinkParams"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:entries="@array/arrayLink" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:padding="5dp">

                    <Button
                        android:id="@+id/btnSetLinkParams"
                        android:layout_width="0dip"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/button_bg3"
                        android:text="@string/uhf_btn_setLinkParams"
                        android:textColor="@color/white"
                        android:textSize="16sp" />

                    <Button
                        android:id="@+id/btnGetLinkParams"
                        android:layout_width="0dip"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="20dp"
                        android:layout_weight="1"
                        android:background="@drawable/button_bg3"
                        android:text="@string/uhf_btn_getLinkParams"
                        android:textColor="@color/white"
                        android:textSize="16sp" />
                </LinearLayout>
            </LinearLayout>


            <!--epc+user+RESERVED-->
            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="vertical"
                android:paddingTop="5dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingHorizontal="10dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/uhf_btn_memoryBank"
                        android:textSize="16sp" />

                    <Spinner
                        android:id="@+id/spMemoryBank"
                        android:layout_width="wrap_content"
                        android:layout_height="40dp"
                        android:layout_weight="1"
                        android:entries="@array/arrayMemoryBank" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llMemoryBankParams"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="10dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Offset:"
                        android:textSize="16sp" />

                    <EditText
                        android:id="@+id/etOffset"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:inputType="number"
                        android:text="0" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/word" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:text="Length:"
                        android:textSize="16sp" />

                    <EditText
                        android:id="@+id/etLength"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:inputType="number"
                        android:text="6" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/word" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:padding="5dp">

                    <Button
                        android:id="@+id/btnSetMemoryBank"
                        android:layout_width="0dip"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/button_bg3"
                        android:text="@string/er_dsoft_get_Set"
                        android:textColor="@color/white"
                        android:textSize="16sp" />

                    <Button
                        android:id="@+id/btnGetMemoryBank"
                        android:layout_width="0dip"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="20dp"
                        android:layout_weight="1"
                        android:background="@drawable/button_bg3"
                        android:text="@string/er_dsoft_get_Get"
                        android:textColor="@color/white"
                        android:textSize="16sp" />
                </LinearLayout>


            </LinearLayout>

            <!--session-->
            <LinearLayout
                android:id="@+id/llSession"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/gray1"
                android:orientation="horizontal"
                android:paddingLeft="3dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Session ID:" />

                <Spinner
                    android:id="@+id/spSessionID"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:entries="@array/arraySession" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Inventoried Flag:" />

                <Spinner
                    android:id="@+id/spInventoried"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:entries="@array/arrayInventoried" />
            </LinearLayout>

            <LinearLayout

                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/gray1">

                <Button
                    android:id="@+id/btnSetSession"
                    android:layout_width="0dip"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@drawable/button_bg3"
                    android:text="@string/er_dsoft_get_Set"
                    android:textColor="@color/white" />

                <Button
                    android:id="@+id/btnGetSession"
                    android:layout_width="0dip"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:layout_weight="1"
                    android:background="@drawable/button_bg3"
                    android:text="@string/er_dsoft_get_Get"
                    android:textColor="@color/white" />
            </LinearLayout>
            <!-- session-->

            <!-- fast inventory-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingLeft="3dp">

                <RadioGroup
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <RadioButton
                        android:id="@+id/rbFastInventoryOpen"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Fast Inventory Open" />

                    <RadioButton
                        android:id="@+id/rbFastInventoryClose"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Fast Inventory Close" />
                </RadioGroup>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="5dp">

                <Button
                    android:id="@+id/btnSetFastInventory"
                    android:layout_width="0dip"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@drawable/button_bg3"
                    android:text="@string/er_dsoft_get_Set"
                    android:textColor="@color/white" />

                <Button
                    android:id="@+id/btnGetFastInventory"
                    android:layout_width="0dip"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:layout_weight="1"
                    android:background="@drawable/button_bg3"
                    android:text="@string/er_dsoft_get_Get"
                    android:textColor="@color/white" />
            </LinearLayout>

            <Button
                android:id="@+id/btnFactoryReset"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="5dp"
                android:layout_marginVertical="10dp"
                android:background="@drawable/button_bg3"
                android:text="@string/factory_reset"
                android:textAllCaps="false"
                android:textColor="@color/red2"
                android:textSize="16sp" />


            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:background="@color/gray1"
                android:orientation="vertical"
                android:paddingTop="5dp"
                android:paddingBottom="5dp">

                <CheckBox
                    android:id="@+id/cbTagFocus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:paddingTop="5dp"
                    android:paddingBottom="5dp"
                    android:text="@string/tagFocus"
                    android:textColor="@drawable/check_text_color2" />

                <CheckBox
                    android:id="@+id/cbFastID"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="10dp"
                    android:paddingTop="5dp"
                    android:paddingBottom="5dp"
                    android:text="@string/fastID"
                    android:textColor="@drawable/check_text_color2" />

            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

</ScrollView>