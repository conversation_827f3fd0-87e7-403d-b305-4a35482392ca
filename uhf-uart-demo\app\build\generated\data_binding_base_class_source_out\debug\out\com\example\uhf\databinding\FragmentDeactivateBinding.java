// Generated by view binder compiler. Do not edit!
package com.example.uhf.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.RadioButton;
import android.widget.ScrollView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.uhf.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentDeactivateBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final EditText EtAccessPwdDeactivate;

  @NonNull
  public final EditText Etcmd;

  @NonNull
  public final Button btnDeactivate;

  @NonNull
  public final CheckBox cbFilterDeact;

  @NonNull
  public final EditText etDataFilterDeact;

  @NonNull
  public final EditText etLenFilterDeact;

  @NonNull
  public final EditText etPtrFilterDeact;

  @NonNull
  public final RadioButton rbEPCFilterDeact;

  @NonNull
  public final RadioButton rbTIDFilterDeact;

  @NonNull
  public final RadioButton rbUserFilterDeact;

  private FragmentDeactivateBinding(@NonNull ScrollView rootView,
      @NonNull EditText EtAccessPwdDeactivate, @NonNull EditText Etcmd,
      @NonNull Button btnDeactivate, @NonNull CheckBox cbFilterDeact,
      @NonNull EditText etDataFilterDeact, @NonNull EditText etLenFilterDeact,
      @NonNull EditText etPtrFilterDeact, @NonNull RadioButton rbEPCFilterDeact,
      @NonNull RadioButton rbTIDFilterDeact, @NonNull RadioButton rbUserFilterDeact) {
    this.rootView = rootView;
    this.EtAccessPwdDeactivate = EtAccessPwdDeactivate;
    this.Etcmd = Etcmd;
    this.btnDeactivate = btnDeactivate;
    this.cbFilterDeact = cbFilterDeact;
    this.etDataFilterDeact = etDataFilterDeact;
    this.etLenFilterDeact = etLenFilterDeact;
    this.etPtrFilterDeact = etPtrFilterDeact;
    this.rbEPCFilterDeact = rbEPCFilterDeact;
    this.rbTIDFilterDeact = rbTIDFilterDeact;
    this.rbUserFilterDeact = rbUserFilterDeact;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentDeactivateBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentDeactivateBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_deactivate, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentDeactivateBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.EtAccessPwd_deactivate;
      EditText EtAccessPwdDeactivate = ViewBindings.findChildViewById(rootView, id);
      if (EtAccessPwdDeactivate == null) {
        break missingId;
      }

      id = R.id.Etcmd;
      EditText Etcmd = ViewBindings.findChildViewById(rootView, id);
      if (Etcmd == null) {
        break missingId;
      }

      id = R.id.btn_deactivate;
      Button btnDeactivate = ViewBindings.findChildViewById(rootView, id);
      if (btnDeactivate == null) {
        break missingId;
      }

      id = R.id.cb_filter_deact;
      CheckBox cbFilterDeact = ViewBindings.findChildViewById(rootView, id);
      if (cbFilterDeact == null) {
        break missingId;
      }

      id = R.id.etData_filter_deact;
      EditText etDataFilterDeact = ViewBindings.findChildViewById(rootView, id);
      if (etDataFilterDeact == null) {
        break missingId;
      }

      id = R.id.etLen_filter_deact;
      EditText etLenFilterDeact = ViewBindings.findChildViewById(rootView, id);
      if (etLenFilterDeact == null) {
        break missingId;
      }

      id = R.id.etPtr_filter_deact;
      EditText etPtrFilterDeact = ViewBindings.findChildViewById(rootView, id);
      if (etPtrFilterDeact == null) {
        break missingId;
      }

      id = R.id.rbEPC_filter_deact;
      RadioButton rbEPCFilterDeact = ViewBindings.findChildViewById(rootView, id);
      if (rbEPCFilterDeact == null) {
        break missingId;
      }

      id = R.id.rbTID_filter_deact;
      RadioButton rbTIDFilterDeact = ViewBindings.findChildViewById(rootView, id);
      if (rbTIDFilterDeact == null) {
        break missingId;
      }

      id = R.id.rbUser_filter_deact;
      RadioButton rbUserFilterDeact = ViewBindings.findChildViewById(rootView, id);
      if (rbUserFilterDeact == null) {
        break missingId;
      }

      return new FragmentDeactivateBinding((ScrollView) rootView, EtAccessPwdDeactivate, Etcmd,
          btnDeactivate, cbFilterDeact, etDataFilterDeact, etLenFilterDeact, etPtrFilterDeact,
          rbEPCFilterDeact, rbTIDFilterDeact, rbUserFilterDeact);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
