<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_uhfupgrade" modulePackage="com.example.uhf" filePath="app\src\main\res\layout\fragment_uhfupgrade.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/fragment_uhfupgrade_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="80" endOffset="14"/></Target><Target id="@+id/rb_uhf_module" view="RadioButton"><Expressions/><location startLine="19" startOffset="12" endLine="23" endOffset="42"/></Target><Target id="@+id/rb_ex10" view="RadioButton"><Expressions/><location startLine="24" startOffset="12" endLine="29" endOffset="49"/></Target><Target id="@+id/et_file" view="EditText"><Expressions/><location startLine="47" startOffset="12" endLine="54" endOffset="43"/></Target><Target id="@+id/btnBrowser" view="Button"><Expressions/><location startLine="56" startOffset="12" endLine="62" endOffset="56"/></Target><Target id="@+id/btnUpgrade" view="Button"><Expressions/><location startLine="65" startOffset="8" endLine="69" endOffset="47"/></Target><Target id="@+id/tvMsg" view="TextView"><Expressions/><location startLine="74" startOffset="4" endLine="78" endOffset="33"/></Target></Targets></Layout>