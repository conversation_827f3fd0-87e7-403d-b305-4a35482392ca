<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="popwindow_filter" modulePackage="com.example.uhf" filePath="app\src\main\res\layout\popwindow_filter.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/popwindow_filter_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="179" endOffset="14"/></Target><Target id="@+id/etPtr" view="EditText"><Expressions/><location startLine="19" startOffset="8" endLine="25" endOffset="31"/></Target><Target id="@+id/etLen" view="EditText"><Expressions/><location startLine="36" startOffset="8" endLine="42" endOffset="30"/></Target><Target id="@+id/etData" view="EditText"><Expressions/><location startLine="59" startOffset="8" endLine="65" endOffset="39"/></Target><Target id="@+id/rbEPC" view="RadioButton"><Expressions/><location startLine="74" startOffset="8" endLine="84" endOffset="60"/></Target><Target id="@+id/rbTID" view="RadioButton"><Expressions/><location startLine="86" startOffset="8" endLine="97" endOffset="60"/></Target><Target id="@+id/rbUser" view="RadioButton"><Expressions/><location startLine="98" startOffset="8" endLine="109" endOffset="60"/></Target><Target id="@+id/rbRESERVED" view="RadioButton"><Expressions/><location startLine="110" startOffset="8" endLine="122" endOffset="60"/></Target><Target id="@+id/btnDisable" view="Button"><Expressions/><location startLine="137" startOffset="8" endLine="149" endOffset="38"/></Target><Target id="@+id/btnOk" view="Button"><Expressions/><location startLine="151" startOffset="8" endLine="161" endOffset="38"/></Target><Target id="@+id/cb_filter" view="CheckBox"><Expressions/><location startLine="163" startOffset="8" endLine="172" endOffset="46"/></Target><Target id="@+id/btSet" view="Button"><Expressions/><location startLine="173" startOffset="8" endLine="177" endOffset="36"/></Target></Targets></Layout>