<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:scrollbars="vertical">

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#AAAAAA"
            android:text="@string/uhf_title_filter" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/rectangle_bg"
            android:orientation="vertical"
            android:padding="8dp">

            <CheckBox
                android:id="@+id/cb_filter_wt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/button_enable"
                android:textColor="@drawable/check_text_color"
                android:textSize="19sp" />

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/tvPtr" />

                <EditText
                    android:id="@+id/etPtr_filter_wt"
                    android:layout_width="60dp"
                    android:layout_height="wrap_content"
                    android:inputType="numberSigned"
                    android:text="32" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="20dp"
                    android:text="(bit)" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="长度" />

                <EditText
                    android:id="@+id/etLen_filter_wt"
                    android:layout_width="60dp"
                    android:layout_height="wrap_content"
                    android:inputType="numberSigned"
                    android:text="0" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="(bit)" />
            </LinearLayout>
            <!--dd-->
            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/tvData_Read" />

                <EditText
                    android:id="@+id/etData_filter_wt"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:hint=""
                    android:inputType="textNoSuggestions"
                    android:singleLine="true" />
            </LinearLayout>

            <RadioGroup
                android:layout_width="match_parent"
                android:layout_height="wrap_content"

                android:orientation="horizontal">

                <RadioButton
                    android:id="@+id/rbEPC_filter_wt"
                    android:layout_width="0dp"
                    android:layout_height="30dp"
                    android:layout_weight="1"
                    android:background="@drawable/rb_bg"
                    android:button="@null"
                    android:checked="true"
                    android:gravity="center"
                    android:text="EPC"
                    android:textColor="@drawable/check_text_color" />

                <RadioButton
                    android:id="@+id/rbTID_filter_wt"
                    android:layout_width="0dp"
                    android:layout_height="30dp"
                    android:layout_marginLeft="10dp"
                    android:layout_weight="1"
                    android:background="@drawable/rb_bg"
                    android:button="@null"
                    android:checked="false"
                    android:gravity="center"
                    android:text="TID"
                    android:textColor="@drawable/check_text_color" />

                <RadioButton
                    android:id="@+id/rbUser_filter_wt"
                    android:layout_width="0dp"
                    android:layout_height="30dp"
                    android:layout_marginLeft="10dp"
                    android:layout_weight="1"
                    android:background="@drawable/rb_bg"
                    android:button="@null"
                    android:checked="false"
                    android:gravity="center"
                    android:text="USER"
                    android:textColor="@drawable/check_text_color" />

            </RadioGroup>
            <!---->
        </LinearLayout>


        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:padding="5dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tvBank"
                android:textSize="16sp" />

            <Spinner
                android:id="@+id/SpinnerBank_Write"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:entries="@array/arrayBank" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:padding="5dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tvPtr"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/EtPtr_Write"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:inputType="numberSigned"
                android:text="0"
                android:textColor="@color/red1"
                android:textSize="15sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="10dp"
                android:text="(word)" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tvLen"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/EtLen_Write"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:inputType="numberSigned"
                android:text="4"
                android:textColor="@color/red1"
                android:textSize="15sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="(word)" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:padding="5dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tvAccessPwd"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/EtAccessPwd_Write"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:hint="00000000"
                android:textSize="15sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:padding="5dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tvData_Write"
                android:textSize="16sp" />

            <!-- text="59428BF857CE66F95BB657615F204E092020433246206E633535202020202020674E56DB2020674E4E94516D674E4E03516B34303030323031353036313031B7" -->
            <EditText
                android:id="@+id/EtData_Write"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:hint="@string/inputData"
                android:text=""
                android:textSize="15sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content">

            <Button
                android:id="@+id/BtWrite"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:layout_weight="1"
                android:background="@drawable/button_bg"
                android:text="@string/btWrite"
                android:textColor="@color/white" />
        </LinearLayout>
    </LinearLayout>

</ScrollView>