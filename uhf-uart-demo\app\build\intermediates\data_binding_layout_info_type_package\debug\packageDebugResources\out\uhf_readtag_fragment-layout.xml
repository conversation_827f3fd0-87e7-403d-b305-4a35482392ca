<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="uhf_readtag_fragment" modulePackage="com.example.uhf" filePath="app\src\main\res\layout\uhf_readtag_fragment.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/uhf_readtag_fragment_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="471" endOffset="14"/></Target><Target id="@+id/RgInventory" view="RadioGroup"><Expressions/><location startLine="6" startOffset="4" endLine="39" endOffset="16"/></Target><Target id="@+id/cbFilter" view="CheckBox"><Expressions/><location startLine="13" startOffset="8" endLine="18" endOffset="53"/></Target><Target id="@+id/RbInventorySingle" view="RadioButton"><Expressions/><location startLine="20" startOffset="8" endLine="28" endOffset="37"/></Target><Target id="@+id/RbInventoryLoop" view="RadioButton"><Expressions/><location startLine="30" startOffset="8" endLine="38" endOffset="37"/></Target><Target id="@+id/layout_filter" view="LinearLayout"><Expressions/><location startLine="41" startOffset="4" endLine="221" endOffset="18"/></Target><Target id="@+id/etPtr" view="EditText"><Expressions/><location startLine="58" startOffset="12" endLine="64" endOffset="35"/></Target><Target id="@+id/etLen" view="EditText"><Expressions/><location startLine="77" startOffset="12" endLine="83" endOffset="34"/></Target><Target id="@+id/etData" view="EditText"><Expressions/><location startLine="100" startOffset="12" endLine="106" endOffset="43"/></Target><Target id="@+id/rbEPC" view="RadioButton"><Expressions/><location startLine="114" startOffset="12" endLine="124" endOffset="64"/></Target><Target id="@+id/rbTID" view="RadioButton"><Expressions/><location startLine="126" startOffset="12" endLine="137" endOffset="64"/></Target><Target id="@+id/rbUser" view="RadioButton"><Expressions/><location startLine="139" startOffset="12" endLine="150" endOffset="64"/></Target><Target id="@+id/rbRESERVED" view="RadioButton"><Expressions/><location startLine="152" startOffset="12" endLine="164" endOffset="43"/></Target><Target id="@+id/btnDisable" view="Button"><Expressions/><location startLine="178" startOffset="12" endLine="190" endOffset="43"/></Target><Target id="@+id/btnOk" view="Button"><Expressions/><location startLine="192" startOffset="12" endLine="202" endOffset="43"/></Target><Target id="@+id/cb_filter" view="CheckBox"><Expressions/><location startLine="204" startOffset="12" endLine="213" endOffset="43"/></Target><Target id="@+id/btSet" view="Button"><Expressions/><location startLine="215" startOffset="12" endLine="219" endOffset="35"/></Target><Target id="@+id/layout12" view="LinearLayout"><Expressions/><location startLine="228" startOffset="4" endLine="271" endOffset="18"/></Target><Target id="@+id/cbPhase" view="CheckBox"><Expressions/><location startLine="235" startOffset="8" endLine="240" endOffset="37"/></Target><Target id="@+id/BtInventory" view="Button"><Expressions/><location startLine="242" startOffset="8" endLine="250" endOffset="46"/></Target><Target id="@+id/BtClear" view="Button"><Expressions/><location startLine="252" startOffset="8" endLine="260" endOffset="37"/></Target><Target id="@+id/BtUpload" view="Button"><Expressions/><location startLine="262" startOffset="8" endLine="270" endOffset="37"/></Target><Target id="@+id/llContinuous" view="LinearLayout"><Expressions/><location startLine="307" startOffset="4" endLine="366" endOffset="18"/></Target><Target id="@+id/tvContinuous" view="TextView"><Expressions/><location startLine="315" startOffset="8" endLine="320" endOffset="42"/></Target><Target id="@+id/etTime" view="EditText"><Expressions/><location startLine="322" startOffset="8" endLine="331" endOffset="42"/></Target><Target id="@+id/tvMS" view="TextView"><Expressions/><location startLine="333" startOffset="8" endLine="339" endOffset="42"/></Target><Target id="@+id/tvTime" view="TextView"><Expressions/><location startLine="349" startOffset="8" endLine="355" endOffset="45"/></Target><Target id="@+id/cbEPC_Tam" view="CheckBox"><Expressions/><location startLine="357" startOffset="8" endLine="364" endOffset="39"/></Target><Target id="@+id/layout4" view="LinearLayout"><Expressions/><location startLine="383" startOffset="12" endLine="456" endOffset="26"/></Target><Target id="@+id/tv_count" view="TextView"><Expressions/><location startLine="405" startOffset="20" endLine="414" endOffset="50"/></Target><Target id="@+id/tv_total" view="TextView"><Expressions/><location startLine="422" startOffset="20" endLine="430" endOffset="50"/></Target><Target id="@+id/LvTags" view="ListView"><Expressions/><location startLine="463" startOffset="12" endLine="467" endOffset="49"/></Target></Targets></Layout>