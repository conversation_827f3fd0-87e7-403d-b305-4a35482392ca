1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.uhf"
4    android:versionCode="2"
5    android:versionName="1.4.5" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="33" />
10
11    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
11-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:7:5-80
11-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:7:22-77
12    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
12-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:8:5-81
12-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:8:22-78
13    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
13-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:9:5-82
13-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:9:22-79
14
15    <!-- Network permissions for upload service -->
16    <uses-permission android:name="android.permission.INTERNET" />
16-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:12:5-67
16-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:12:22-64
17    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
17-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:13:5-79
17-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:13:22-76
18    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
18-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:14:5-76
18-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:14:22-73
19    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
19-->[DeviceAPI_ver20250209_release.aar] C:\Users\<USER>\.gradle\caches\8.13\transforms\85fcb8e4536dcbd65732e06ceb6fc6c9\transformed\DeviceAPI_ver20250209_release\AndroidManifest.xml:12:5-74
19-->[DeviceAPI_ver20250209_release.aar] C:\Users\<USER>\.gradle\caches\8.13\transforms\85fcb8e4536dcbd65732e06ceb6fc6c9\transformed\DeviceAPI_ver20250209_release\AndroidManifest.xml:12:22-71
20    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
20-->[DeviceAPI_ver20250209_release.aar] C:\Users\<USER>\.gradle\caches\8.13\transforms\85fcb8e4536dcbd65732e06ceb6fc6c9\transformed\DeviceAPI_ver20250209_release\AndroidManifest.xml:13:5-76
20-->[DeviceAPI_ver20250209_release.aar] C:\Users\<USER>\.gradle\caches\8.13\transforms\85fcb8e4536dcbd65732e06ceb6fc6c9\transformed\DeviceAPI_ver20250209_release\AndroidManifest.xml:13:22-73
21    <uses-permission android:name="android.permission.WAKE_LOCK" />
21-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
21-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:22-65
22    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
22-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
22-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
23    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
23-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
23-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:22-74
24
25    <permission
25-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\33498c78bdd4f4128ef0bb8451a88732\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
26        android:name="com.example.uhf.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
26-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\33498c78bdd4f4128ef0bb8451a88732\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
27        android:protectionLevel="signature" />
27-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\33498c78bdd4f4128ef0bb8451a88732\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
28
29    <uses-permission android:name="com.example.uhf.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
29-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\33498c78bdd4f4128ef0bb8451a88732\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
29-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\33498c78bdd4f4128ef0bb8451a88732\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
30
31    <application
31-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:16:5-45:19
32        android:allowBackup="true"
32-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:17:9-35
33        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
33-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\33498c78bdd4f4128ef0bb8451a88732\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
34        android:debuggable="true"
35        android:extractNativeLibs="false"
36        android:icon="@drawable/ic_launcher"
36-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:18:9-45
37        android:label="@string/app_name"
37-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:19:9-41
38        android:theme="@style/CustomTheme" >
38-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:20:9-43
39        <activity
39-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:21:9-31:20
40            android:name="com.example.uhf.activity.UHFMainActivity"
40-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:22:13-68
41            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
41-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:23:13-87
42            android:exported="true"
42-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:24:13-36
43            android:label="@string/app_name" >
43-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:25:13-45
44            <intent-filter>
44-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:26:13-30:29
45                <action android:name="android.intent.action.MAIN" />
45-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:27:17-69
45-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:27:25-66
46
47                <category android:name="android.intent.category.LAUNCHER" />
47-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:29:17-77
47-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:29:27-74
48            </intent-filter>
49        </activity>
50
51        <receiver
51-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:32:9-39:20
52            android:name="com.example.uhf.BootBroadcastReceiver"
52-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:33:13-65
53            android:exported="true" >
53-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:34:13-36
54            <intent-filter android:priority="1000" >
54-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:35:13-38:29
54-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:35:28-51
55                <action android:name="android.intent.action.BOOT_COMPLETED" />
55-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:36:17-79
55-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:36:25-76
56
57                <category android:name="android.intent.category.LAUNCHER" />
57-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:29:17-77
57-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:29:27-74
58            </intent-filter>
59        </receiver>
60
61        <activity
61-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:40:9-43:39
62            android:name="com.example.uhf.filebrowser.FileManagerActivity"
62-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:41:13-75
63            android:configChanges="orientation|screenSize"
63-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:42:13-59
64            android:exported="true" />
64-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:43:13-36
65
66        <provider
66-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
67            android:name="androidx.startup.InitializationProvider"
67-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:30:13-67
68            android:authorities="com.example.uhf.androidx-startup"
68-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:31:13-68
69            android:exported="false" >
69-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:32:13-37
70            <meta-data
70-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
71                android:name="androidx.work.WorkManagerInitializer"
71-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
72                android:value="androidx.startup" />
72-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
73            <meta-data
73-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eeffc068a72679eb7e0aba9e60f9e961\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
74                android:name="androidx.emoji2.text.EmojiCompatInitializer"
74-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eeffc068a72679eb7e0aba9e60f9e961\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
75                android:value="androidx.startup" />
75-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eeffc068a72679eb7e0aba9e60f9e961\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
76            <meta-data
76-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\039b0af749800a2f28736131f7639599\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
77                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
77-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\039b0af749800a2f28736131f7639599\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
78                android:value="androidx.startup" />
78-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\039b0af749800a2f28736131f7639599\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
79            <meta-data
79-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae195636ddfc82de031064e001773fef\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
80                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
80-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae195636ddfc82de031064e001773fef\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
81                android:value="androidx.startup" />
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae195636ddfc82de031064e001773fef\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
82        </provider>
83
84        <service
84-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
85            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
85-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
86            android:directBootAware="false"
86-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
87            android:enabled="@bool/enable_system_alarm_service_default"
87-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
88            android:exported="false" />
88-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
89        <service
89-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
90            android:name="androidx.work.impl.background.systemjob.SystemJobService"
90-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
91            android:directBootAware="false"
91-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
92            android:enabled="@bool/enable_system_job_service_default"
92-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
93            android:exported="true"
93-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
94            android:permission="android.permission.BIND_JOB_SERVICE" />
94-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
95        <service
95-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
96            android:name="androidx.work.impl.foreground.SystemForegroundService"
96-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
97            android:directBootAware="false"
97-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
98            android:enabled="@bool/enable_system_foreground_service_default"
98-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
99            android:exported="false" />
99-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
100
101        <receiver
101-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
102            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
102-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
103            android:directBootAware="false"
103-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
104            android:enabled="true"
104-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
105            android:exported="false" />
105-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
106        <receiver
106-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
107            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
107-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
108            android:directBootAware="false"
108-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
109            android:enabled="false"
109-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
110            android:exported="false" >
110-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
111            <intent-filter>
111-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
112                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
112-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
112-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
113                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
113-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
113-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
114            </intent-filter>
115        </receiver>
116        <receiver
116-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
117            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
117-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
118            android:directBootAware="false"
118-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
119            android:enabled="false"
119-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
120            android:exported="false" >
120-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
121            <intent-filter>
121-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
122                <action android:name="android.intent.action.BATTERY_OKAY" />
122-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
122-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
123                <action android:name="android.intent.action.BATTERY_LOW" />
123-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
123-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
124            </intent-filter>
125        </receiver>
126        <receiver
126-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
127            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
127-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
128            android:directBootAware="false"
128-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
129            android:enabled="false"
129-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
130            android:exported="false" >
130-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
131            <intent-filter>
131-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
132                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
132-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
132-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
133                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
133-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
133-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
134            </intent-filter>
135        </receiver>
136        <receiver
136-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
137            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
137-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
138            android:directBootAware="false"
138-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
139            android:enabled="false"
139-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
140            android:exported="false" >
140-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
141            <intent-filter>
141-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
142                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
142-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
142-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
143            </intent-filter>
144        </receiver>
145        <receiver
145-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
146            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
146-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
147            android:directBootAware="false"
147-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
148            android:enabled="false"
148-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
149            android:exported="false" >
149-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
150            <intent-filter>
150-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
151                <action android:name="android.intent.action.BOOT_COMPLETED" />
151-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:36:17-79
151-->C:\Maspects\ChainWayC72\uhf-uart-demo\app\src\main\AndroidManifest.xml:36:25-76
152                <action android:name="android.intent.action.TIME_SET" />
152-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
152-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
153                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
153-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
153-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
154            </intent-filter>
155        </receiver>
156        <receiver
156-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
157            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
157-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
158            android:directBootAware="false"
158-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
159            android:enabled="@bool/enable_system_alarm_service_default"
159-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
160            android:exported="false" >
160-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
161            <intent-filter>
161-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
162                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
162-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
162-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
163            </intent-filter>
164        </receiver>
165        <receiver
165-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
166            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
166-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
167            android:directBootAware="false"
167-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
168            android:enabled="true"
168-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
169            android:exported="true"
169-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
170            android:permission="android.permission.DUMP" >
170-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
171            <intent-filter>
171-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
172                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
172-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
172-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d2fd2cfb34b70e2286ceb9b26d8ada\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
173            </intent-filter>
174        </receiver>
175
176        <uses-library
176-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab420cf1890459faaedc439daf35c55\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
177            android:name="androidx.window.extensions"
177-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab420cf1890459faaedc439daf35c55\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
178            android:required="false" />
178-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab420cf1890459faaedc439daf35c55\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
179        <uses-library
179-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab420cf1890459faaedc439daf35c55\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
180            android:name="androidx.window.sidecar"
180-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab420cf1890459faaedc439daf35c55\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
181            android:required="false" />
181-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab420cf1890459faaedc439daf35c55\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
182
183        <receiver
183-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae195636ddfc82de031064e001773fef\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
184            android:name="androidx.profileinstaller.ProfileInstallReceiver"
184-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae195636ddfc82de031064e001773fef\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
185            android:directBootAware="false"
185-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae195636ddfc82de031064e001773fef\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
186            android:enabled="true"
186-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae195636ddfc82de031064e001773fef\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
187            android:exported="true"
187-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae195636ddfc82de031064e001773fef\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
188            android:permission="android.permission.DUMP" >
188-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae195636ddfc82de031064e001773fef\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
189            <intent-filter>
189-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae195636ddfc82de031064e001773fef\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
190                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
190-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae195636ddfc82de031064e001773fef\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
190-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae195636ddfc82de031064e001773fef\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
191            </intent-filter>
192            <intent-filter>
192-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae195636ddfc82de031064e001773fef\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
193                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
193-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae195636ddfc82de031064e001773fef\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
193-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae195636ddfc82de031064e001773fef\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
194            </intent-filter>
195            <intent-filter>
195-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae195636ddfc82de031064e001773fef\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
196                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
196-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae195636ddfc82de031064e001773fef\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
196-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae195636ddfc82de031064e001773fef\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
197            </intent-filter>
198            <intent-filter>
198-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae195636ddfc82de031064e001773fef\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
199                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
199-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae195636ddfc82de031064e001773fef\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
199-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae195636ddfc82de031064e001773fef\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
200            </intent-filter>
201        </receiver>
202
203        <service
203-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0da4892eac7fab6ffcdcdc144c1dc68c\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
204            android:name="androidx.room.MultiInstanceInvalidationService"
204-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0da4892eac7fab6ffcdcdc144c1dc68c\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
205            android:directBootAware="true"
205-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0da4892eac7fab6ffcdcdc144c1dc68c\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
206            android:exported="false" />
206-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0da4892eac7fab6ffcdcdc144c1dc68c\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
207    </application>
208
209</manifest>
