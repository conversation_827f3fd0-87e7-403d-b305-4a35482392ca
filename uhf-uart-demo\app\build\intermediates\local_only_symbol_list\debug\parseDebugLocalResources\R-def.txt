R_DEF: Internal format may change without notice
local
anim grow_from_topleft_to_bottomright
anim slide_left_in
anim slide_left_out
anim slide_right_in
anim slide_right_out
array action1
array action2
array allcls
array allicon
array allname
array arrayAntiQ
array arrayBank
array arrayBank2
array arrayBase
array arrayBaud
array arrayChannelSpc0
array arrayChannelSpc1
array arrayCheck
array arrayDesFirePwdMode
array arrayDesFirePwdNum
array arrayFreHop
array arrayFreHop_us
array arrayFringeprintBaud
array arrayHop
array arrayInventoried
array arrayKeyType
array arrayLF
array arrayLink
array arrayLinkValue
array arrayLock
array arrayMemoryBank
array arrayMemoryBankValue
array arrayMode
array arrayNum
array arrayOption_Read
array arrayOption_Write
array arrayPSAMBaud
array arrayPacketSize
array arrayPower
array arrayPower2
array arrayProtocol
array arrayReadLock
array arraySession
array arrayTagType
array arrayThreshold
array arrayUart
array arrayUartBaud
array arrayVoltage
array c4000cls
array c4000icon
array c4000name
array colors
array config
array country_codes
array defaultcls
array defaulticon
array defaultname
array desfire_array_encry
array desfire_array_hex
array fileEndingAudio
array fileEndingImage
array fileEndingPackage
array fileEndingVideo
array fileEndingWebText
array i760cls
array i760icon
array i760name
array storageArea
array userAreaNumbers
attr? center_image
attr? exampleColor
attr? exampleDimension
attr? exampleDrawable
attr? exampleString
attr? image_height
attr? image_width
color black
color black1
color blue
color blue1
color blue2
color contents_text
color deep_gray
color encode_view
color full_transparent
color gold
color gray
color gray1
color gray2
color gray3
color grayslate
color graywhite
color green
color green1
color green2
color green3
color half_transparent
color help_button_view
color help_view
color lemonyellow
color lfile_colorPrimary
color lightblue
color lightblue2
color listitem_black
color listitem_gray
color orange
color pink
color possible_result_points
color purple
color red
color red1
color red2
color red3
color red4
color red5
color red_qian
color result_image_border
color result_minor_text
color result_points
color result_text
color result_view
color sbc_header_text
color sbc_header_view
color sbc_layout_view
color sbc_list_item
color sbc_page_number_text
color sbc_snippet_text
color share_text
color sliding_menu_back
color sliding_menu_item
color sliding_menu_item_pressed
color sliding_menu_item_text_color
color sliding_menu_item_text_color_pressed
color sliding_menu_title_text_color
color status_text
color text_gray
color transparent
color viewfinder_frame
color viewfinder_laser
color viewfinder_mask
color white
color white1
color yellow
dimen activity_horizontal_margin
dimen activity_vertical_margin
dimen half_padding
dimen key_height
dimen list_padding
dimen network_list_address_size
dimen network_list_channel_width
dimen network_list_default_size
dimen network_list_description_size
dimen network_list_features_size
dimen network_list_proxy_size
dimen network_list_small_padding
dimen network_list_ssid_size
dimen network_list_state_width
dimen shadow_width
dimen slidingmenu_offset
dimen standard_padding
dimen text_size_10
dimen text_size_11
dimen text_size_12
dimen text_size_13
dimen text_size_14
dimen text_size_15
dimen text_size_16
dimen text_size_17
dimen text_size_18
dimen text_size_19
dimen text_size_20
dimen text_size_21
dimen text_size_22
dimen text_size_23
dimen text_size_24
dimen text_size_25
dimen text_size_26
dimen text_size_27
dimen text_size_28
dimen text_size_29
dimen text_size_30
dimen text_size_31
dimen text_size_32
dimen text_size_7
dimen text_size_8
dimen text_size_9
drawable actionbar_back
drawable audio
drawable button_bg
drawable button_bg2
drawable button_bg3
drawable button_bg_down
drawable button_bg_down2
drawable button_bg_down3
drawable button_bg_gray
drawable button_bg_up
drawable button_bg_up2
drawable button_bg_up3
drawable card_normal
drawable card_press
drawable check_text_color
drawable check_text_color2
drawable close
drawable folder
drawable goroot
drawable ic_launcher
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable image
drawable item_list_goods_show_list_bg
drawable menu_bg_selected
drawable packed
drawable paste
drawable phone
drawable pop_menu_bg
drawable rb_bg
drawable rectangle_bg
drawable rectangle_bg2
drawable seekbar_bg
drawable shape_seekbar_circle
drawable text
drawable triangle
drawable uponelevel
drawable video
drawable webtext
id BtClear
id BtInventory
id BtRead
id BtUpload
id BtWrite
id EtAccessPwd
id EtAccessPwd_Lock
id EtAccessPwd_Write
id EtAccessPwd_deactivate
id EtData
id EtData_Write
id EtLen
id EtLen_Write
id EtPtr
id EtPtr_Write
id EtRange
id Etcmd
id LvTags
id RbInventoryLoop
id RbInventorySingle
id RgInventory
id SpinnerAgreement
id SpinnerBank
id SpinnerBank_T
id SpinnerBank_Write
id SpinnerReadLock
id TvPhase
id TvTagCount
id TvTagRssi
id TvTagUii
id UHF_ver
id _centerImage
id _labelPanelView
id _llSearchRange
id _radarBackgroundView
id action_Deactivate
id action_kill
id action_lock
id action_set
id btOK
id btRadarStart
id btRadarStop
id btSet
id btStart
id btStop
id btnBrowser
id btnDisable
id btnFactoryReset
id btnGBLock
id btnGetFastInventory
id btnGetFrequency
id btnGetLinkParams
id btnGetMemoryBank
id btnGetPower
id btnGetSession
id btnKill
id btnLock
id btnOK
id btnOk
id btnSaveSettings
id btnSetFastInventory
id btnSetFreHop
id btnSetFrequency
id btnSetLinkParams
id btnSetMemoryBank
id btnSetPower
id btnSetProtocol
id btnSetSession
id btnTestConnection
id btnUpgrade
id btn_deactivate
id btn_light_continuous
id btn_light_single
id cbAccess
id cbAutoUpload
id cbBlock1
id cbBlock10
id cbBlock11
id cbBlock12
id cbBlock13
id cbBlock14
id cbBlock15
id cbBlock16
id cbBlock2
id cbBlock3
id cbBlock4
id cbBlock5
id cbBlock6
id cbBlock7
id cbBlock8
id cbBlock9
id cbEPC
id cbEPC_Tam
id cbFastID
id cbFilter
id cbKill
id cbPerm
id cbPhase
id cbTagFocus
id cbTid
id cbUser
id cb_filter
id cb_filter_2
id cb_filter_deact
id cb_filter_lock
id cb_filter_wt
id cb_light_filter
id decode
id decode_failed
id decode_succeeded
id epcLayout
id etApiKey
id etData
id etData_filter
id etData_filter_deact
id etData_filter_lock
id etData_filter_perm
id etData_filter_wt
id etData_light_filter
id etEPC
id etGBAccessPwd
id etLen
id etLen_filter
id etLen_filter_deact
id etLen_filter_lock
id etLen_filter_perm
id etLen_filter_wt
id etLen_light_filter
id etLength
id etLockCode
id etMDL
id etMSA
id etOffset
id etPtr
id etPtr_filter
id etPtr_filter_deact
id etPtr_filter_lock
id etPtr_filter_perm
id etPtr_filter_wt
id etPtr_light_filter
id etRadarEPC
id etServerUrl
id etTime
id etUploadEndpoint
id et_file
id export
id hScroller_mytabhostactivity
id iv_dismissDialog
id launch_product_query
id layout12
id layout4
id layoutUserAreaNumber
id layout_filter
id listView_frequency
id llButton
id llChart
id llContinuous
id llFilter
id llMemoryBankParams
id llSession
id ll_freHop
id maskbuf
id quit
id radarView
id rbEPC
id rbEPC_filter
id rbEPC_filter_deact
id rbEPC_filter_lock
id rbEPC_filter_perm
id rbEPC_filter_wt
id rbEPC_light_filter
id rbFastInventoryClose
id rbFastInventoryOpen
id rbLock
id rbOpen
id rbRESERVED
id rbTID
id rbTID_filter
id rbTID_filter_deact
id rbTID_filter_lock
id rbTID_filter_perm
id rbTID_filter_wt
id rbTID_light_filter
id rbUser
id rbUser_filter
id rbUser_filter_deact
id rbUser_filter_lock
id rbUser_filter_perm
id rbUser_filter_wt
id rbUser_light_filter
id rb_America
id rb_Europe
id rb_Others
id rb_china
id rb_ex10
id rb_uhf_module
id realtabcontent
id restart_preview
id return_scan_result
id rgFileType
id seekBarPower
id spFreHop
id spFrequency
id spGBAction
id spGBConfig
id spGBStorageArea
id spGBUserAreaNumber
id spInventoried
id spMemoryBank
id spPower
id spSessionID
id splinkParams
id tv
id tvConnectionStatus
id tvContinuous
id tvData
id tvDeviceId
id tvLastUpload
id tvMS
id tvMsg
id tvSearchRange
id tvSpeed
id tvTime
id tvUploadStats
id tv_count
id tv_total
integer num_cols
layout activity_main
layout activity_test
layout fragment_blank
layout fragment_block_permalock
layout fragment_block_write
layout fragment_deactivate
layout fragment_main
layout fragment_temperature
layout fragment_uhf_radar_location
layout fragment_uhf_set
layout fragment_uhflocation
layout fragment_uhfupgrade
layout fragment_upload_settings
layout item_text1
layout listtag_items
layout popwindow_filter
layout rardar_view
layout uhf_dialog_frequency
layout uhf_dialog_lock_code
layout uhf_kill_fragment
layout uhf_light_fragment
layout uhf_lock_fragment
layout uhf_read_write_fragment
layout uhf_readtag_fragment
menu main
mipmap ic_launcher
mipmap ic_launcher_round
raw barcodebeep
raw serror
string America
string China
string China_Standard_840_845MHz
string China_Standard_920_925MHz
string Clear
string EPC_TID
string EPC_TID_off
string ETSI_Standard
string Europe
string Fixed_Frequency_915MHz
string GB_tag_lock
string HintChannelCount
string Japan
string Korea
string Morocco
string New_Zealand
string OAUTH_AccessToken_ACCESS
string OAUTH_AccessToken_ERROR
string OAUTH_AccessToken_SXPIRED
string OAUTH_ERROR
string OAUTH_RequestToken_ACCESS
string OAUTH_RequestToken_ERROR
string Others
string Print
string PrintContent
string R2000_set
string South_Africa_915_919MHz
string United_States_Standard
string Weibo_Message_LONG
string Weibo_Message_NULL
string Weibo_Share_Error
string Weibo_Share_Repeat
string Weibo_Share_Success
string action
string action_about
string action_back
string action_display
string action_fingerprint_ver
string action_psam_upgrader
string action_psam_ver
string action_rfid_upgrader
string action_rfid_ver
string action_set
string action_settings
string action_uhf_ver
string ap_dialog_title
string ap_list_header_channel
string ap_list_header_level
string ap_list_header_ssid
string ap_list_header_state
string ap_title_bssid
string ap_title_goon
string ap_title_ip
string ap_title_pause
string ap_title_pwd
string ap_title_security
string ap_title_ssid
string ap_title_wifi_size
string app_error
string app_error_message
string app_menu_surelogout
string app_msg_Upgrade
string app_msg_exit_confirm
string app_msg_init
string app_name
string app_name_enhanced
string app_picker_name
string app_run_code_error
string app_title
string b14443_msg_cmd_data
string b14443_msg_data
string b14443_msg_init_err
string b14443_msg_num
string b14443_msg_rats
string b14443_msg_reset_err
string b14443_msg_rev
string b14443_msg_send_err
string b14443_msg_uid
string b14443_msg_uid_err
string b14443_title_auto
string b14443_title_cmd
string b14443_title_getid
string b14443_title_init
string b14443_title_rev
string b14443_title_send
string b14443_title_stop
string battery_msg_background_monitoring
string battery_msg_runing
string battery_tips_HEALTH_DEAD
string battery_tips_HEALTH_GOOD
string battery_tips_HEALTH_OVERHEAT
string battery_tips_HEALTH_OVER_VOLTAGE
string battery_tips_HEALTH_UNKNOWN
string battery_tips_NOT_CHARGING
string battery_tips_STATUS_CHARGING
string battery_tips_STATUS_DISCHARGING
string battery_tips_STATUS_FULL
string battery_tips_STATUS_NOT_CHARGING
string battery_tips_STATUS_UNKNOWN
string battery_tips_extra_dl
string battery_tips_extra_level
string battery_tips_level
string battery_tips_temperature
string battery_tips_voltage
string battery_title_rb_cust
string battery_title_rb_def
string battery_title_tips
string bd_msg_Locate_stop
string bd_msg_Locate_succ
string bd_msg_Locateing
string bd_msg_title_satellite_signal
string bd_title_Altitude
string bd_title_Latitude
string bd_title_Longitude
string bd_title_SatelliteCount
string bd_title_Time
string bd_title_bd
string bd_title_bdStatus
string bd_title_cold
string bd_title_gb
string bd_title_gps
string bd_title_hot
string bd_title_warm
string bluetooth_btn_no
string bluetooth_btn_yes
string bluetooth_msg_connection_failed
string bluetooth_msg_not_adapter
string bluetooth_msg_not_supp
string bluetooth_title_file
string bluetooth_title_tip
string bookmark_picker_name
string btAddD
string btAddI
string btAdd_Select
string btBack1
string btCalRange
string btCancel
string btChoose_Select
string btClear
string btCreateCode
string btDel_Select
string btEnter
string btErase
string btGetFre
string btGetPrar
string btInventory
string btKill
string btLock
string btRead
string btReadUii
string btRead_Select
string btReduceD
string btReduceI
string btSetFre
string btSetFreHop
string btSetPrar
string btSetProtocol
string btStop
string btUii
string btWrite
string bt_not_enabled_leaving
string btn_light_continuous
string btn_light_single
string btn_light_stop
string button_add_calendar
string button_add_contact
string button_back
string button_book_search
string button_cancel
string button_custom_product_search
string button_dial
string button_disable
string button_done
string button_email
string button_enable
string button_get_directions
string button_google_shopper
string button_mms
string button_ok
string button_open_browser
string button_product_search
string button_scan
string button_search_book_contents
string button_share_app
string button_share_bookmark
string button_share_by_email
string button_share_by_sms
string button_share_clipboard
string button_share_contact
string button_show_map
string button_sms
string button_web_search
string button_wifi
string camera_alert
string camera_none
string cancel
string cancle
string choose_image
string ckAnti_Read
string ckLoop_Read
string ckSecond
string ckWithUii
string clear
string clearwords
string close
string configure
string contents_contact
string contents_email
string contents_location
string contents_phone
string contents_sms
string contents_text
string current_dir
string default_font_gray_scale
string delete_blog
string delete_image
string delete_tweet
string desfire_ms_filesize_not_null
string desfire_ms_not_null
string desfire_ms_pwd_not_null
string desfire_ms_unknown_file
string desfire_msg_comm_setting_must_len_1
string desfire_msg_file_no_less_1
string desfire_msg_file_num_must_len_1
string desfire_msg_get_apps_fail
string desfire_msg_get_files_fail
string desfire_msg_get_info_fail
string desfire_msg_id_must_len_3
string desfire_msg_operator_fail
string desfire_msg_operator_succ
string desfire_msg_pwd_not_hex
string desfire_msg_pwd_vail_fail
string desfire_title_add_app
string desfire_title_add_del_pwd
string desfire_title_add_file
string desfire_title_app_add_del_pwd
string desfire_title_app_change_pwd
string desfire_title_app_change_pwd_permission
string desfire_title_app_id
string desfire_title_app_max_key
string desfire_title_app_oper_pwd
string desfire_title_app_properties
string desfire_title_appid_input
string desfire_title_back
string desfire_title_card_click_tip
string desfire_title_card_properties
string desfire_title_change_pwd
string desfire_title_comm_setting
string desfire_title_create
string desfire_title_dialog_tips
string desfire_title_dialog_tips_msg
string desfire_title_file
string desfire_title_file_id
string desfire_title_file_num
string desfire_title_file_size
string desfire_title_format
string desfire_title_get_card_version
string desfire_title_get_version
string desfire_title_link_encry
string desfire_title_modify
string desfire_title_oper_pwd
string desfire_title_properties
string desfire_title_pwd
string desfire_title_pwd_chg
string desfire_title_pwd_pro_chg
string desfire_title_pwd_tip
string desfire_title_read_permission
string desfire_title_readwrite_permission
string desfire_title_scan
string desfire_title_sel_app_fail
string desfire_title_sta_file
string desfire_title_update_permission
string desfire_title_value_balance
string desfire_title_value_credit
string desfire_title_value_curr
string desfire_title_value_debit
string desfire_title_value_file
string desfire_title_value_max
string desfire_title_value_min
string desfire_title_write_permission
string download_msg_cancel
string download_msg_cancel_down
string download_msg_cancel_upload
string download_msg_close
string download_msg_confirm
string download_msg_down_confirm
string download_msg_down_fail
string download_msg_down_file
string download_msg_down_file_size
string download_msg_down_report
string download_msg_down_succ
string download_msg_downing
string download_msg_file_path_not_exist
string download_msg_sdcard_not_exist
string download_msg_total_time
string download_msg_up_fail
string download_msg_up_succ
string download_msg_uploading
string download_title
string er_dsoft_Set_fail
string er_dsoft_Set_succ
string er_dsoft_get_Get
string er_dsoft_get_ParamNum
string er_dsoft_get_ParamVal
string er_dsoft_get_Set
string er_dsoft_get_img_data_fail
string er_dsoft_get_img_succ
string er_dsoft_tab_pic
string er_dsoft_tab_scan
string er_dsoft_tab_set
string erd_title
string exit
string export
string factory_reset
string fastID
string fastID_off
string file_btn_cancel
string file_btn_confirm
string file_msg_cancel
string file_title_sel_file
string file_title_sel_file_confirm
string fingerprint_btn_Vfy_PSW
string fingerprint_btn_clear
string fingerprint_btn_get_Baudrate
string fingerprint_btn_get_PacketSize
string fingerprint_btn_get_Threshold
string fingerprint_btn_identification
string fingerprint_btn_import
string fingerprint_btn_reinit
string fingerprint_btn_save
string fingerprint_btn_save_stop
string fingerprint_btn_set_Baudrate
string fingerprint_btn_set_PSW
string fingerprint_btn_set_PacketSize
string fingerprint_btn_set_Threshold
string fingerprint_dailog_msg
string fingerprint_dailog_no
string fingerprint_dailog_ok
string fingerprint_dailog_title
string fingerprint_menu_del
string fingerprint_menu_delall
string fingerprint_msg_acq_fail
string fingerprint_msg_acq_succ
string fingerprint_msg_clear_fail
string fingerprint_msg_clear_succ
string fingerprint_msg_ident_fail
string fingerprint_msg_ident_succ
string fingerprint_msg_import_fail
string fingerprint_msg_import_succ
string fingerprint_msg_init_fail
string fingerprint_msg_name_not_null
string fingerprint_msg_page_id_need_0_to_254
string fingerprint_msg_page_id_need_digits
string fingerprint_msg_page_id_not_null
string fingerprint_msg_set_Baudrate_fail
string fingerprint_msg_set_Baudrate_succ
string fingerprint_msg_set_PSW_fail
string fingerprint_msg_set_PSW_succ
string fingerprint_msg_set_PacketSize_fail
string fingerprint_msg_set_PacketSize_succ
string fingerprint_msg_set_threshold_fail
string fingerprint_msg_set_threshold_succ
string fingerprint_msg_sure_clear
string fingerprint_msg_verify_PSW_fail
string fingerprint_msg_verify_PSW_succ
string fingerprint_tab_acquisition
string fingerprint_tab_history
string fingerprint_tab_identification
string fingerprint_tab_set
string fingerprint_title_PSW
string fingerprint_title_auto_acquisition
string fingerprint_title_create
string fingerprint_title_get_img_fail
string fingerprint_title_index
string fingerprint_title_local_num
string fingerprint_title_model_num
string fingerprint_title_name
string fingerprint_title_packet_size
string fingerprint_title_page_id
string fingerprint_title_page_mode
string fingerprint_title_page_mode_1
string fingerprint_title_page_mode_2
string fingerprint_title_pageid
string fingerprint_title_score
string fingerprint_title_show_img
string fingerprint_title_show_imgiso
string fingerprint_title_threshold
string fingerprint_title_uname
string fontGrayScaleRange
string fontGrayscaleName
string font_size
string freHopType
string getQTParams_fail
string getQTParams_succ
string get_fail
string get_succ
string gps_btn_no
string gps_btn_yes
string gps_msg_Locate_stop
string gps_msg_Locate_succ
string gps_msg_Locateing
string gps_msg_gps_not_open
string gps_msg_open_gps
string gps_msg_title_satellite_signal
string gps_title_Altitude
string gps_title_GpsStatus
string gps_title_Latitude
string gps_title_Longitude
string gps_title_SatelliteCount
string gps_title_Time
string gps_title_tip
string gs_msg_not_one
string hello_blank_fragment
string hello_world
string history_clear_one_history_text
string history_clear_text
string history_email_title
string history_empty
string history_empty_detail
string history_send
string history_title
string http_exception_error
string http_status_code_error
string inputData
string io_exception_error
string kettest_msg_broadcast_tip
string keyboard_title_multiple
string keyboard_title_single
string led_action_close
string led_action_open
string led_msg_close_fail
string led_msg_open_fail
string lf_msg_data_not_hex4
string lf_msg_data_not_null
string lf_msg_data_page_id_err_em4305
string lf_msg_scan_fail
string lf_msg_write_fail
string lf_msg_write_succ
string lf_title_card_type
string lf_title_data
string lf_title_pageid
string lf_title_read
string lf_title_write
string load_empty
string load_error
string load_full
string load_ing
string load_more
string location
string location_fail
string lp_btn_auto
string lp_btn_close
string lp_btn_open
string lp_btn_stop
string lp_title_b
string lp_title_brightness
string lp_title_compass
string lp_title_g
string lp_title_gyroscope
string lp_title_light
string lp_title_lightsensor
string lp_title_psensor
string lp_title_r
string lp_title_sensor
string m1_title_block
string m1_title_key_type
string m1_title_key_value
string m1_title_multiple
string m1_title_read
string m1_title_sector
string m1_title_single
string m1_title_tag_type
string m1_title_write
string menu_encode_mecard
string menu_encode_vcard
string menu_help
string menu_history
string menu_settings
string menu_share
string ml_title_read_id
string msg_barcode_ck
string msg_barcode_set
string msg_bulk_mode_scanned
string msg_camera_framework_bug
string msg_copy_clipboard
string msg_data_hex
string msg_default_format
string msg_default_meta
string msg_default_mms_subject
string msg_default_status
string msg_default_time
string msg_default_type
string msg_disable_fail
string msg_disable_succ
string msg_domain_ip_bad
string msg_encode_contents_failed
string msg_file_format_err
string msg_free_fail
string msg_free_succ
string msg_google_books
string msg_google_product
string msg_google_shopper_missing
string msg_init_fail
string msg_init_succ
string msg_install_google_shopper
string msg_intent_failed
string msg_load_image_fail
string msg_load_is_null
string msg_load_userface_fail
string msg_loding
string msg_login_email_error
string msg_login_error
string msg_login_fail
string msg_login_logout
string msg_login_pwd_null
string msg_login_success
string msg_login_username_null
string msg_mac_change
string msg_mac_error
string msg_mac_unmatched
string msg_network_none
string msg_network_ok
string msg_network_unavailable
string msg_no_data
string msg_noaccess_delete
string msg_psam_set
string msg_read_detail_fail
string msg_reboot_complete
string msg_reboot_count
string msg_reboot_set
string msg_receive
string msg_redirect
string msg_rfid_set
string msg_sbc_book_not_searchable
string msg_sbc_failed
string msg_sbc_no_page_returned
string msg_sbc_page
string msg_sbc_results
string msg_sbc_searching_book
string msg_sbc_snippet_unavailable
string msg_sbc_unknown_page
string msg_share_explanation
string msg_share_subject_line
string msg_share_text
string msg_sure
string msg_tag_hex
string msg_unmount_usb
string myMenu
string network_msg_emergency_only
string network_msg_in_service
string network_msg_operator_is_null
string network_msg_out_of_service
string network_msg_power_off
string network_msg_sim_not_exist
string network_msg_title_mobile
string network_msg_wifi_conn
string network_msg_wifi_conn_fail
string network_msg_wifi_conning
string network_msg_wifi_level
string network_msg_wifi_not_enable
string network_msg_wifi_speed
string network_not_connected
string new_data_toast_message
string new_data_toast_none
string nfc_msg_no_device
string nfc_msg_no_open
string nfc_msg_not_support_ndef
string nfc_msg_read_only
string nfc_msg_too_long
string nfc_msg_write_exception
string nfc_msg_write_succ
string nfc_msg_write_tip
string nfc_title_reader
string nfc_title_writer
string none_found
string none_paired
string normal_set
string not_connected
string num
string ok
string ping_msg_background_ping
string ping_msg_fail
string ping_msg_finish
string ping_msg_not_null
string ping_msg_pinging
string ping_msg_runing
string ping_title
string please_on
string preferences_actions_title
string preferences_auto_focus_title
string preferences_bulk_mode_summary
string preferences_bulk_mode_title
string preferences_copy_to_clipboard_title
string preferences_custom_product_search_summary
string preferences_custom_product_search_title
string preferences_decode_1D_title
string preferences_decode_Data_Matrix_title
string preferences_decode_QR_title
string preferences_device_bug_workarounds_title
string preferences_disable_continuous_focus_summary
string preferences_disable_continuous_focus_title
string preferences_disable_exposure_title
string preferences_front_light_summary
string preferences_front_light_title
string preferences_general_title
string preferences_name
string preferences_play_beep_title
string preferences_remember_duplicates_summary
string preferences_remember_duplicates_title
string preferences_result_title
string preferences_scanning_title
string preferences_search_country
string preferences_supplemental_summary
string preferences_supplemental_title
string preferences_try_bsplus
string preferences_try_bsplus_summary
string preferences_vibrate_title
string printer_msg_low_pager
string psam_msg_fail
string psam_msg_upgrade_fail
string psam_msg_upgrade_succ
string psam_title_baud
string psam_title_card_1
string psam_title_card_2
string psam_title_clear
string psam_title_cmd
string psam_title_data
string psam_title_parameter
string psam_title_save
string psam_title_send
string psam_title_set
string psam_title_tips
string psam_title_voltage
string publishing
string rbErase
string rbInventoryAnti
string rbInventoryLoop
string rbInventorySingle
string rbLock
string rbRead
string rbWrite
string republish_tweet
string reset_fail
string reset_succ
string result_address_book
string result_calendar
string result_email_address
string result_geo
string result_isbn
string result_product
string result_sms
string result_tel
string result_text
string result_uri
string result_wifi
string rfid_mgs_error_0not_write
string rfid_mgs_error_config
string rfid_mgs_error_init
string rfid_mgs_error_lessthan12
string rfid_mgs_error_nohex
string rfid_mgs_error_nolockcode
string rfid_mgs_error_nopwd
string rfid_mgs_error_not_found
string rfid_mgs_error_not_supper_write
string rfid_mgs_error_not_write_null
string rfid_mgs_error_veri_fail
string rfid_mgs_kill_fail
string rfid_mgs_kill_succ
string rfid_mgs_killpwdtip
string rfid_mgs_lock_fail
string rfid_mgs_lock_succ
string rfid_mgs_lockpwdtip
string rfid_mgs_locktip
string rfid_msg_1byte_fail
string rfid_msg_confirm_afi
string rfid_msg_confirm_dsfid
string rfid_msg_confirm_flase
string rfid_msg_confirm_title
string rfid_msg_confirm_true
string rfid_msg_data
string rfid_msg_lock_fail
string rfid_msg_lock_succ
string rfid_msg_read_fail
string rfid_msg_read_succ
string rfid_msg_scan_continuous
string rfid_msg_scan_fail
string rfid_msg_type
string rfid_msg_uid
string rfid_msg_upgrade_fail
string rfid_msg_upgrade_succ
string rfid_msg_write_fail
string rfid_msg_write_succ
string rfid_title_continuous
string rfid_title_continuous_count
string rfid_title_continuous_read
string rfid_title_continuous_read_fail_count
string rfid_title_continuous_read_succ_count
string rfid_title_continuous_read_write
string rfid_title_continuous_write
string rfid_title_continuous_write_fail_count
string rfid_title_continuous_write_succ_count
string save
string sbc_name
string scanning
string searchRange
string second
string select
string select_device
string send
string setAgreement_fail
string setAgreement_succ
string setQTParams_fail
string setQTParams_succ
string setting_fail
string setting_succ
string share
string share_name
string sharing
string sinalogin_check_account
string sinalogin_check_pass
string sinalogin_check_server
string socket_exception_error
string start_location
string stop_location
string storageArea
string strBrowser
string strFilePath
string strPermissionDeny
string strUpgrade
string strUpload
string submit_report
string sure
string switch_cam
string tagFocus
string tagFocus_off
string tel_title_NetworkType
string tel_title_OperatorName
string tel_title_Signal
string tel_title_Status
string time_d_s
string title_07_infrared
string title_97_infrared
string title_Check
string title_Download
string title_Ping
string title_QTTag
string title_UART
string title_Upload
string title_about_android_release
string title_about_cpu
string title_about_device_kernel_version
string title_about_device_release
string title_about_display
string title_about_imei
string title_about_jar_release
string title_about_mac
string title_about_model
string title_about_ram
string title_about_sn
string title_about_storage
string title_activity_a1443
string title_activity_analog_call
string title_activity_app_set
string title_activity_app_setings
string title_activity_auto_run_network
string title_activity_battery_monitor
string title_activity_bdnav
string title_activity_bluetooth_print
string title_activity_camera
string title_activity_download
string title_activity_er_d
string title_activity_er_dsoft
string title_activity_fingerprint
string title_activity_global_set
string title_activity_gps
string title_activity_infrared
string title_activity_iso14443_a4_cpu
string title_activity_iso14443b
string title_activity_iso15693
string title_activity_key_back
string title_activity_key_home
string title_activity_key_power
string title_activity_key_test
string title_activity_lf
string title_activity_light_and_psensor
string title_activity_needle
string title_activity_network_status
string title_activity_nfc
string title_activity_ping
string title_activity_ping_pop
string title_activity_printer
string title_activity_psam
string title_activity_reboot
string title_activity_settings
string title_activity_test
string title_activity_to_service_page
string title_activity_uart
string title_activity_uhfmain
string title_activity_uhfset
string title_activity_upload
string title_activity_volum
string title_activity_yi_d
string title_analog_call_touch_off
string title_analog_call_touch_on
string title_clear
string title_connected_to
string title_connecting
string title_decode_time
string title_del
string title_elapsed_time
string title_error_count
string title_error_rate
string title_fail_count
string title_fail_rate
string title_hex
string title_id_power
string title_init_barcode
string title_ip_or_domain
string title_not_connected
string title_other_devices
string title_paired_devices
string title_ping_background
string title_ping_count
string title_ping_downloadresult
string title_ping_fail
string title_ping_packet_size
string title_ping_pingresult
string title_ping_restart
string title_ping_seconds
string title_ping_succ
string title_ping_uploadresult
string title_ping_wait
string title_power_id
string title_scan
string title_scan_between
string title_scan_count
string title_start
string title_stop
string title_stop_Inventory
string title_succ_count
string title_succ_rate
string title_wait_time
string title_work_time
string tvAccessPwd
string tvAccess_Lock
string tvBank
string tvBank2_Read
string tvBank_Data
string tvBank_Select
string tvBase
string tvChannelCount
string tvChinnelSpace
string tvChooseInfo
string tvCountOfTags
string tvCount_Data
string tvData_Data
string tvData_Read
string tvData_Write
string tvFastID
string tvFreD
string tvFreHop
string tvFreI
string tvFreRange
string tvGetUiiForOperation
string tvHop
string tvInventoryName
string tvKHz
string tvKillPwd
string tvKill_Lock
string tvLen
string tvLen2_Read
string tvLen_Data
string tvLockCode
string tvLockCode_Lock
string tvMHz
string tvMask
string tvMode
string tvOption_Read
string tvOption_Write
string tvOrder
string tvProtocol
string tvPtr
string tvPtr2_Read
string tvPtr_Data
string tvPtr_Select
string tvQ
string tvQTPublic
string tvStartFreD
string tvStartFreI
string tvStatus_Data
string tvTAndE
string tvTID_Lock
string tvTag
string tvTagCount
string tvTagFocus
string tvTagLen
string tvTagUii
string tvTagUii_Data
string tvTagUii_Kill
string tvTagUii_Lock
string tvTagUii_Read
string tvTagUii_Write
string tvUII_Lock
string tvUSER_Lock
string tv_all
string tv_phase
string tv_r2000_fun
string uhf_btn_getLinkParams
string uhf_btn_get_link
string uhf_btn_get_workwait
string uhf_btn_getpower
string uhf_btn_memoryBank
string uhf_btn_setLinkParams
string uhf_btn_set_link
string uhf_btn_setpower
string uhf_btn_workwait
string uhf_gyro_loaction
string uhf_located_angle
string uhf_locating_tag_value
string uhf_msg_addr_must_decimal
string uhf_msg_addr_must_len8
string uhf_msg_addr_not_null
string uhf_msg_export_data_empty
string uhf_msg_filter_addr_must_decimal
string uhf_msg_filter_addr_not_null
string uhf_msg_filter_data_must_hex
string uhf_msg_filter_data_not_null
string uhf_msg_filter_len_must_decimal
string uhf_msg_filter_len_not_null
string uhf_msg_get_para_fail
string uhf_msg_get_para_succ
string uhf_msg_inventory_fail
string uhf_msg_inventory_open_fail
string uhf_msg_inventory_stop_fail
string uhf_msg_len_must_decimal
string uhf_msg_len_not_null
string uhf_msg_length_error
string uhf_msg_no_Bluetooth
string uhf_msg_no_Sensor
string uhf_msg_offset_error
string uhf_msg_r2000_tip
string uhf_msg_read_data_fail
string uhf_msg_read_frequency_fail
string uhf_msg_read_frequency_succ
string uhf_msg_read_power_fail
string uhf_msg_read_power_succ
string uhf_msg_read_pwm_fail
string uhf_msg_read_tag_fail
string uhf_msg_scaning
string uhf_msg_set_fail
string uhf_msg_set_filter_fail
string uhf_msg_set_filter_fail2
string uhf_msg_set_filter_succ
string uhf_msg_set_frehop_fail
string uhf_msg_set_frehop_succ
string uhf_msg_set_frequency_fail
string uhf_msg_set_frequency_succ
string uhf_msg_set_power_fail
string uhf_msg_set_power_succ
string uhf_msg_set_protocol_fail
string uhf_msg_set_protocol_succ
string uhf_msg_set_pwm_fail
string uhf_msg_set_pwm_succ
string uhf_msg_set_succ
string uhf_msg_tab_kill
string uhf_msg_tab_light
string uhf_msg_tab_lock
string uhf_msg_tab_read_write
string uhf_msg_tab_scan
string uhf_msg_tab_set
string uhf_msg_tab_write
string uhf_msg_tag_must_not_null
string uhf_msg_upgrade_fail
string uhf_msg_upgrade_succ
string uhf_msg_write_fail
string uhf_msg_write_must_len4
string uhf_msg_write_must_len4x
string uhf_msg_write_must_not_null
string uhf_msg_write_succ
string uhf_not_support_rssi
string uhf_radar_loaction
string uhf_tag_count
string uhf_title_access
string uhf_title_dbm
string uhf_title_filter
string uhf_title_kill
string uhf_title_link
string uhf_title_lock
string uhf_title_open
string uhf_title_perm
string uhf_title_power
string uhf_title_read
string uhf_title_temp
string uhf_title_tid
string uhf_title_uii
string uhf_title_user
string uhf_title_write
string ul_title_AFI
string ul_title_DSFID
string ul_title_lock_AFI
string ul_title_lock_DSFID
string ul_title_scan
string ul_title_uid
string ul_title_write_AFI
string ul_title_write_DSFID
string up_msg_avg_speed
string up_msg_file_not_exist
string up_msg_file_size
string up_msg_net_not_conn
string up_msg_sel_file
string up_msg_start_time
string up_msg_stop_time
string up_msg_total_time
string up_one_level
string update_msg_checking
string update_msg_curr_new
string update_msg_diag_title
string update_msg_diag_title_two
string update_msg_unmount
string update_msg_updateing
string update_msg_version_info_fail
string update_title_cancel
string update_title_ok
string update_title_update_last
string update_title_update_now
string upload_title
string user_area_number
string volum_title_systen
string volum_title_voice_alarm
string volum_title_voice_call
string volum_title_voice_music
string volum_title_voice_notification
string volum_title_voice_ring
string wifi_changing_network
string wifi_ssid_label
string wifi_type_label
string word
string workTime
string xml_parser_failed
string yid_msg_scan_cancel
string yid_msg_scan_error
string yid_msg_scan_fail
string yid_msg_scan_timeout
string yid_title
string yid_title_scan_compare
string yid_title_scan_continuous
string zxing_app_name
style ActionBarBaseTheme
style ActionBarBaseTheme1
style AppBaseTheme
style AppTheme
style Base.Theme.Uhfuartdemo
style CustomTheme
style MyActionBarStyle
style MyActionBarTabStyle
style MyActionBarTabTextStyle
style MyDropDownListView
style MyDropDownNav
style Theme.Uhfuartdemo
styleable CardView exampleString exampleDimension exampleColor exampleDrawable
styleable RadarView center_image image_width image_height
xml backup_rules
xml data_extraction_rules
