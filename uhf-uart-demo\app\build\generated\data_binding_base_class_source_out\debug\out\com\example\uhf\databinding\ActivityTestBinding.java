// Generated by view binder compiler. Do not edit!
package com.example.uhf.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.uhf.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityTestBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button BtClear;

  @NonNull
  public final Button BtInventory;

  @NonNull
  public final ListView LvTags;

  @NonNull
  public final LinearLayout layout12;

  @NonNull
  public final LinearLayout layout4;

  @NonNull
  public final LinearLayout layoutFilter;

  @NonNull
  public final TextView tvCount;

  @NonNull
  public final TextView tvSpeed;

  @NonNull
  public final TextView tvTime;

  @NonNull
  public final TextView tvTotal;

  private ActivityTestBinding(@NonNull LinearLayout rootView, @NonNull Button BtClear,
      @NonNull Button BtInventory, @NonNull ListView LvTags, @NonNull LinearLayout layout12,
      @NonNull LinearLayout layout4, @NonNull LinearLayout layoutFilter, @NonNull TextView tvCount,
      @NonNull TextView tvSpeed, @NonNull TextView tvTime, @NonNull TextView tvTotal) {
    this.rootView = rootView;
    this.BtClear = BtClear;
    this.BtInventory = BtInventory;
    this.LvTags = LvTags;
    this.layout12 = layout12;
    this.layout4 = layout4;
    this.layoutFilter = layoutFilter;
    this.tvCount = tvCount;
    this.tvSpeed = tvSpeed;
    this.tvTime = tvTime;
    this.tvTotal = tvTotal;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityTestBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityTestBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_test, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityTestBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.BtClear;
      Button BtClear = ViewBindings.findChildViewById(rootView, id);
      if (BtClear == null) {
        break missingId;
      }

      id = R.id.BtInventory;
      Button BtInventory = ViewBindings.findChildViewById(rootView, id);
      if (BtInventory == null) {
        break missingId;
      }

      id = R.id.LvTags;
      ListView LvTags = ViewBindings.findChildViewById(rootView, id);
      if (LvTags == null) {
        break missingId;
      }

      id = R.id.layout12;
      LinearLayout layout12 = ViewBindings.findChildViewById(rootView, id);
      if (layout12 == null) {
        break missingId;
      }

      id = R.id.layout4;
      LinearLayout layout4 = ViewBindings.findChildViewById(rootView, id);
      if (layout4 == null) {
        break missingId;
      }

      id = R.id.layout_filter;
      LinearLayout layoutFilter = ViewBindings.findChildViewById(rootView, id);
      if (layoutFilter == null) {
        break missingId;
      }

      id = R.id.tv_count;
      TextView tvCount = ViewBindings.findChildViewById(rootView, id);
      if (tvCount == null) {
        break missingId;
      }

      id = R.id.tvSpeed;
      TextView tvSpeed = ViewBindings.findChildViewById(rootView, id);
      if (tvSpeed == null) {
        break missingId;
      }

      id = R.id.tvTime;
      TextView tvTime = ViewBindings.findChildViewById(rootView, id);
      if (tvTime == null) {
        break missingId;
      }

      id = R.id.tv_total;
      TextView tvTotal = ViewBindings.findChildViewById(rootView, id);
      if (tvTotal == null) {
        break missingId;
      }

      return new ActivityTestBinding((LinearLayout) rootView, BtClear, BtInventory, LvTags,
          layout12, layout4, layoutFilter, tvCount, tvSpeed, tvTime, tvTotal);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
