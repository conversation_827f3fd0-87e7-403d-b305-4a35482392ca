<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".fragment.UHFLightFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#AAAAAA"
            android:text="@string/uhf_title_filter" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/rectangle_bg"
            android:orientation="vertical"
            android:layout_marginHorizontal="1dp"
            android:padding="8dp">

            <CheckBox
                android:id="@+id/cb_light_filter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/button_enable"
                android:textColor="@drawable/check_text_color"
                android:textSize="19sp" />

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/tvPtr" />

                <EditText
                    android:id="@+id/etPtr_light_filter"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:inputType="numberSigned"
                    android:text="32" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="30dp"
                    android:text="(bit)" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="长度:" />

                <EditText
                    android:id="@+id/etLen_light_filter"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:inputType="numberSigned"
                    android:text="0" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="(bit)" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/tvData_Read" />

                <EditText
                    android:id="@+id/etData_light_filter"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:hint=""
                    android:inputType="textNoSuggestions"
                    android:singleLine="true" />
            </LinearLayout>

            <RadioGroup
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <RadioButton
                    android:id="@+id/rbEPC_light_filter"
                    android:layout_width="0dp"
                    android:layout_height="30dp"
                    android:layout_weight="1"
                    android:background="@drawable/rb_bg"
                    android:button="@null"
                    android:checked="true"
                    android:gravity="center"
                    android:text="EPC"
                    android:textColor="@drawable/check_text_color" />

                <RadioButton
                    android:id="@+id/rbTID_light_filter"
                    android:layout_width="0dp"
                    android:layout_height="30dp"
                    android:layout_marginLeft="10dp"
                    android:layout_weight="1"
                    android:background="@drawable/rb_bg"
                    android:button="@null"
                    android:checked="false"
                    android:gravity="center"
                    android:text="TID"
                    android:textColor="@drawable/check_text_color" />

                <RadioButton
                    android:id="@+id/rbUser_light_filter"
                    android:layout_width="0dp"
                    android:layout_height="30dp"
                    android:layout_marginLeft="10dp"
                    android:layout_weight="1"
                    android:background="@drawable/rb_bg"
                    android:button="@null"
                    android:checked="false"
                    android:gravity="center"
                    android:text="USER"
                    android:textColor="@drawable/check_text_color" />

            </RadioGroup>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="horizontal"
            android:padding="8dp">

            <Button
                android:id="@+id/btn_light_single"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/btn_light_single" />

            <Button
                android:id="@+id/btn_light_continuous"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_weight="1"
                android:text="@string/btn_light_continuous" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>