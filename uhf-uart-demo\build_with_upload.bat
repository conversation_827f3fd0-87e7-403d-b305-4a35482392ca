@echo off
echo Building UHF App with Upload Functionality
echo ==========================================

REM Set Android SDK paths
set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
set PATH=%PATH%;%ANDROID_HOME%\platform-tools;%ANDROID_HOME%\emulator

echo.
echo Step 1: Cleaning previous build...
gradlew.bat clean
if %ERRORLEVEL% neq 0 (
    echo ERROR: Clean failed
    pause
    exit /b 1
)

echo.
echo Step 2: Building debug APK...
gradlew.bat assembleDebug --stacktrace
if %ERRORLEVEL% neq 0 (
    echo ERROR: Build failed
    echo.
    echo This is expected due to R class issues.
    echo Let's use the working APK instead...
    goto :use_working_apk
)

echo.
echo Step 3: Installing new APK...
adb uninstall com.maspects.uhftool
adb install app\build\outputs\apk\debug\app-debug.apk
if %ERRORLEVEL% equ 0 (
    echo SUCCESS: New APK with upload functionality installed!
    goto :launch_app
)

:use_working_apk
echo.
echo Using working APK with upload functionality...
echo.
echo Step 3: Uninstalling current app...
adb uninstall com.maspects.uhftool
adb uninstall com.example.uhf

echo.
echo Step 4: Installing working APK...
if exist "UHF-serial_v1.4.5.apk" (
    adb install UHF-serial_v1.4.5.apk
    if %ERRORLEVEL% equ 0 (
        echo SUCCESS: Working APK installed!
    ) else (
        echo ERROR: Failed to install working APK
        pause
        exit /b 1
    )
) else (
    echo ERROR: UHF-serial_v1.4.5.apk not found!
    pause
    exit /b 1
)

:launch_app
echo.
echo Step 5: Starting test server...
if exist "test_server.py" (
    echo Starting Python test server...
    start "UHF Test Server" python test_server.py
    timeout /t 2 >nul
    echo Test server started!
) else (
    echo WARNING: test_server.py not found
)

echo.
echo Step 6: Launching app...
REM Try both package names
adb shell am start -n com.maspects.uhftool/.activity.UHFMainActivity 2>nul
if %ERRORLEVEL% neq 0 (
    adb shell am start -n com.example.uhf/.activity.UHFMainActivity 2>nul
)

echo.
echo Step 7: Getting network info...
echo.
echo Your PC IP addresses:
ipconfig | findstr IPv4
echo.

echo ========================================
echo UPLOAD FUNCTIONALITY READY!
echo ========================================
echo.
echo In the app, you should now see:
echo.
echo SCAN TAB:
echo   [Start] [Clear] [Upload] buttons
echo.
echo UPLOAD SETTINGS TAB:
echo   - Server URL: http://********:8080/upload
echo   - API Key: test-key  
echo   - Enable Auto Upload checkbox
echo   - Test Connection button
echo.
echo TESTING STEPS:
echo 1. Go to Upload Settings tab
echo 2. Configure server URL and API key
echo 3. Enable Auto Upload
echo 4. Test connection
echo 5. Go to Scan tab
echo 6. Use Upload button or scan tags for auto-upload
echo 7. Check test server console for upload data
echo.
echo Monitor app logs with:
echo   adb logcat ^| findstr "Upload\|uhftool"
echo.
echo Press any key to exit...
pause
