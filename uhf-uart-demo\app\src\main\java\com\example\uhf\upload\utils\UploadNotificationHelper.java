package com.example.uhf.upload.utils;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.os.Build;

import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;

import com.example.uhf.R;
import com.example.uhf.activity.UHFMainActivity;

/**
 * Helper class for managing upload-related notifications
 * Provides methods to show upload progress, success, and error notifications
 */
public class UploadNotificationHelper {
    
    private static final String CHANNEL_ID = "upload_channel";
    private static final String CHANNEL_NAME = "Upload Notifications";
    private static final String CHANNEL_DESCRIPTION = "Notifications for RFID data upload status";
    
    private static final int NOTIFICATION_ID_UPLOAD_PROGRESS = 1001;
    private static final int NOTIFICATION_ID_UPLOAD_SUCCESS = 1002;
    private static final int NOTIFICATION_ID_UPLOAD_ERROR = 1003;
    
    private Context context;
    private NotificationManagerCompat notificationManager;
    
    public UploadNotificationHelper(Context context) {
        this.context = context;
        this.notificationManager = NotificationManagerCompat.from(context);
        createNotificationChannel();
    }
    
    /**
     * Create notification channel for Android O and above
     */
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID,
                    CHANNEL_NAME,
                    NotificationManager.IMPORTANCE_DEFAULT
            );
            channel.setDescription(CHANNEL_DESCRIPTION);
            
            NotificationManager manager = context.getSystemService(NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(channel);
            }
        }
    }
    
    /**
     * Show upload progress notification
     * @param progress Progress percentage (0-100)
     * @param tagCount Number of tags being uploaded
     */
    public void showUploadProgress(int progress, int tagCount) {
        Intent intent = new Intent(context, UHFMainActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(
                context, 0, intent, 
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.M ? PendingIntent.FLAG_IMMUTABLE : 0
        );
        
        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_launcher) // You may want to create a specific upload icon
                .setContentTitle("Uploading RFID Data")
                .setContentText("Uploading " + tagCount + " tags...")
                .setProgress(100, progress, false)
                .setOngoing(true)
                .setContentIntent(pendingIntent)
                .setPriority(NotificationCompat.PRIORITY_DEFAULT);
        
        notificationManager.notify(NOTIFICATION_ID_UPLOAD_PROGRESS, builder.build());
    }
    
    /**
     * Show upload success notification
     * @param tagCount Number of tags uploaded successfully
     * @param uploadId Server upload ID (optional)
     */
    public void showUploadSuccess(int tagCount, String uploadId) {
        // Cancel progress notification
        notificationManager.cancel(NOTIFICATION_ID_UPLOAD_PROGRESS);
        
        Intent intent = new Intent(context, UHFMainActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(
                context, 0, intent,
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.M ? PendingIntent.FLAG_IMMUTABLE : 0
        );
        
        String contentText = "Successfully uploaded " + tagCount + " tags";
        if (uploadId != null && !uploadId.isEmpty()) {
            contentText += " (ID: " + uploadId + ")";
        }
        
        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_launcher)
                .setContentTitle("Upload Successful")
                .setContentText(contentText)
                .setAutoCancel(true)
                .setContentIntent(pendingIntent)
                .setPriority(NotificationCompat.PRIORITY_DEFAULT);
        
        notificationManager.notify(NOTIFICATION_ID_UPLOAD_SUCCESS, builder.build());
    }
    
    /**
     * Show upload error notification
     * @param errorMessage Error description
     * @param tagCount Number of tags that failed to upload
     */
    public void showUploadError(String errorMessage, int tagCount) {
        // Cancel progress notification
        notificationManager.cancel(NOTIFICATION_ID_UPLOAD_PROGRESS);
        
        Intent intent = new Intent(context, UHFMainActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(
                context, 0, intent,
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.M ? PendingIntent.FLAG_IMMUTABLE : 0
        );
        
        String contentText = "Failed to upload " + tagCount + " tags";
        if (errorMessage != null && !errorMessage.isEmpty()) {
            contentText += ": " + errorMessage;
        }
        
        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_launcher)
                .setContentTitle("Upload Failed")
                .setContentText(contentText)
                .setStyle(new NotificationCompat.BigTextStyle().bigText(contentText))
                .setAutoCancel(true)
                .setContentIntent(pendingIntent)
                .setPriority(NotificationCompat.PRIORITY_HIGH);
        
        notificationManager.notify(NOTIFICATION_ID_UPLOAD_ERROR, builder.build());
    }
    
    /**
     * Cancel all upload notifications
     */
    public void cancelAllNotifications() {
        notificationManager.cancel(NOTIFICATION_ID_UPLOAD_PROGRESS);
        notificationManager.cancel(NOTIFICATION_ID_UPLOAD_SUCCESS);
        notificationManager.cancel(NOTIFICATION_ID_UPLOAD_ERROR);
    }
    
    /**
     * Show simple upload started notification
     * @param tagCount Number of tags to be uploaded
     */
    public void showUploadStarted(int tagCount) {
        Intent intent = new Intent(context, UHFMainActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(
                context, 0, intent,
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.M ? PendingIntent.FLAG_IMMUTABLE : 0
        );
        
        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_launcher)
                .setContentTitle("Upload Started")
                .setContentText("Starting upload of " + tagCount + " tags...")
                .setAutoCancel(true)
                .setContentIntent(pendingIntent)
                .setPriority(NotificationCompat.PRIORITY_LOW);
        
        notificationManager.notify(NOTIFICATION_ID_UPLOAD_PROGRESS, builder.build());
    }
}
