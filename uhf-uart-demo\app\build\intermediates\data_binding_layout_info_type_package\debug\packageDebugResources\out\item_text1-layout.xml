<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_text1" modulePackage="com.example.uhf" filePath="app\src\main\res\layout\item_text1.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.TextView" rootNodeViewId="@+id/tv"><Targets><Target id="@+id/tv" tag="layout/item_text1_0" view="TextView"><Expressions/><location startLine="1" startOffset="0" endLine="9" endOffset="29"/></Target></Targets></Layout>