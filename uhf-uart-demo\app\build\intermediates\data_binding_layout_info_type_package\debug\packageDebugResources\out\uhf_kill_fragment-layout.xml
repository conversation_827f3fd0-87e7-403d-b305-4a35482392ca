<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="uhf_kill_fragment" modulePackage="com.example.uhf" filePath="app\src\main\res\layout\uhf_kill_fragment.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/uhf_kill_fragment_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="193" endOffset="12"/></Target><Target id="@+id/cb_filter" view="CheckBox"><Expressions/><location startLine="11" startOffset="8" endLine="17" endOffset="37"/></Target><Target id="@+id/llFilter" view="LinearLayout"><Expressions/><location startLine="19" startOffset="8" endLine="127" endOffset="22"/></Target><Target id="@+id/etPtr_filter" view="EditText"><Expressions/><location startLine="36" startOffset="16" endLine="42" endOffset="39"/></Target><Target id="@+id/etLen_filter" view="EditText"><Expressions/><location startLine="55" startOffset="16" endLine="61" endOffset="39"/></Target><Target id="@+id/etData_filter" view="EditText"><Expressions/><location startLine="78" startOffset="16" endLine="84" endOffset="47"/></Target><Target id="@+id/rbEPC_filter" view="RadioButton"><Expressions/><location startLine="92" startOffset="16" endLine="101" endOffset="40"/></Target><Target id="@+id/rbTID_filter" view="RadioButton"><Expressions/><location startLine="103" startOffset="16" endLine="113" endOffset="40"/></Target><Target id="@+id/rbUser_filter" view="RadioButton"><Expressions/><location startLine="115" startOffset="16" endLine="125" endOffset="41"/></Target><Target id="@+id/EtAccessPwd_Write" view="EditText"><Expressions/><location startLine="175" startOffset="12" endLine="179" endOffset="60"/></Target><Target id="@+id/btnKill" view="Button"><Expressions/><location startLine="182" startOffset="8" endLine="190" endOffset="45"/></Target></Targets></Layout>