@echo off
echo Fixing Upload Button Issue
echo ==========================

REM Set Android SDK paths
set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
set PATH=%PATH%;%ANDROID_HOME%\platform-tools;%ANDROID_HOME%\emulator

echo.
echo Step 1: Checking current app installation...
adb shell pm list packages | findstr uhftool

echo.
echo Step 2: Uninstalling current app...
adb uninstall com.maspects.uhftool
if %ERRORLEVEL% equ 0 (
    echo SUCCESS: App uninstalled
) else (
    echo WARNING: App may not have been installed or already uninstalled
)

echo.
echo Step 3: Installing original APK with upload functionality...
if exist "UHF-serial_v1.4.5.apk" (
    adb install UHF-serial_v1.4.5.apk
    if %ERRORLEVEL% equ 0 (
        echo SUCCESS: Original APK installed!
    ) else (
        echo ERROR: Failed to install APK
        pause
        exit /b 1
    )
) else (
    echo ERROR: UHF-serial_v1.4.5.apk not found!
    pause
    exit /b 1
)

echo.
echo Step 4: Launching app...
adb shell am start -n com.example.uhf/.activity.UHFMainActivity
if %ERRORLEVEL% equ 0 (
    echo SUCCESS: App launched with original package!
) else (
    echo WARNING: Could not launch app automatically
    echo Please open the app manually
)

echo.
echo Step 5: Starting test server...
if exist "test_server.py" (
    echo Starting Python test server in new window...
    start "Test Server" python test_server.py
    echo Test server started!
) else (
    echo WARNING: test_server.py not found
)

echo.
echo ========================================
echo UPLOAD BUTTON FIX COMPLETE!
echo ========================================
echo.
echo The app should now show:
echo - Upload Settings tab (scroll to find it)
echo - Upload button in Scan tab (next to Clear)
echo.
echo Configuration for emulator:
echo - Server URL: http://10.0.2.2:8080/upload
echo - API Key: test-key
echo - Enable Auto Upload
echo.
echo Press any key to exit...
pause
