package com.maspects.uhftool.upload;

import android.content.Context;

import com.maspects.uhftool.upload.models.TagData;
import com.maspects.uhftool.upload.models.UploadRequest;
import com.maspects.uhftool.upload.service.UploadService;
import com.rscja.deviceapi.entity.UHFTAGInfo;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.RuntimeEnvironment;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for UploadService
 * Tests data conversion, configuration, and basic functionality
 */
@RunWith(RobolectricTestRunner.class)
public class UploadServiceTest {
    
    private UploadService uploadService;
    private Context context;
    
    @Mock
    private UHFTAGInfo mockTag1;
    
    @Mock
    private UHFTAGInfo mockTag2;
    
    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        context = RuntimeEnvironment.application;
        uploadService = new UploadService(context);
        
        // Setup mock tag data
        when(mockTag1.getEPC()).thenReturn("E2001234567890123456");
        when(mockTag1.getTid()).thenReturn("E200123456789012");
        when(mockTag1.getUser()).thenReturn("UserData1");
        when(mockTag1.getRssi()).thenReturn("-45");
        when(mockTag1.getPhase()).thenReturn(123);
        when(mockTag1.getCount()).thenReturn(5);
        
        when(mockTag2.getEPC()).thenReturn("E2009876543210987654");
        when(mockTag2.getTid()).thenReturn("E200987654321098");
        when(mockTag2.getUser()).thenReturn("UserData2");
        when(mockTag2.getRssi()).thenReturn("-52");
        when(mockTag2.getPhase()).thenReturn(456);
        when(mockTag2.getCount()).thenReturn(3);
    }
    
    @Test
    public void testServiceInitialization() {
        assertNotNull("UploadService should be initialized", uploadService);
        assertNotNull("Device ID should be generated", uploadService.getDeviceId());
        assertFalse("Service should not be configured initially", uploadService.isConfigured());
    }
    
    @Test
    public void testConfiguration() {
        // Test initial state
        assertFalse("Should not be configured initially", uploadService.isConfigured());
        assertEquals("", uploadService.getServerUrl());
        assertEquals("", uploadService.getApiKey());
        
        // Test configuration
        uploadService.setServerUrl("https://test-server.com/");
        uploadService.setApiKey("test-api-key");
        
        assertTrue("Should be configured after setting URL and API key", uploadService.isConfigured());
        assertEquals("https://test-server.com/", uploadService.getServerUrl());
        assertEquals("test-api-key", uploadService.getApiKey());
    }
    
    @Test
    public void testAutoUploadSetting() {
        // Test default state
        assertFalse("Auto upload should be disabled by default", uploadService.isAutoUploadEnabled());
        
        // Test enabling auto upload
        uploadService.setAutoUpload(true);
        assertTrue("Auto upload should be enabled", uploadService.isAutoUploadEnabled());
        
        // Test disabling auto upload
        uploadService.setAutoUpload(false);
        assertFalse("Auto upload should be disabled", uploadService.isAutoUploadEnabled());
    }
    
    @Test
    public void testUploadEndpointConfiguration() {
        // Test default endpoint
        assertEquals("api/rfid/upload", uploadService.getUploadEndpoint());
        
        // Test custom endpoint
        uploadService.setUploadEndpoint("custom/upload/endpoint");
        assertEquals("custom/upload/endpoint", uploadService.getUploadEndpoint());
    }
    
    @Test
    public void testDeviceIdPersistence() {
        String deviceId1 = uploadService.getDeviceId();
        
        // Create new service instance
        UploadService uploadService2 = new UploadService(context);
        String deviceId2 = uploadService2.getDeviceId();
        
        assertEquals("Device ID should be persistent across service instances", deviceId1, deviceId2);
    }
    
    @Test
    public void testStatusTrackerAccess() {
        assertNotNull("Status tracker should be available", uploadService.getStatusTracker());
    }
    
    @Test
    public void testNotificationHelperAccess() {
        assertNotNull("Notification helper should be available", uploadService.getNotificationHelper());
    }
    
    @Test
    public void testUploadWithoutConfiguration() {
        List<UHFTAGInfo> tagList = new ArrayList<>();
        tagList.add(mockTag1);
        
        // Test upload without configuration
        uploadService.uploadScanData(tagList, System.currentTimeMillis() - 60000, 
                System.currentTimeMillis(), new UploadService.UploadCallback() {
            @Override
            public void onUploadSuccess(com.maspects.uhftool.upload.models.UploadResponse response) {
                fail("Upload should not succeed without configuration");
            }
            
            @Override
            public void onUploadError(String error) {
                assertTrue("Error should mention configuration", 
                        error.contains("not configured"));
            }
            
            @Override
            public void onUploadProgress(int progress) {
                // Not expected for this test
            }
        });
    }
    
    @Test
    public void testUploadWithEmptyTagList() {
        // Configure service
        uploadService.setServerUrl("https://test-server.com/");
        uploadService.setApiKey("test-api-key");
        
        List<UHFTAGInfo> emptyTagList = new ArrayList<>();
        
        uploadService.uploadScanData(emptyTagList, System.currentTimeMillis() - 60000, 
                System.currentTimeMillis(), new UploadService.UploadCallback() {
            @Override
            public void onUploadSuccess(com.maspects.uhftool.upload.models.UploadResponse response) {
                fail("Upload should not succeed with empty tag list");
            }
            
            @Override
            public void onUploadError(String error) {
                assertTrue("Error should mention no tag data", 
                        error.contains("No tag data"));
            }
            
            @Override
            public void onUploadProgress(int progress) {
                // Not expected for this test
            }
        });
    }
}
