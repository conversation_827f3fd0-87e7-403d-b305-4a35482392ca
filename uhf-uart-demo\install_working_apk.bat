@echo off
echo Installing Working APK with Upload Button
echo =========================================

REM Set Android SDK paths
set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
set PATH=%PATH%;%ANDROID_HOME%\platform-tools

echo.
echo Step 1: Uninstalling current apps...
adb uninstall com.maspects.uhftool
adb uninstall com.example.uhf

echo.
echo Step 2: Installing original working APK...
if exist "UHF-serial_v1.4.5.apk" (
    adb install UHF-serial_v1.4.5.apk
    if %ERRORLEVEL% equ 0 (
        echo SUCCESS: Original APK installed!
        echo Package: com.example.uhf
    ) else (
        echo ERROR: Failed to install APK
        pause
        exit /b 1
    )
) else (
    echo ERROR: UHF-serial_v1.4.5.apk not found!
    pause
    exit /b 1
)

echo.
echo Step 3: Launching app...
adb shell am start -n com.example.uhf/.activity.UHFMainActivity
if %ERRORLEVEL% equ 0 (
    echo SUCCESS: App launched!
) else (
    echo WARNING: Could not launch automatically
    echo Please open the app manually
)

echo.
echo Step 4: Starting test server...
if exist "test_server.py" (
    echo Starting test server...
    start "UHF Test Server" python test_server.py
    timeout /t 2 >nul
    echo Test server started!
) else (
    echo WARNING: test_server.py not found
)

echo.
echo ========================================
echo ORIGINAL APK INSTALLED!
echo ========================================
echo.
echo Now check the app for:
echo 1. Upload Settings tab (scroll through tabs)
echo 2. Upload button in Scan tab
echo.
echo If you see the Upload button:
echo - Configure in Upload Settings tab
echo - Server URL: http://10.0.2.2:8080/upload
echo - API Key: test-key
echo.
echo Press any key to exit...
pause
