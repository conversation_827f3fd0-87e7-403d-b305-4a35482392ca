@echo off
echo UHF App Emulator Quick Test
echo ============================

REM Set Android SDK paths
set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
set PATH=%PATH%;%ANDROID_HOME%\platform-tools;%ANDROID_HOME%\emulator

echo.
echo Step 1: Checking ADB...
adb version
if %ERRORLEVEL% neq 0 (
    echo ERROR: ADB not found!
    pause
    exit /b 1
)

echo.
echo Step 2: Checking for running devices...
adb devices

echo.
echo Step 3: Checking available AVDs...
emulator -list-avds

echo.
echo Step 4: Starting emulator (if needed)...
echo If no devices are running, please start an emulator manually:
echo   emulator -avd Medium_Phone_API_36.0
echo.
echo Or use Android Studio: Tools -> AVD Manager -> Start emulator
echo.

echo Step 5: Waiting for device to be ready...
echo Press any key when emulator is fully booted...
pause

echo.
echo Step 6: Installing APK...
if exist "UHF-serial_v1.4.5.apk" (
    adb install UHF-serial_v1.4.5.apk
    if %ERRORLEVEL% equ 0 (
        echo SUCCESS: APK installed!
    ) else (
        echo Trying to reinstall...
        adb install -r UHF-serial_v1.4.5.apk
    )
) else (
    echo ERROR: UHF-serial_v1.4.5.apk not found!
    pause
    exit /b 1
)

echo.
echo Step 7: Starting test server...
if exist "test_server.py" (
    echo Starting Python test server in new window...
    start "Test Server" python test_server.py
    echo Test server started!
) else (
    echo WARNING: test_server.py not found
)

echo.
echo Step 8: Launching app...
adb shell am start -n com.maspects.uhftool/.activity.UHFMainActivity
if %ERRORLEVEL% equ 0 (
    echo SUCCESS: App launched!
) else (
    echo WARNING: Could not launch app automatically
    echo Please open the app manually on the emulator
)

echo.
echo Step 9: Getting network info...
echo Your PC IP addresses:
ipconfig | findstr IPv4

echo.
echo ========================================
echo SETUP COMPLETE!
echo ========================================
echo.
echo Next steps:
echo 1. Open the UHF app on the emulator
echo 2. Go to Upload Settings tab
echo 3. Configure:
echo    - Server URL: http://********:8080/upload
echo    - API Key: test-key
echo    - Enable Auto Upload
echo 4. Test the connection
echo 5. Check the test server window for requests
echo.
echo Monitor app logs with:
echo   adb logcat ^| findstr uhftool
echo.
echo Press any key to exit...
pause
