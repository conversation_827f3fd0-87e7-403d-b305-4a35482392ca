[{"merged": "com.example.uhf.app-debug-4:/anim_slide_left_out.xml.flat", "source": "com.example.uhf.app-main-6:/anim/slide_left_out.xml"}, {"merged": "com.example.uhf.app-debug-4:/drawable_check_text_color.xml.flat", "source": "com.example.uhf.app-main-6:/drawable/check_text_color.xml"}, {"merged": "com.example.uhf.app-debug-4:/xml_data_extraction_rules.xml.flat", "source": "com.example.uhf.app-main-6:/xml/data_extraction_rules.xml"}, {"merged": "com.example.uhf.app-debug-4:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.example.uhf.app-main-6:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.example.uhf.app-debug-4:/drawable_goroot.png.flat", "source": "com.example.uhf.app-main-6:/drawable/goroot.png"}, {"merged": "com.example.uhf.app-debug-4:/drawable-hdpi_webtext.png.flat", "source": "com.example.uhf.app-main-6:/drawable-hdpi/webtext.png"}, {"merged": "com.example.uhf.app-debug-4:/layout_uhf_lock_fragment.xml.flat", "source": "com.example.uhf.app-main-6:/layout/uhf_lock_fragment.xml"}, {"merged": "com.example.uhf.app-debug-4:/drawable_ic_launcher_foreground.xml.flat", "source": "com.example.uhf.app-main-6:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.example.uhf.app-debug-4:/drawable_button_bg_gray.xml.flat", "source": "com.example.uhf.app-main-6:/drawable/button_bg_gray.xml"}, {"merged": "com.example.uhf.app-debug-4:/layout_rardar_view.xml.flat", "source": "com.example.uhf.app-main-6:/layout/rardar_view.xml"}, {"merged": "com.example.uhf.app-debug-4:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.example.uhf.app-main-6:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.example.uhf.app-debug-4:/layout_fragment_uhf_set.xml.flat", "source": "com.example.uhf.app-main-6:/layout/fragment_uhf_set.xml"}, {"merged": "com.example.uhf.app-debug-4:/anim_slide_left_in.xml.flat", "source": "com.example.uhf.app-main-6:/anim/slide_left_in.xml"}, {"merged": "com.example.uhf.app-debug-4:/layout_popwindow_filter.xml.flat", "source": "com.example.uhf.app-main-6:/layout/popwindow_filter.xml"}, {"merged": "com.example.uhf.app-debug-4:/drawable_button_bg_down3.xml.flat", "source": "com.example.uhf.app-main-6:/drawable/button_bg_down3.xml"}, {"merged": "com.example.uhf.app-debug-4:/drawable_button_bg_up3.xml.flat", "source": "com.example.uhf.app-main-6:/drawable/button_bg_up3.xml"}, {"merged": "com.example.uhf.app-debug-4:/drawable-mdpi_audio.png.flat", "source": "com.example.uhf.app-main-6:/drawable-mdpi/audio.png"}, {"merged": "com.example.uhf.app-debug-4:/layout_fragment_block_permalock.xml.flat", "source": "com.example.uhf.app-main-6:/layout/fragment_block_permalock.xml"}, {"merged": "com.example.uhf.app-debug-4:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.example.uhf.app-main-6:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.uhf.app-debug-4:/layout_uhf_read_write_fragment.xml.flat", "source": "com.example.uhf.app-main-6:/layout/uhf_read_write_fragment.xml"}, {"merged": "com.example.uhf.app-debug-4:/layout_fragment_blank.xml.flat", "source": "com.example.uhf.app-main-6:/layout/fragment_blank.xml"}, {"merged": "com.example.uhf.app-debug-4:/raw_serror.ogg.flat", "source": "com.example.uhf.app-main-6:/raw/serror.ogg"}, {"merged": "com.example.uhf.app-debug-4:/layout_fragment_deactivate.xml.flat", "source": "com.example.uhf.app-main-6:/layout/fragment_deactivate.xml"}, {"merged": "com.example.uhf.app-debug-4:/drawable_item_list_goods_show_list_bg.9.png.flat", "source": "com.example.uhf.app-main-6:/drawable/item_list_goods_show_list_bg.9.png"}, {"merged": "com.example.uhf.app-debug-4:/drawable-xxhdpi_ic_launcher.png.flat", "source": "com.example.uhf.app-main-6:/drawable-xxhdpi/ic_launcher.png"}, {"merged": "com.example.uhf.app-debug-4:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.example.uhf.app-main-6:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.example.uhf.app-debug-4:/layout_fragment_block_write.xml.flat", "source": "com.example.uhf.app-main-6:/layout/fragment_block_write.xml"}, {"merged": "com.example.uhf.app-debug-4:/drawable_ic_launcher_background.xml.flat", "source": "com.example.uhf.app-main-6:/drawable/ic_launcher_background.xml"}, {"merged": "com.example.uhf.app-debug-4:/drawable_triangle.png.flat", "source": "com.example.uhf.app-main-6:/drawable/triangle.png"}, {"merged": "com.example.uhf.app-debug-4:/drawable_menu_bg_selected.9.png.flat", "source": "com.example.uhf.app-main-6:/drawable/menu_bg_selected.9.png"}, {"merged": "com.example.uhf.app-debug-4:/drawable_button_bg_up.xml.flat", "source": "com.example.uhf.app-main-6:/drawable/button_bg_up.xml"}, {"merged": "com.example.uhf.app-debug-4:/layout-land_fragment_uhf_radar_location.xml.flat", "source": "com.example.uhf.app-main-6:/layout-land/fragment_uhf_radar_location.xml"}, {"merged": "com.example.uhf.app-debug-4:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.example.uhf.app-main-6:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.example.uhf.app-debug-4:/layout_item_text1.xml.flat", "source": "com.example.uhf.app-main-6:/layout/item_text1.xml"}, {"merged": "com.example.uhf.app-debug-4:/anim_grow_from_topleft_to_bottomright.xml.flat", "source": "com.example.uhf.app-main-6:/anim/grow_from_topleft_to_bottomright.xml"}, {"merged": "com.example.uhf.app-debug-4:/layout_fragment_upload_settings.xml.flat", "source": "com.example.uhf.app-main-6:/layout/fragment_upload_settings.xml"}, {"merged": "com.example.uhf.app-debug-4:/drawable_phone.png.flat", "source": "com.example.uhf.app-main-6:/drawable/phone.png"}, {"merged": "com.example.uhf.app-debug-4:/drawable-mdpi_webtext.png.flat", "source": "com.example.uhf.app-main-6:/drawable-mdpi/webtext.png"}, {"merged": "com.example.uhf.app-debug-4:/drawable_uponelevel.png.flat", "source": "com.example.uhf.app-main-6:/drawable/uponelevel.png"}, {"merged": "com.example.uhf.app-debug-4:/drawable-mdpi_folder.png.flat", "source": "com.example.uhf.app-main-6:/drawable-mdpi/folder.png"}, {"merged": "com.example.uhf.app-debug-4:/drawable_button_bg_down.xml.flat", "source": "com.example.uhf.app-main-6:/drawable/button_bg_down.xml"}, {"merged": "com.example.uhf.app-debug-4:/drawable-mdpi_packed.png.flat", "source": "com.example.uhf.app-main-6:/drawable-mdpi/packed.png"}, {"merged": "com.example.uhf.app-debug-4:/drawable_check_text_color2.xml.flat", "source": "com.example.uhf.app-main-6:/drawable/check_text_color2.xml"}, {"merged": "com.example.uhf.app-debug-4:/anim_slide_right_in.xml.flat", "source": "com.example.uhf.app-main-6:/anim/slide_right_in.xml"}, {"merged": "com.example.uhf.app-debug-4:/drawable-mdpi_paste.png.flat", "source": "com.example.uhf.app-main-6:/drawable-mdpi/paste.png"}, {"merged": "com.example.uhf.app-debug-4:/layout_activity_test.xml.flat", "source": "com.example.uhf.app-main-6:/layout/activity_test.xml"}, {"merged": "com.example.uhf.app-debug-4:/layout_activity_main.xml.flat", "source": "com.example.uhf.app-main-6:/layout/activity_main.xml"}, {"merged": "com.example.uhf.app-debug-4:/drawable_seekbar_bg.xml.flat", "source": "com.example.uhf.app-main-6:/drawable/seekbar_bg.xml"}, {"merged": "com.example.uhf.app-debug-4:/layout_fragment_uhflocation.xml.flat", "source": "com.example.uhf.app-main-6:/layout/fragment_uhflocation.xml"}, {"merged": "com.example.uhf.app-debug-4:/drawable-mdpi_text.png.flat", "source": "com.example.uhf.app-main-6:/drawable-mdpi/text.png"}, {"merged": "com.example.uhf.app-debug-4:/layout_fragment_temperature.xml.flat", "source": "com.example.uhf.app-main-6:/layout/fragment_temperature.xml"}, {"merged": "com.example.uhf.app-debug-4:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.example.uhf.app-main-6:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.example.uhf.app-debug-4:/layout_uhf_light_fragment.xml.flat", "source": "com.example.uhf.app-main-6:/layout/uhf_light_fragment.xml"}, {"merged": "com.example.uhf.app-debug-4:/layout_uhf_readtag_fragment.xml.flat", "source": "com.example.uhf.app-main-6:/layout/uhf_readtag_fragment.xml"}, {"merged": "com.example.uhf.app-debug-4:/anim_slide_right_out.xml.flat", "source": "com.example.uhf.app-main-6:/anim/slide_right_out.xml"}, {"merged": "com.example.uhf.app-debug-4:/drawable_button_bg3.xml.flat", "source": "com.example.uhf.app-main-6:/drawable/button_bg3.xml"}, {"merged": "com.example.uhf.app-debug-4:/layout_listtag_items.xml.flat", "source": "com.example.uhf.app-main-6:/layout/listtag_items.xml"}, {"merged": "com.example.uhf.app-debug-4:/mipmap-anydpi_ic_launcher_round.xml.flat", "source": "com.example.uhf.app-main-6:/mipmap-anydpi/ic_launcher_round.xml"}, {"merged": "com.example.uhf.app-debug-4:/drawable-mdpi_ic_launcher.png.flat", "source": "com.example.uhf.app-main-6:/drawable-mdpi/ic_launcher.png"}, {"merged": "com.example.uhf.app-debug-4:/drawable-mdpi_image.png.flat", "source": "com.example.uhf.app-main-6:/drawable-mdpi/image.png"}, {"merged": "com.example.uhf.app-debug-4:/layout_uhf_kill_fragment.xml.flat", "source": "com.example.uhf.app-main-6:/layout/uhf_kill_fragment.xml"}, {"merged": "com.example.uhf.app-debug-4:/drawable_pop_menu_bg.xml.flat", "source": "com.example.uhf.app-main-6:/drawable/pop_menu_bg.xml"}, {"merged": "com.example.uhf.app-debug-4:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.example.uhf.app-main-6:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.example.uhf.app-debug-4:/layout_fragment_uhfupgrade.xml.flat", "source": "com.example.uhf.app-main-6:/layout/fragment_uhfupgrade.xml"}, {"merged": "com.example.uhf.app-debug-4:/drawable_card_press.xml.flat", "source": "com.example.uhf.app-main-6:/drawable/card_press.xml"}, {"merged": "com.example.uhf.app-debug-4:/drawable-xhdpi_ic_launcher.png.flat", "source": "com.example.uhf.app-main-6:/drawable-xhdpi/ic_launcher.png"}, {"merged": "com.example.uhf.app-debug-4:/drawable_card_normal.xml.flat", "source": "com.example.uhf.app-main-6:/drawable/card_normal.xml"}, {"merged": "com.example.uhf.app-debug-4:/drawable_rectangle_bg.xml.flat", "source": "com.example.uhf.app-main-6:/drawable/rectangle_bg.xml"}, {"merged": "com.example.uhf.app-debug-4:/layout_uhf_dialog_lock_code.xml.flat", "source": "com.example.uhf.app-main-6:/layout/uhf_dialog_lock_code.xml"}, {"merged": "com.example.uhf.app-debug-4:/drawable_button_bg.xml.flat", "source": "com.example.uhf.app-main-6:/drawable/button_bg.xml"}, {"merged": "com.example.uhf.app-debug-4:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.example.uhf.app-main-6:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.example.uhf.app-debug-4:/drawable_shape_seekbar_circle.xml.flat", "source": "com.example.uhf.app-main-6:/drawable/shape_seekbar_circle.xml"}, {"merged": "com.example.uhf.app-debug-4:/drawable_rectangle_bg2.xml.flat", "source": "com.example.uhf.app-main-6:/drawable/rectangle_bg2.xml"}, {"merged": "com.example.uhf.app-debug-4:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.example.uhf.app-main-6:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.uhf.app-debug-4:/drawable-mdpi_video.png.flat", "source": "com.example.uhf.app-main-6:/drawable-mdpi/video.png"}, {"merged": "com.example.uhf.app-debug-4:/xml_backup_rules.xml.flat", "source": "com.example.uhf.app-main-6:/xml/backup_rules.xml"}, {"merged": "com.example.uhf.app-debug-4:/layout_fragment_main.xml.flat", "source": "com.example.uhf.app-main-6:/layout/fragment_main.xml"}, {"merged": "com.example.uhf.app-debug-4:/layout_fragment_uhf_radar_location.xml.flat", "source": "com.example.uhf.app-main-6:/layout/fragment_uhf_radar_location.xml"}, {"merged": "com.example.uhf.app-debug-4:/drawable_button_bg2.xml.flat", "source": "com.example.uhf.app-main-6:/drawable/button_bg2.xml"}, {"merged": "com.example.uhf.app-debug-4:/drawable-hdpi_close.png.flat", "source": "com.example.uhf.app-main-6:/drawable-hdpi/close.png"}, {"merged": "com.example.uhf.app-debug-4:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.example.uhf.app-main-6:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.example.uhf.app-debug-4:/drawable_button_bg_up2.xml.flat", "source": "com.example.uhf.app-main-6:/drawable/button_bg_up2.xml"}, {"merged": "com.example.uhf.app-debug-4:/mipmap-anydpi_ic_launcher.xml.flat", "source": "com.example.uhf.app-main-6:/mipmap-anydpi/ic_launcher.xml"}, {"merged": "com.example.uhf.app-debug-4:/layout_uhf_dialog_frequency.xml.flat", "source": "com.example.uhf.app-main-6:/layout/uhf_dialog_frequency.xml"}, {"merged": "com.example.uhf.app-debug-4:/menu_main.xml.flat", "source": "com.example.uhf.app-main-6:/menu/main.xml"}, {"merged": "com.example.uhf.app-debug-4:/drawable_rb_bg.xml.flat", "source": "com.example.uhf.app-main-6:/drawable/rb_bg.xml"}, {"merged": "com.example.uhf.app-debug-4:/drawable-hdpi_ic_launcher.png.flat", "source": "com.example.uhf.app-main-6:/drawable-hdpi/ic_launcher.png"}, {"merged": "com.example.uhf.app-debug-4:/drawable_actionbar_back.xml.flat", "source": "com.example.uhf.app-main-6:/drawable/actionbar_back.xml"}, {"merged": "com.example.uhf.app-debug-4:/raw_barcodebeep.ogg.flat", "source": "com.example.uhf.app-main-6:/raw/barcodebeep.ogg"}, {"merged": "com.example.uhf.app-debug-4:/drawable_button_bg_down2.xml.flat", "source": "com.example.uhf.app-main-6:/drawable/button_bg_down2.xml"}]