<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_block_write" modulePackage="com.example.uhf" filePath="app\src\main\res\layout\fragment_block_write.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_block_write_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="265" endOffset="12"/></Target><Target id="@+id/cb_filter_wt" view="CheckBox"><Expressions/><location startLine="24" startOffset="12" endLine="30" endOffset="41"/></Target><Target id="@+id/etPtr_filter_wt" view="EditText"><Expressions/><location startLine="41" startOffset="16" endLine="46" endOffset="39"/></Target><Target id="@+id/etLen_filter_wt" view="EditText"><Expressions/><location startLine="59" startOffset="16" endLine="64" endOffset="38"/></Target><Target id="@+id/etData_filter_wt" view="EditText"><Expressions/><location startLine="81" startOffset="16" endLine="87" endOffset="47"/></Target><Target id="@+id/rbEPC_filter_wt" view="RadioButton"><Expressions/><location startLine="96" startOffset="16" endLine="106" endOffset="68"/></Target><Target id="@+id/rbTID_filter_wt" view="RadioButton"><Expressions/><location startLine="108" startOffset="16" endLine="119" endOffset="68"/></Target><Target id="@+id/rbUser_filter_wt" view="RadioButton"><Expressions/><location startLine="121" startOffset="16" endLine="132" endOffset="68"/></Target><Target id="@+id/SpinnerBank_Write" view="Spinner"><Expressions/><location startLine="150" startOffset="12" endLine="154" endOffset="52"/></Target><Target id="@+id/EtPtr_Write" view="EditText"><Expressions/><location startLine="168" startOffset="12" endLine="177" endOffset="41"/></Target><Target id="@+id/EtLen_Write" view="EditText"><Expressions/><location startLine="191" startOffset="12" endLine="200" endOffset="41"/></Target><Target id="@+id/EtAccessPwd_Write" view="EditText"><Expressions/><location startLine="219" startOffset="12" endLine="224" endOffset="41"/></Target><Target id="@+id/EtData_Write" view="EditText"><Expressions/><location startLine="239" startOffset="12" endLine="245" endOffset="41"/></Target><Target id="@+id/BtWrite" view="Button"><Expressions/><location startLine="252" startOffset="12" endLine="261" endOffset="50"/></Target></Targets></Layout>