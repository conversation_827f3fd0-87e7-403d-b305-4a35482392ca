[{"merged": "com.example.uhf.app-mergeDebugResources-3:/layout/uhf_kill_fragment.xml", "source": "com.example.uhf.app-main-6:/layout/uhf_kill_fragment.xml"}, {"merged": "com.example.uhf.app-mergeDebugResources-3:/layout/uhf_readtag_fragment.xml", "source": "com.example.uhf.app-main-6:/layout/uhf_readtag_fragment.xml"}, {"merged": "com.example.uhf.app-mergeDebugResources-3:/layout/uhf_lock_fragment.xml", "source": "com.example.uhf.app-main-6:/layout/uhf_lock_fragment.xml"}, {"merged": "com.example.uhf.app-mergeDebugResources-3:/layout/fragment_uhflocation.xml", "source": "com.example.uhf.app-main-6:/layout/fragment_uhflocation.xml"}, {"merged": "com.example.uhf.app-mergeDebugResources-3:/layout/uhf_light_fragment.xml", "source": "com.example.uhf.app-main-6:/layout/uhf_light_fragment.xml"}, {"merged": "com.example.uhf.app-mergeDebugResources-3:/layout/fragment_block_permalock.xml", "source": "com.example.uhf.app-main-6:/layout/fragment_block_permalock.xml"}, {"merged": "com.example.uhf.app-mergeDebugResources-3:/layout/listtag_items.xml", "source": "com.example.uhf.app-main-6:/layout/listtag_items.xml"}, {"merged": "com.example.uhf.app-mergeDebugResources-3:/layout/fragment_main.xml", "source": "com.example.uhf.app-main-6:/layout/fragment_main.xml"}, {"merged": "com.example.uhf.app-mergeDebugResources-3:/layout/fragment_upload_settings.xml", "source": "com.example.uhf.app-main-6:/layout/fragment_upload_settings.xml"}, {"merged": "com.example.uhf.app-mergeDebugResources-3:/layout/activity_main.xml", "source": "com.example.uhf.app-main-6:/layout/activity_main.xml"}, {"merged": "com.example.uhf.app-mergeDebugResources-3:/layout/fragment_blank.xml", "source": "com.example.uhf.app-main-6:/layout/fragment_blank.xml"}, {"merged": "com.example.uhf.app-mergeDebugResources-3:/layout/fragment_uhf_set.xml", "source": "com.example.uhf.app-main-6:/layout/fragment_uhf_set.xml"}, {"merged": "com.example.uhf.app-mergeDebugResources-3:/layout/fragment_deactivate.xml", "source": "com.example.uhf.app-main-6:/layout/fragment_deactivate.xml"}, {"merged": "com.example.uhf.app-mergeDebugResources-3:/layout/uhf_read_write_fragment.xml", "source": "com.example.uhf.app-main-6:/layout/uhf_read_write_fragment.xml"}, {"merged": "com.example.uhf.app-mergeDebugResources-3:/layout/uhf_dialog_frequency.xml", "source": "com.example.uhf.app-main-6:/layout/uhf_dialog_frequency.xml"}, {"merged": "com.example.uhf.app-mergeDebugResources-3:/layout/popwindow_filter.xml", "source": "com.example.uhf.app-main-6:/layout/popwindow_filter.xml"}, {"merged": "com.example.uhf.app-mergeDebugResources-3:/layout/fragment_uhfupgrade.xml", "source": "com.example.uhf.app-main-6:/layout/fragment_uhfupgrade.xml"}, {"merged": "com.example.uhf.app-mergeDebugResources-3:/layout/fragment_temperature.xml", "source": "com.example.uhf.app-main-6:/layout/fragment_temperature.xml"}, {"merged": "com.example.uhf.app-mergeDebugResources-3:/layout/uhf_dialog_lock_code.xml", "source": "com.example.uhf.app-main-6:/layout/uhf_dialog_lock_code.xml"}, {"merged": "com.example.uhf.app-mergeDebugResources-3:/layout/fragment_uhf_radar_location.xml", "source": "com.example.uhf.app-main-6:/layout/fragment_uhf_radar_location.xml"}, {"merged": "com.example.uhf.app-mergeDebugResources-3:/layout/rardar_view.xml", "source": "com.example.uhf.app-main-6:/layout/rardar_view.xml"}, {"merged": "com.example.uhf.app-mergeDebugResources-3:/layout/item_text1.xml", "source": "com.example.uhf.app-main-6:/layout/item_text1.xml"}, {"merged": "com.example.uhf.app-mergeDebugResources-3:/layout/activity_test.xml", "source": "com.example.uhf.app-main-6:/layout/activity_test.xml"}, {"merged": "com.example.uhf.app-mergeDebugResources-3:/layout/fragment_block_write.xml", "source": "com.example.uhf.app-main-6:/layout/fragment_block_write.xml"}]