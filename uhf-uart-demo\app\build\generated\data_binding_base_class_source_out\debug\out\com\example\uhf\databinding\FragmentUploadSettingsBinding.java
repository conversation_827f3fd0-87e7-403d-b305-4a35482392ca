// Generated by view binder compiler. Do not edit!
package com.example.uhf.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.uhf.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentUploadSettingsBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button btnSaveSettings;

  @NonNull
  public final Button btnTestConnection;

  @NonNull
  public final CheckBox cbAutoUpload;

  @NonNull
  public final EditText etApiKey;

  @NonNull
  public final EditText etServerUrl;

  @NonNull
  public final EditText etUploadEndpoint;

  @NonNull
  public final TextView tvConnectionStatus;

  @NonNull
  public final TextView tvDeviceId;

  @NonNull
  public final TextView tvLastUpload;

  @NonNull
  public final TextView tvUploadStats;

  private FragmentUploadSettingsBinding(@NonNull ScrollView rootView,
      @NonNull Button btnSaveSettings, @NonNull Button btnTestConnection,
      @NonNull CheckBox cbAutoUpload, @NonNull EditText etApiKey, @NonNull EditText etServerUrl,
      @NonNull EditText etUploadEndpoint, @NonNull TextView tvConnectionStatus,
      @NonNull TextView tvDeviceId, @NonNull TextView tvLastUpload,
      @NonNull TextView tvUploadStats) {
    this.rootView = rootView;
    this.btnSaveSettings = btnSaveSettings;
    this.btnTestConnection = btnTestConnection;
    this.cbAutoUpload = cbAutoUpload;
    this.etApiKey = etApiKey;
    this.etServerUrl = etServerUrl;
    this.etUploadEndpoint = etUploadEndpoint;
    this.tvConnectionStatus = tvConnectionStatus;
    this.tvDeviceId = tvDeviceId;
    this.tvLastUpload = tvLastUpload;
    this.tvUploadStats = tvUploadStats;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentUploadSettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentUploadSettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_upload_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentUploadSettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnSaveSettings;
      Button btnSaveSettings = ViewBindings.findChildViewById(rootView, id);
      if (btnSaveSettings == null) {
        break missingId;
      }

      id = R.id.btnTestConnection;
      Button btnTestConnection = ViewBindings.findChildViewById(rootView, id);
      if (btnTestConnection == null) {
        break missingId;
      }

      id = R.id.cbAutoUpload;
      CheckBox cbAutoUpload = ViewBindings.findChildViewById(rootView, id);
      if (cbAutoUpload == null) {
        break missingId;
      }

      id = R.id.etApiKey;
      EditText etApiKey = ViewBindings.findChildViewById(rootView, id);
      if (etApiKey == null) {
        break missingId;
      }

      id = R.id.etServerUrl;
      EditText etServerUrl = ViewBindings.findChildViewById(rootView, id);
      if (etServerUrl == null) {
        break missingId;
      }

      id = R.id.etUploadEndpoint;
      EditText etUploadEndpoint = ViewBindings.findChildViewById(rootView, id);
      if (etUploadEndpoint == null) {
        break missingId;
      }

      id = R.id.tvConnectionStatus;
      TextView tvConnectionStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvConnectionStatus == null) {
        break missingId;
      }

      id = R.id.tvDeviceId;
      TextView tvDeviceId = ViewBindings.findChildViewById(rootView, id);
      if (tvDeviceId == null) {
        break missingId;
      }

      id = R.id.tvLastUpload;
      TextView tvLastUpload = ViewBindings.findChildViewById(rootView, id);
      if (tvLastUpload == null) {
        break missingId;
      }

      id = R.id.tvUploadStats;
      TextView tvUploadStats = ViewBindings.findChildViewById(rootView, id);
      if (tvUploadStats == null) {
        break missingId;
      }

      return new FragmentUploadSettingsBinding((ScrollView) rootView, btnSaveSettings,
          btnTestConnection, cbAutoUpload, etApiKey, etServerUrl, etUploadEndpoint,
          tvConnectionStatus, tvDeviceId, tvLastUpload, tvUploadStats);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
