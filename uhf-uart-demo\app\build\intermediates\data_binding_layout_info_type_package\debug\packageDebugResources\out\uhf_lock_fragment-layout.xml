<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="uhf_lock_fragment" modulePackage="com.example.uhf" filePath="app\src\main\res\layout\uhf_lock_fragment.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/uhf_lock_fragment_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="340" endOffset="12"/></Target><Target id="@+id/cb_filter_lock" view="CheckBox"><Expressions/><location startLine="27" startOffset="12" endLine="33" endOffset="41"/></Target><Target id="@+id/etPtr_filter_lock" view="EditText"><Expressions/><location startLine="44" startOffset="16" endLine="50" endOffset="39"/></Target><Target id="@+id/etLen_filter_lock" view="EditText"><Expressions/><location startLine="63" startOffset="16" endLine="69" endOffset="38"/></Target><Target id="@+id/etData_filter_lock" view="EditText"><Expressions/><location startLine="86" startOffset="16" endLine="92" endOffset="47"/></Target><Target id="@+id/rbEPC_filter_lock" view="RadioButton"><Expressions/><location startLine="100" startOffset="16" endLine="110" endOffset="68"/></Target><Target id="@+id/rbTID_filter_lock" view="RadioButton"><Expressions/><location startLine="112" startOffset="16" endLine="123" endOffset="68"/></Target><Target id="@+id/rbUser_filter_lock" view="RadioButton"><Expressions/><location startLine="125" startOffset="16" endLine="136" endOffset="68"/></Target><Target id="@+id/EtAccessPwd_Lock" view="EditText"><Expressions/><location startLine="173" startOffset="20" endLine="178" endOffset="65"/></Target><Target id="@+id/etLockCode" view="EditText"><Expressions/><location startLine="191" startOffset="20" endLine="196" endOffset="61"/></Target><Target id="@+id/btnLock" view="Button"><Expressions/><location startLine="199" startOffset="16" endLine="206" endOffset="54"/></Target><Target id="@+id/etGBAccessPwd" view="EditText"><Expressions/><location startLine="250" startOffset="20" endLine="256" endOffset="65"/></Target><Target id="@+id/spGBStorageArea" view="Spinner"><Expressions/><location startLine="269" startOffset="20" endLine="273" endOffset="62"/></Target><Target id="@+id/layoutUserAreaNumber" view="LinearLayout"><Expressions/><location startLine="276" startOffset="16" endLine="293" endOffset="30"/></Target><Target id="@+id/spGBUserAreaNumber" view="Spinner"><Expressions/><location startLine="288" startOffset="20" endLine="292" endOffset="66"/></Target><Target id="@+id/spGBConfig" view="Spinner"><Expressions/><location startLine="305" startOffset="20" endLine="309" endOffset="57"/></Target><Target id="@+id/spGBAction" view="Spinner"><Expressions/><location startLine="322" startOffset="20" endLine="326" endOffset="58"/></Target><Target id="@+id/btnGBLock" view="Button"><Expressions/><location startLine="329" startOffset="16" endLine="336" endOffset="54"/></Target></Targets></Layout>