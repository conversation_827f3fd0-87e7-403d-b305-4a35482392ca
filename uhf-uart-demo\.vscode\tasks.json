{"version": "2.0.0", "tasks": [{"label": "Build Debug APK", "type": "shell", "command": "./gradlew", "args": ["assembleDebug"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Clean Build", "type": "shell", "command": "./gradlew", "args": ["clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Install Debug APK", "type": "shell", "command": "./gradlew", "args": ["installDebug"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "dependsOn": "Build Debug APK"}, {"label": "Run Tests", "type": "shell", "command": "./gradlew", "args": ["test"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}]}