package com.example.uhf.upload.service;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Build;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;

import com.example.uhf.upload.api.UploadApiService;
import com.example.uhf.upload.models.ScanSession;
import com.example.uhf.upload.models.TagData;
import com.example.uhf.upload.models.UploadRequest;
import com.example.uhf.upload.models.UploadResponse;
import com.example.uhf.upload.utils.UploadNotificationHelper;
import com.example.uhf.upload.utils.UploadStatusTracker;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.rscja.deviceapi.entity.UHFTAGInfo;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

/**
 * Service class for uploading RFID scan data to a remote server
 * Handles API communication, data conversion, and error handling
 */
public class UploadService {
    
    private static final String TAG = "UploadService";
    private static final String PREFS_NAME = "upload_settings";
    private static final String PREF_SERVER_URL = "server_url";
    private static final String PREF_API_KEY = "api_key";
    private static final String PREF_DEVICE_ID = "device_id";
    private static final String PREF_AUTO_UPLOAD = "auto_upload";
    private static final String PREF_UPLOAD_ENDPOINT = "upload_endpoint";
    
    private Context context;
    private SharedPreferences preferences;
    private UploadApiService apiService;
    private String deviceId;
    private UploadNotificationHelper notificationHelper;
    private UploadStatusTracker statusTracker;
    
    // Upload callback interface
    public interface UploadCallback {
        void onUploadSuccess(UploadResponse response);
        void onUploadError(String error);
        void onUploadProgress(int progress);
    }
    
    public UploadService(Context context) {
        this.context = context;
        this.preferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        this.deviceId = getOrCreateDeviceId();
        this.notificationHelper = new UploadNotificationHelper(context);
        this.statusTracker = new UploadStatusTracker(context);
        initializeApiService();
    }
    
    /**
     * Initialize Retrofit API service with current settings
     */
    private void initializeApiService() {
        String serverUrl = getServerUrl();
        if (TextUtils.isEmpty(serverUrl)) {
            Log.w(TAG, "Server URL not configured");
            return;
        }
        
        // Create HTTP client with logging
        HttpLoggingInterceptor logging = new HttpLoggingInterceptor();
        logging.setLevel(HttpLoggingInterceptor.Level.BODY);
        
        OkHttpClient client = new OkHttpClient.Builder()
                .addInterceptor(logging)
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();
        
        // Create Gson with date formatting
        Gson gson = new GsonBuilder()
                .setDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'")
                .create();
        
        // Create Retrofit instance
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(serverUrl)
                .client(client)
                .addConverterFactory(GsonConverterFactory.create(gson))
                .build();
        
        apiService = retrofit.create(UploadApiService.class);
    }
    
    /**
     * Upload scanned tag data to the server
     * @param tagList List of scanned tags
     * @param scanStartTime When scanning started
     * @param scanEndTime When scanning ended
     * @param callback Upload result callback
     */
    public void uploadScanData(List<UHFTAGInfo> tagList, long scanStartTime, 
                              long scanEndTime, UploadCallback callback) {
        
        if (apiService == null) {
            callback.onUploadError("Upload service not configured. Please check server settings.");
            return;
        }
        
        if (tagList == null || tagList.isEmpty()) {
            callback.onUploadError("No tag data to upload");
            return;
        }
        
        try {
            // Show upload started notification
            notificationHelper.showUploadStarted(tagList.size());

            // Record upload start time for tracking
            long uploadStartTime = System.currentTimeMillis();

            // Convert tag data
            List<TagData> uploadTags = convertTagData(tagList);

            // Create scan session
            ScanSession session = createScanSession(scanStartTime, scanEndTime);

            // Create upload request
            UploadRequest request = new UploadRequest(deviceId, session, uploadTags);
            request.setUploadTimestamp(getCurrentTimestamp());

            // Get authorization header
            String authHeader = getAuthorizationHeader();

            // Make API call
            Call<UploadResponse> call = apiService.uploadScanData(authHeader, request);
            call.enqueue(new Callback<UploadResponse>() {
                @Override
                public void onResponse(Call<UploadResponse> call, Response<UploadResponse> response) {
                    long uploadDuration = System.currentTimeMillis() - uploadStartTime;

                    if (response.isSuccessful() && response.body() != null) {
                        UploadResponse uploadResponse = response.body();
                        if (uploadResponse.isSuccess()) {
                            Log.i(TAG, "Upload successful: " + uploadResponse.getMessage());

                            // Show success notification
                            notificationHelper.showUploadSuccess(tagList.size(), uploadResponse.getUploadId());

                            // Record successful upload
                            statusTracker.recordSuccessfulUpload(tagList.size(), uploadResponse.getUploadId(), uploadDuration);

                            callback.onUploadSuccess(uploadResponse);
                        } else {
                            Log.e(TAG, "Upload failed: " + uploadResponse.getMessage());

                            // Show error notification
                            notificationHelper.showUploadError(uploadResponse.getMessage(), tagList.size());

                            // Record failed upload
                            statusTracker.recordFailedUpload(tagList.size(), uploadResponse.getMessage(), uploadDuration);

                            callback.onUploadError(uploadResponse.getMessage());
                        }
                    } else {
                        String error = "Upload failed with HTTP " + response.code();
                        try {
                            if (response.errorBody() != null) {
                                error += ": " + response.errorBody().string();
                            }
                        } catch (IOException e) {
                            Log.e(TAG, "Error reading error body", e);
                        }
                        Log.e(TAG, error);

                        // Show error notification
                        notificationHelper.showUploadError(error, tagList.size());

                        // Record failed upload
                        statusTracker.recordFailedUpload(tagList.size(), error, uploadDuration);

                        callback.onUploadError(error);
                    }
                }

                @Override
                public void onFailure(Call<UploadResponse> call, Throwable t) {
                    long uploadDuration = System.currentTimeMillis() - uploadStartTime;
                    String error = "Network error: " + t.getMessage();
                    Log.e(TAG, error, t);

                    // Show error notification
                    notificationHelper.showUploadError(error, tagList.size());

                    // Record failed upload
                    statusTracker.recordFailedUpload(tagList.size(), error, uploadDuration);

                    callback.onUploadError(error);
                }
            });
            
        } catch (Exception e) {
            String error = "Error preparing upload data: " + e.getMessage();
            Log.e(TAG, error, e);
            callback.onUploadError(error);
        }
    }
    
    /**
     * Test connection to the server
     * @param callback Result callback
     */
    public void testConnection(UploadCallback callback) {
        if (apiService == null) {
            callback.onUploadError("Upload service not configured");
            return;
        }
        
        String authHeader = getAuthorizationHeader();
        Call<UploadResponse> call = apiService.testConnection(authHeader);
        call.enqueue(new Callback<UploadResponse>() {
            @Override
            public void onResponse(Call<UploadResponse> call, Response<UploadResponse> response) {
                if (response.isSuccessful()) {
                    callback.onUploadSuccess(response.body());
                } else {
                    callback.onUploadError("Connection test failed: HTTP " + response.code());
                }
            }
            
            @Override
            public void onFailure(Call<UploadResponse> call, Throwable t) {
                callback.onUploadError("Connection test failed: " + t.getMessage());
            }
        });
    }
    
    /**
     * Convert UHFTAGInfo list to TagData list for upload
     */
    private List<TagData> convertTagData(List<UHFTAGInfo> tagList) {
        List<TagData> uploadTags = new ArrayList<>();
        String currentTime = getCurrentTimestamp();

        for (UHFTAGInfo tag : tagList) {
            TagData tagData = new TagData();
            tagData.setEpc(tag.getEPC());
            tagData.setTid(tag.getTid());
            tagData.setUserData(tag.getUser());
            tagData.setRssi(tag.getRssi());
            tagData.setPhase(String.valueOf(tag.getPhase()));
            tagData.setCount(tag.getCount());
            tagData.setFirstSeen(currentTime); // Could be enhanced to track actual first/last seen times
            tagData.setLastSeen(currentTime);
            uploadTags.add(tagData);
        }

        return uploadTags;
    }

    /**
     * Create scan session metadata
     */
    private ScanSession createScanSession(long startTime, long endTime) {
        String sessionId = UUID.randomUUID().toString();
        String startTimeStr = formatTimestamp(startTime);
        String endTimeStr = formatTimestamp(endTime);

        ScanSession session = new ScanSession(sessionId, startTimeStr, endTimeStr);
        session.setDeviceInfo(Build.MODEL + " (" + Build.MANUFACTURER + ")");
        session.setAppVersion("1.4.5"); // Could be read from BuildConfig

        return session;
    }

    /**
     * Get current timestamp in ISO format
     */
    private String getCurrentTimestamp() {
        return formatTimestamp(System.currentTimeMillis());
    }

    /**
     * Format timestamp to ISO 8601 format
     */
    private String formatTimestamp(long timestamp) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.US);
        return sdf.format(new Date(timestamp));
    }

    /**
     * Get or create unique device ID
     */
    private String getOrCreateDeviceId() {
        String deviceId = preferences.getString(PREF_DEVICE_ID, null);
        if (TextUtils.isEmpty(deviceId)) {
            // Try to get Android ID, fallback to UUID
            deviceId = Settings.Secure.getString(context.getContentResolver(), Settings.Secure.ANDROID_ID);
            if (TextUtils.isEmpty(deviceId) || "9774d56d682e549c".equals(deviceId)) {
                deviceId = UUID.randomUUID().toString();
            }
            preferences.edit().putString(PREF_DEVICE_ID, deviceId).apply();
        }
        return deviceId;
    }

    /**
     * Get authorization header for API calls
     */
    private String getAuthorizationHeader() {
        String apiKey = getApiKey();
        if (!TextUtils.isEmpty(apiKey)) {
            return "Bearer " + apiKey; // or "API-Key " + apiKey depending on server requirements
        }
        return "";
    }

    // Configuration methods
    public void setServerUrl(String url) {
        preferences.edit().putString(PREF_SERVER_URL, url).apply();
        initializeApiService();
    }

    public String getServerUrl() {
        return preferences.getString(PREF_SERVER_URL, "");
    }

    public void setApiKey(String apiKey) {
        preferences.edit().putString(PREF_API_KEY, apiKey).apply();
    }

    public String getApiKey() {
        return preferences.getString(PREF_API_KEY, "");
    }

    public void setAutoUpload(boolean autoUpload) {
        preferences.edit().putBoolean(PREF_AUTO_UPLOAD, autoUpload).apply();
    }

    public boolean isAutoUploadEnabled() {
        return preferences.getBoolean(PREF_AUTO_UPLOAD, false);
    }

    public void setUploadEndpoint(String endpoint) {
        preferences.edit().putString(PREF_UPLOAD_ENDPOINT, endpoint).apply();
    }

    public String getUploadEndpoint() {
        return preferences.getString(PREF_UPLOAD_ENDPOINT, "api/rfid/upload");
    }

    public String getDeviceId() {
        return deviceId;
    }

    public boolean isConfigured() {
        return !TextUtils.isEmpty(getServerUrl()) && !TextUtils.isEmpty(getApiKey());
    }

    /**
     * Get upload status tracker for monitoring upload history
     */
    public UploadStatusTracker getStatusTracker() {
        return statusTracker;
    }

    /**
     * Get notification helper for managing upload notifications
     */
    public UploadNotificationHelper getNotificationHelper() {
        return notificationHelper;
    }
}
