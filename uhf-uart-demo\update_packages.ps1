# PowerShell script to update package names from com.example.uhf to com.maspects.uhftool

$sourceDir = ".\app\src\main\java\com\maspects\uhftool"
$testDir = ".\app\src\test\java\com\example\uhf"

Write-Host "Updating package declarations in Java files..."

# Get all Java files in the new package structure
$javaFiles = Get-ChildItem -Path $sourceDir -Recurse -Filter "*.java"

foreach ($file in $javaFiles) {
    Write-Host "Processing: $($file.FullName)"
    
    # Read file content
    $content = Get-Content $file.FullName -Raw
    
    # Update package declaration
    $content = $content -replace "package com\.example\.uhf", "package com.maspects.uhftool"
    
    # Update import statements
    $content = $content -replace "import com\.example\.uhf\.", "import com.maspects.uhftool."
    
    # Write back to file
    Set-Content -Path $file.FullName -Value $content -NoNewline
}

# Also update test files if they exist
if (Test-Path $testDir) {
    Write-Host "Updating test files..."
    $testFiles = Get-ChildItem -Path $testDir -Recurse -Filter "*.java"
    
    foreach ($file in $testFiles) {
        Write-Host "Processing test: $($file.FullName)"
        
        $content = Get-Content $file.FullName -Raw
        $content = $content -replace "package com\.example\.uhf", "package com.maspects.uhftool"
        $content = $content -replace "import com\.example\.uhf\.", "import com.maspects.uhftool."
        
        Set-Content -Path $file.FullName -Value $content -NoNewline
    }
}

Write-Host "Package update completed!"
