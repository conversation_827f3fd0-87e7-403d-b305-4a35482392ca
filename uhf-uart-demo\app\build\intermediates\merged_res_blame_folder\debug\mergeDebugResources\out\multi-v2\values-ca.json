{"logs": [{"outputFile": "com.maspects.uhftool.app-mergeDebugResources-2:/values-ca/values-ca.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\33498c78bdd4f4128ef0bb8451a88732\\transformed\\core-1.13.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,449,555,660,786", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "146,248,347,444,550,655,781,882"}, "to": {"startLines": "38,39,40,41,42,43,44,120", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3493,3589,3691,3790,3887,3993,4098,10491", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "3584,3686,3785,3882,3988,4093,4219,10587"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aef17b985e4c925ecf4549f65f319b6b\\transformed\\material-1.12.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,274,355,435,523,626,718,819,947,1031,1092,1157,1254,1334,1399,1494,1558,1630,1692,1768,1831,1888,2009,2067,2128,2185,2265,2402,2489,2564,2657,2737,2821,2960,3038,3117,3269,3358,3434,3491,3547,3613,3691,3772,3843,3931,4009,4086,4160,4239,4349,4439,4531,4623,4724,4798,4880,4981,5031,5114,5180,5272,5359,5421,5485,5548,5621,5744,5857,5961,6069,6130,6190,6276,6362,6439", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,79,87,102,91,100,127,83,60,64,96,79,64,94,63,71,61,75,62,56,120,57,60,56,79,136,86,74,92,79,83,138,77,78,151,88,75,56,55,65,77,80,70,87,77,76,73,78,109,89,91,91,100,73,81,100,49,82,65,91,86,61,63,62,72,122,112,103,107,60,59,85,85,76,78", "endOffsets": "269,350,430,518,621,713,814,942,1026,1087,1152,1249,1329,1394,1489,1553,1625,1687,1763,1826,1883,2004,2062,2123,2180,2260,2397,2484,2559,2652,2732,2816,2955,3033,3112,3264,3353,3429,3486,3542,3608,3686,3767,3838,3926,4004,4081,4155,4234,4344,4434,4526,4618,4719,4793,4875,4976,5026,5109,5175,5267,5354,5416,5480,5543,5616,5739,5852,5956,6064,6125,6185,6271,6357,6434,6513"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,49,50,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,114,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3049,3130,3210,3298,3401,4224,4325,4453,4606,4667,4828,4925,5005,5070,5165,5229,5301,5363,5439,5502,5559,5680,5738,5799,5856,5936,6073,6160,6235,6328,6408,6492,6631,6709,6788,6940,7029,7105,7162,7218,7284,7362,7443,7514,7602,7680,7757,7831,7910,8020,8110,8202,8294,8395,8469,8551,8652,8702,8785,8851,8943,9030,9092,9156,9219,9292,9415,9528,9632,9740,9801,9938,10249,10335,10412", "endLines": "5,33,34,35,36,37,45,46,47,49,50,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,114,117,118,119", "endColumns": "12,80,79,87,102,91,100,127,83,60,64,96,79,64,94,63,71,61,75,62,56,120,57,60,56,79,136,86,74,92,79,83,138,77,78,151,88,75,56,55,65,77,80,70,87,77,76,73,78,109,89,91,91,100,73,81,100,49,82,65,91,86,61,63,62,72,122,112,103,107,60,59,85,85,76,78", "endOffsets": "319,3125,3205,3293,3396,3488,4320,4448,4532,4662,4727,4920,5000,5065,5160,5224,5296,5358,5434,5497,5554,5675,5733,5794,5851,5931,6068,6155,6230,6323,6403,6487,6626,6704,6783,6935,7024,7100,7157,7213,7279,7357,7438,7509,7597,7675,7752,7826,7905,8015,8105,8197,8289,8390,8464,8546,8647,8697,8780,8846,8938,9025,9087,9151,9214,9287,9410,9523,9627,9735,9796,9856,10019,10330,10407,10486"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7e858fb0a02ac4fcc5a44c485ded2814\\transformed\\preference-1.2.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,270,347,490,659,746", "endColumns": "68,95,76,142,168,86,80", "endOffsets": "169,265,342,485,654,741,822"}, "to": {"startLines": "48,51,113,115,121,122,123", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4537,4732,9861,10024,10592,10761,10848", "endColumns": "68,95,76,142,168,86,80", "endOffsets": "4601,4823,9933,10162,10756,10843,10924"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8a82ccfd0a18eaa7ab7c918e5f6cc0f6\\transformed\\appcompat-1.7.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,333,440,523,629,755,839,918,1009,1102,1195,1290,1388,1481,1574,1668,1759,1850,1931,2042,2150,2248,2358,2463,2571,2731,2830", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "223,328,435,518,624,750,834,913,1004,1097,1190,1285,1383,1476,1569,1663,1754,1845,1926,2037,2145,2243,2353,2458,2566,2726,2825,2907"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "324,447,552,659,742,848,974,1058,1137,1228,1321,1414,1509,1607,1700,1793,1887,1978,2069,2150,2261,2369,2467,2577,2682,2790,2950,10167", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "442,547,654,737,843,969,1053,1132,1223,1316,1409,1504,1602,1695,1788,1882,1973,2064,2145,2256,2364,2462,2572,2677,2785,2945,3044,10244"}}]}]}