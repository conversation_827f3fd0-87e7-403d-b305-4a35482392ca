com.example.uhf:xml/device_filter = 0x7f150002
com.example.uhf:xml/data_extraction_rules = 0x7f150001
com.example.uhf:xml/backup_rules = 0x7f150000
com.example.uhf:styleable/include = 0x7f1400ad
com.example.uhf:styleable/ViewStubCompat = 0x7f1400ab
com.example.uhf:styleable/View = 0x7f1400a8
com.example.uhf:styleable/Variant = 0x7f1400a7
com.example.uhf:styleable/Transform = 0x7f1400a5
com.example.uhf:styleable/Toolbar = 0x7f1400a3
com.example.uhf:styleable/TextInputEditText = 0x7f1400a0
com.example.uhf:styleable/TabLayout = 0x7f14009d
com.example.uhf:styleable/SwitchPreferenceCompat = 0x7f14009b
com.example.uhf:styleable/SwitchMaterial = 0x7f140099
com.example.uhf:styleable/SwitchCompat = 0x7f140098
com.example.uhf:styleable/StateSet = 0x7f140097
com.example.uhf:styleable/StateListDrawableItem = 0x7f140096
com.example.uhf:styleable/StateListDrawable = 0x7f140095
com.example.uhf:styleable/State = 0x7f140094
com.example.uhf:styleable/SplitPlaceholderRule = 0x7f140093
com.example.uhf:styleable/Spinner = 0x7f140090
com.example.uhf:styleable/SnackbarLayout = 0x7f14008f
com.example.uhf:styleable/Snackbar = 0x7f14008e
com.example.uhf:styleable/Slider = 0x7f14008d
com.example.uhf:styleable/ShapeableImageView = 0x7f14008b
com.example.uhf:styleable/SeekBarPreference = 0x7f140089
com.example.uhf:styleable/SearchBar = 0x7f140087
com.example.uhf:styleable/ScrollingViewBehavior_Layout = 0x7f140086
com.example.uhf:styleable/ScrimInsetsFrameLayout = 0x7f140085
com.example.uhf:styleable/RecyclerView = 0x7f140084
com.example.uhf:styleable/RecycleListView = 0x7f140083
com.example.uhf:styleable/RangeSlider = 0x7f140082
com.example.uhf:styleable/PreferenceTheme = 0x7f14007e
com.example.uhf:styleable/PreferenceImageView = 0x7f14007d
com.example.uhf:styleable/PreferenceFragmentCompat = 0x7f14007b
com.example.uhf:styleable/PreferenceFragment = 0x7f14007a
com.example.uhf:styleable/Preference = 0x7f140079
com.example.uhf:styleable/PopupWindowBackgroundState = 0x7f140078
com.example.uhf:styleable/PopupWindow = 0x7f140077
com.example.uhf:styleable/OnClick = 0x7f140075
com.example.uhf:styleable/NavigationView = 0x7f140074
com.example.uhf:styleable/NavigationRailView = 0x7f140073
com.example.uhf:styleable/MotionTelltales = 0x7f14006f
com.example.uhf:styleable/MotionLayout = 0x7f14006d
com.example.uhf:styleable/MotionLabel = 0x7f14006c
com.example.uhf:styleable/MenuView = 0x7f140067
com.example.uhf:styleable/MenuItem = 0x7f140066
com.example.uhf:styleable/MenuGroup = 0x7f140065
com.example.uhf:styleable/MaterialTimePicker = 0x7f140063
com.example.uhf:styleable/MaterialTextView = 0x7f140062
com.example.uhf:styleable/MaterialTextAppearance = 0x7f140061
com.example.uhf:styleable/MaterialSwitch = 0x7f140060
com.example.uhf:styleable/MaterialShape = 0x7f14005f
com.example.uhf:styleable/MaterialRadioButton = 0x7f14005e
com.example.uhf:styleable/MaterialDivider = 0x7f14005d
com.example.uhf:styleable/MaterialCheckBoxStates = 0x7f14005c
com.example.uhf:styleable/MaterialCardView = 0x7f14005a
com.example.uhf:styleable/MaterialCalendar = 0x7f140058
com.example.uhf:styleable/MaterialButtonToggleGroup = 0x7f140057
com.example.uhf:styleable/MaterialAlertDialogTheme = 0x7f140054
com.example.uhf:styleable/LinearLayoutCompat = 0x7f14004e
com.example.uhf:styleable/Layout = 0x7f14004d
com.example.uhf:styleable/KeyPosition = 0x7f14004a
com.example.uhf:styleable/KeyFramesAcceleration = 0x7f140048
com.example.uhf:styleable/KeyFrame = 0x7f140047
com.example.uhf:styleable/KeyCycle = 0x7f140046
com.example.uhf:styleable/ImageFilterView = 0x7f140043
com.example.uhf:styleable/GradientColorItem = 0x7f140042
com.example.uhf:styleable/Fragment = 0x7f14003f
com.example.uhf:styleable/FloatingActionButton_Behavior_Layout = 0x7f14003a
com.example.uhf:styleable/FloatingActionButton = 0x7f140039
com.example.uhf:styleable/ExtendedFloatingActionButton = 0x7f140037
com.example.uhf:styleable/DrawerLayout = 0x7f140035
com.example.uhf:styleable/DrawerArrowToggle = 0x7f140034
com.example.uhf:styleable/DialogPreference = 0x7f140033
com.example.uhf:styleable/CoordinatorLayout = 0x7f140030
com.example.uhf:styleable/ConstraintSet = 0x7f14002f
com.example.uhf:styleable/ConstraintOverride = 0x7f14002e
com.example.uhf:styleable/Constraint = 0x7f14002a
com.example.uhf:styleable/CollapsingToolbarLayout_Layout = 0x7f140027
com.example.uhf:styleable/CollapsingToolbarLayout = 0x7f140026
com.example.uhf:styleable/ChipGroup = 0x7f140022
com.example.uhf:styleable/CheckedTextView = 0x7f140020
com.example.uhf:styleable/Carousel = 0x7f14001e
com.example.uhf:styleable/CardView = 0x7f14001d
com.example.uhf:styleable/Capability = 0x7f14001c
com.example.uhf:styleable/BottomNavigationView = 0x7f140019
com.example.uhf:styleable/BottomAppBar = 0x7f140018
com.example.uhf:styleable/BaseProgressIndicator = 0x7f140017
com.example.uhf:styleable/BackgroundStyle = 0x7f140015
com.example.uhf:styleable/AppCompatTheme = 0x7f140014
com.example.uhf:styleable/AppCompatTextView = 0x7f140013
com.example.uhf:styleable/AppCompatSeekBar = 0x7f140011
com.example.uhf:styleable/AppCompatImageView = 0x7f140010
com.example.uhf:styleable/AppBarLayout = 0x7f14000c
com.example.uhf:styleable/AnimatedStateListDrawableTransition = 0x7f14000b
com.example.uhf:styleable/AnimatedStateListDrawableItem = 0x7f14000a
com.example.uhf:styleable/AnimatedStateListDrawableCompat = 0x7f140009
com.example.uhf:styleable/AlertDialog = 0x7f140008
com.example.uhf:styleable/ActionMode = 0x7f140004
com.example.uhf:styleable/ActionMenuView = 0x7f140003
com.example.uhf:styleable/ActionMenuItemView = 0x7f140002
com.example.uhf:style/Widget.MaterialComponents.Tooltip = 0x7f130490
com.example.uhf:style/Widget.MaterialComponents.Toolbar.PrimarySurface = 0x7f13048e
com.example.uhf:style/Widget.MaterialComponents.Toolbar.Primary = 0x7f13048d
com.example.uhf:style/Widget.MaterialComponents.TimePicker.Display.TextInputLayout = 0x7f130489
com.example.uhf:style/Widget.MaterialComponents.TimePicker.Display.Divider = 0x7f130486
com.example.uhf:style/Widget.MaterialComponents.TimePicker.Button = 0x7f130483
com.example.uhf:style/Widget.MaterialComponents.TimePicker = 0x7f130482
com.example.uhf:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox = 0x7f13047d
com.example.uhf:style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f13047c
com.example.uhf:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f130478
com.example.uhf:style/Widget.MaterialComponents.TextInputEditText.FilledBox = 0x7f130475
com.example.uhf:style/Widget.MaterialComponents.Snackbar.TextView = 0x7f130471
com.example.uhf:style/Widget.MaterialComponents.Snackbar.FullWidth = 0x7f130470
com.example.uhf:style/Widget.MaterialComponents.Snackbar = 0x7f13046f
com.example.uhf:style/Widget.MaterialComponents.Slider = 0x7f13046e
com.example.uhf:style/Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f13046a
com.example.uhf:style/Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f130469
com.example.uhf:style/Widget.MaterialComponents.PopupMenu = 0x7f130468
com.example.uhf:style/Widget.MaterialComponents.NavigationRailView.Compact = 0x7f130465
com.example.uhf:style/Widget.MaterialComponents.NavigationRailView.Colored.Compact = 0x7f130464
com.example.uhf:style/Widget.MaterialComponents.NavigationRailView.Colored = 0x7f130463
com.example.uhf:style/Widget.MaterialComponents.NavigationRailView = 0x7f130462
com.example.uhf:style/Widget.MaterialComponents.MaterialDivider = 0x7f130461
com.example.uhf:style/Widget.MaterialComponents.MaterialCalendar.YearNavigationButton = 0x7f130460
com.example.uhf:style/Widget.MaterialComponents.MaterialCalendar.Year.Today = 0x7f13045f
com.example.uhf:style/Widget.MaterialComponents.MaterialCalendar.Year = 0x7f13045d
com.example.uhf:style/Widget.MaterialComponents.MaterialCalendar.MonthTextView = 0x7f13045c
com.example.uhf:style/Widget.MaterialComponents.MaterialCalendar.MonthNavigationButton = 0x7f13045b
com.example.uhf:style/Widget.MaterialComponents.MaterialCalendar.Item = 0x7f13045a
com.example.uhf:style/Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f130459
com.example.uhf:style/Widget.MaterialComponents.MaterialCalendar.HeaderTitle = 0x7f130458
com.example.uhf:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f130457
com.example.uhf:style/Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton = 0x7f130452
com.example.uhf:style/Widget.MaterialComponents.MaterialCalendar.Day.Today = 0x7f13044d
com.example.uhf:style/Widget.MaterialComponents.MaterialCalendar.Day.Selected = 0x7f13044c
com.example.uhf:style/Widget.MaterialComponents.MaterialCalendar.Day = 0x7f13044a
com.example.uhf:style/Widget.MaterialComponents.MaterialCalendar = 0x7f130449
com.example.uhf:style/Widget.MaterialComponents.MaterialButtonToggleGroup = 0x7f130448
com.example.uhf:style/Widget.MaterialComponents.LinearProgressIndicator = 0x7f130447
com.example.uhf:style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon = 0x7f130444
com.example.uhf:style/Widget.MaterialComponents.ExtendedFloatingActionButton = 0x7f130443
com.example.uhf:style/Widget.MaterialComponents.CompoundButton.RadioButton = 0x7f130441
com.example.uhf:style/Widget.MaterialComponents.CompoundButton.CheckBox = 0x7f130440
com.example.uhf:style/Widget.MaterialComponents.CollapsingToolbar = 0x7f13043f
com.example.uhf:style/Widget.MaterialComponents.CircularProgressIndicator.Medium = 0x7f13043d
com.example.uhf:style/Widget.MaterialComponents.CircularProgressIndicator = 0x7f13043b
com.example.uhf:style/Widget.MaterialComponents.Chip.Choice = 0x7f130437
com.example.uhf:style/Widget.MaterialComponents.Chip.Action = 0x7f130436
com.example.uhf:style/Widget.MaterialComponents.CardView = 0x7f130434
com.example.uhf:style/Widget.MaterialComponents.Button.TextButton.Snackbar = 0x7f130431
com.example.uhf:style/Widget.MaterialComponents.Button.TextButton.Icon = 0x7f130430
com.example.uhf:style/Widget.MaterialComponents.Button.TextButton.Dialog.Icon = 0x7f13042f
com.example.uhf:style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush = 0x7f13042e
com.example.uhf:style/Widget.MaterialComponents.Button.TextButton.Dialog = 0x7f13042d
com.example.uhf:style/Widget.MaterialComponents.Button.OutlinedButton.Icon = 0x7f13042b
com.example.uhf:style/Widget.MaterialComponents.Button = 0x7f130428
com.example.uhf:style/Widget.MaterialComponents.BottomSheet.Modal = 0x7f130427
com.example.uhf:style/Widget.MaterialComponents.BottomNavigationView.Colored = 0x7f130424
com.example.uhf:style/Widget.MaterialComponents.BottomAppBar.PrimarySurface = 0x7f130422
com.example.uhf:style/Widget.MaterialComponents.BottomAppBar.Colored = 0x7f130421
com.example.uhf:style/Widget.MaterialComponents.BottomAppBar = 0x7f130420
com.example.uhf:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f13041d
com.example.uhf:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f13041c
com.example.uhf:style/Widget.MaterialComponents.AppBarLayout.PrimarySurface = 0x7f130419
com.example.uhf:style/Widget.MaterialComponents.AppBarLayout.Primary = 0x7f130418
com.example.uhf:style/Widget.MaterialComponents.ActionBar.Primary = 0x7f130413
com.example.uhf:style/Widget.Material3.Tooltip = 0x7f130412
com.example.uhf:style/Widget.Material3.Toolbar.Surface = 0x7f130411
com.example.uhf:style/Widget.Material3.Toolbar.OnSurface = 0x7f130410
com.example.uhf:style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f13040e
com.example.uhf:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f13040d
com.example.uhf:style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f13040a
com.example.uhf:style/Widget.Material3.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f130409
com.example.uhf:style/Widget.Material3.TextInputLayout.FilledBox.Dense = 0x7f130408
com.example.uhf:style/Widget.Material3.TextInputEditText.OutlinedBox = 0x7f130405
com.example.uhf:style/Widget.Material3.TextInputEditText.FilledBox = 0x7f130403
com.example.uhf:style/Widget.Material3.TabLayout.Secondary = 0x7f130402
com.example.uhf:style/Widget.Material3.TabLayout.OnSurface = 0x7f130401
com.example.uhf:style/Widget.Material3.Snackbar.TextView = 0x7f1303ff
com.example.uhf:style/Widget.Material3.Snackbar.FullWidth = 0x7f1303fe
com.example.uhf:style/Widget.Material3.Slider.Legacy.Label = 0x7f1303fc
com.example.uhf:style/Widget.Material3.SideSheet.Modal = 0x7f1303f7
com.example.uhf:style/Widget.Material3.Search.ActionButton.Overflow = 0x7f1303ee
com.example.uhf:style/Widget.Material3.PopupMenu.Overflow = 0x7f1303ed
com.example.uhf:style/Widget.Material3.PopupMenu.ListPopupWindow = 0x7f1303ec
com.example.uhf:style/Widget.Material3.PopupMenu.ContextMenu = 0x7f1303eb
com.example.uhf:style/Widget.Material3.PopupMenu = 0x7f1303ea
com.example.uhf:style/Widget.Material3.NavigationView = 0x7f1303e9
com.example.uhf:style/Widget.Material3.MaterialTimePicker.Display.TextInputLayout = 0x7f1303e4
com.example.uhf:style/Widget.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1303e3
com.example.uhf:style/Widget.Material3.MaterialTimePicker.Display.HelperText = 0x7f1303e2
com.example.uhf:style/Widget.Material3.MaterialTimePicker.Display.Divider = 0x7f1303e1
com.example.uhf:style/Widget.Material3.MaterialTimePicker.Display = 0x7f1303e0
com.example.uhf:style/Widget.Material3.MaterialTimePicker.Clock = 0x7f1303df
com.example.uhf:style/Widget.Material3.MaterialTimePicker.Button = 0x7f1303de
com.example.uhf:style/Widget.Material3.MaterialCalendar.Year.Selected = 0x7f1303d8
com.example.uhf:style/Widget.Material3.MaterialCalendar.Year = 0x7f1303d7
com.example.uhf:style/Widget.Material3.MaterialCalendar.MonthTextView = 0x7f1303d6
com.example.uhf:style/Widget.Material3.MaterialCalendar.MonthNavigationButton = 0x7f1303d5
com.example.uhf:style/Widget.Material3.MaterialCalendar.Item = 0x7f1303d4
com.example.uhf:style/Widget.Material3.MaterialCalendar.HeaderTitle = 0x7f1303d2
com.example.uhf:style/Widget.Material3.MaterialCalendar.HeaderLayout.Fullscreen = 0x7f1303cf
com.example.uhf:style/Widget.Material3.MaterialCalendar.HeaderLayout = 0x7f1303ce
com.example.uhf:style/Widget.Material3.MaterialCalendar.HeaderDivider = 0x7f1303cd
com.example.uhf:style/Widget.Material3.MaterialCalendar.Fullscreen = 0x7f1303cb
com.example.uhf:style/Widget.Material3.MaterialCalendar.DayOfWeekLabel = 0x7f1303c9
com.example.uhf:style/Widget.Material3.MaterialCalendar.Day.Selected = 0x7f1303c7
com.example.uhf:style/Widget.Material3.MaterialCalendar.Day.Invalid = 0x7f1303c6
com.example.uhf:style/Widget.Material3.MaterialCalendar.Day = 0x7f1303c5
com.example.uhf:style/Widget.Material3.MaterialCalendar = 0x7f1303c4
com.example.uhf:style/Widget.Material3.MaterialButtonToggleGroup = 0x7f1303c3
com.example.uhf:style/Widget.Material3.LinearProgressIndicator.Legacy = 0x7f1303c2
com.example.uhf:style/Widget.Material3.Light.ActionBar.Solid = 0x7f1303c0
com.example.uhf:style/Widget.Material3.FloatingActionButton.Tertiary = 0x7f1303bf
com.example.uhf:style/Widget.Material3.FloatingActionButton.Surface = 0x7f1303be
com.example.uhf:style/Widget.Material3.FloatingActionButton.Small.Primary = 0x7f1303ba
com.example.uhf:style/Widget.Material3.FloatingActionButton.Primary = 0x7f1303b8
com.example.uhf:style/Widget.Material3.FloatingActionButton.Large.Surface = 0x7f1303b6
com.example.uhf:style/Widget.Material3.FloatingActionButton.Large.Secondary = 0x7f1303b5
com.example.uhf:style/Widget.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f1303b3
com.example.uhf:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Tertiary = 0x7f1303af
com.example.uhf:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Secondary = 0x7f1303ad
com.example.uhf:style/Widget.Material3.CompoundButton.Switch = 0x7f1303aa
com.example.uhf:style/Widget.Material3.CompoundButton.RadioButton = 0x7f1303a9
com.example.uhf:style/Widget.Material3.CollapsingToolbar = 0x7f1303a4
com.example.uhf:style/Widget.Material3.CircularProgressIndicator.Medium = 0x7f1303a2
com.example.uhf:style/Widget.Material3.CircularProgressIndicator.Legacy.Small = 0x7f1303a1
com.example.uhf:style/Widget.Material3.CircularProgressIndicator.Legacy.ExtraSmall = 0x7f13039f
com.example.uhf:style/Widget.Material3.CircularProgressIndicator.ExtraSmall = 0x7f13039d
com.example.uhf:style/Widget.Material3.CircularProgressIndicator = 0x7f13039c
com.example.uhf:style/Widget.Material3.ChipGroup = 0x7f13039b
com.example.uhf:style/Widget.Material3.Chip.Suggestion.Elevated = 0x7f13039a
com.example.uhf:style/Widget.Material3.Chip.Suggestion = 0x7f130399
com.example.uhf:style/Widget.Material3.Chip.Input.Icon = 0x7f130397
com.example.uhf:style/Widget.Material3.Chip.Input = 0x7f130395
com.example.uhf:style/Widget.Material3.Chip.Filter = 0x7f130393
com.example.uhf:style/Widget.Material3.Chip.Assist.Elevated = 0x7f130392
com.example.uhf:style/Widget.Material3.Chip.Assist = 0x7f130391
com.example.uhf:style/Widget.Material3.CardView.Outlined = 0x7f13038f
com.example.uhf:style/Widget.Material3.CardView.Filled = 0x7f13038e
com.example.uhf:style/Widget.Material3.CardView.Elevated = 0x7f13038d
com.example.uhf:style/Widget.Material3.Button.TextButton.Icon = 0x7f130388
com.example.uhf:style/Widget.Material3.Button.TextButton.Dialog.Icon = 0x7f130387
com.example.uhf:style/Widget.Material3.Button.TextButton.Dialog.Flush = 0x7f130386
com.example.uhf:style/Widget.Material3.Button.TextButton = 0x7f130384
com.example.uhf:style/Widget.Material3.Button.OutlinedButton = 0x7f130382
com.example.uhf:style/Widget.Material3.Button.IconButton.Outlined = 0x7f130381
com.example.uhf:style/Widget.Material3.Button.IconButton.Filled.Tonal = 0x7f130380
com.example.uhf:style/Widget.Material3.Button.IconButton.Filled = 0x7f13037f
com.example.uhf:style/Widget.Material3.Button.IconButton = 0x7f13037e
com.example.uhf:style/Widget.Material3.Button.Icon = 0x7f13037d
com.example.uhf:style/Widget.Material3.Button.ElevatedButton.Icon = 0x7f13037c
com.example.uhf:style/Widget.Material3.Button.ElevatedButton = 0x7f13037b
com.example.uhf:style/Widget.Material3.Button = 0x7f13037a
com.example.uhf:style/Widget.Material3.BottomSheet.Modal = 0x7f130379
com.example.uhf:style/Widget.Material3.BottomSheet.DragHandle = 0x7f130378
com.example.uhf:style/Widget.Material3.BottomNavigationView.ActiveIndicator = 0x7f130376
com.example.uhf:style/Widget.Material3.BottomNavigation.Badge = 0x7f130374
com.example.uhf:style/Widget.Material3.BottomAppBar.Legacy = 0x7f130373
com.example.uhf:style/Widget.Material3.BottomAppBar.Button.Navigation = 0x7f130372
com.example.uhf:style/Widget.Material3.AutoCompleteTextView.OutlinedBox = 0x7f13036d
com.example.uhf:style/Widget.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f13036c
com.example.uhf:style/Widget.Material3.AutoCompleteTextView.FilledBox = 0x7f13036b
com.example.uhf:style/Widget.Material3.ActionBar.Solid = 0x7f130368
com.example.uhf:style/Widget.Design.TextInputLayout = 0x7f130367
com.example.uhf:style/Widget.Design.Snackbar = 0x7f130364
com.example.uhf:style/Widget.Design.NavigationView = 0x7f130362
com.example.uhf:style/Widget.Design.BottomSheet.Modal = 0x7f13035f
com.example.uhf:style/Widget.Design.BottomNavigationView = 0x7f13035e
com.example.uhf:style/Widget.Compat.NotificationActionText = 0x7f13035c
com.example.uhf:style/Widget.Compat.NotificationActionContainer = 0x7f13035b
com.example.uhf:style/Widget.AppCompat.Spinner.DropDown = 0x7f130354
com.example.uhf:style/Widget.AppCompat.Spinner = 0x7f130353
com.example.uhf:style/Widget.AppCompat.SeekBar.Discrete = 0x7f130352
com.example.uhf:style/Widget.AppCompat.SearchView.ActionBar = 0x7f130350
com.example.uhf:style/Widget.AppCompat.SearchView = 0x7f13034f
com.example.uhf:style/Widget.AppCompat.RatingBar.Small = 0x7f13034e
com.example.uhf:style/Widget.AppCompat.RatingBar.Indicator = 0x7f13034d
com.example.uhf:style/Widget.AppCompat.RatingBar = 0x7f13034c
com.example.uhf:style/Widget.AppCompat.ProgressBar = 0x7f13034a
com.example.uhf:style/Widget.AppCompat.PopupMenu.Overflow = 0x7f130348
com.example.uhf:style/Widget.AppCompat.PopupMenu = 0x7f130347
com.example.uhf:style/Widget.AppCompat.ListView.Menu = 0x7f130346
com.example.uhf:style/Widget.AppCompat.ListView = 0x7f130344
com.example.uhf:style/Widget.AppCompat.ListPopupWindow = 0x7f130343
com.example.uhf:style/Widget.AppCompat.ListMenuView = 0x7f130342
com.example.uhf:style/Widget.AppCompat.Light.PopupMenu = 0x7f13033e
com.example.uhf:style/Widget.AppCompat.Light.DropDownItem.Spinner = 0x7f13033b
com.example.uhf:style/Widget.AppCompat.Light.AutoCompleteTextView = 0x7f13033a
com.example.uhf:style/Widget.AppCompat.Light.ActivityChooserView = 0x7f130339
com.example.uhf:style/Widget.Material3.AppBarLayout = 0x7f13036a
com.example.uhf:style/Widget.AppCompat.Light.ActionMode.Inverse = 0x7f130338
com.example.uhf:style/Widget.AppCompat.Light.ActionButton.Overflow = 0x7f130337
com.example.uhf:style/Widget.AppCompat.Light.ActionButton.CloseMode = 0x7f130336
com.example.uhf:style/Widget.AppCompat.Light.ActionBar.TabView = 0x7f130333
com.example.uhf:style/Widget.AppCompat.Light.ActionBar.TabText = 0x7f130331
com.example.uhf:style/Widget.AppCompat.Light.ActionBar.TabBar.Inverse = 0x7f130330
com.example.uhf:style/Widget.AppCompat.Light.ActionBar.TabBar = 0x7f13032f
com.example.uhf:style/Widget.AppCompat.ImageButton = 0x7f13032b
com.example.uhf:style/Widget.AppCompat.EditText = 0x7f13032a
com.example.uhf:style/Widget.AppCompat.DropDownItem.Spinner = 0x7f130329
com.example.uhf:style/Widget.AppCompat.DrawerArrowToggle = 0x7f130328
com.example.uhf:style/Widget.AppCompat.CompoundButton.Switch = 0x7f130327
com.example.uhf:style/Widget.AppCompat.CompoundButton.RadioButton = 0x7f130326
com.example.uhf:style/Widget.AppCompat.ButtonBar = 0x7f130323
com.example.uhf:style/Widget.AppCompat.Button.Small = 0x7f130322
com.example.uhf:style/Widget.AppCompat.Button.Colored = 0x7f130321
com.example.uhf:style/Widget.AppCompat.Button.Borderless.Colored = 0x7f13031f
com.example.uhf:style/Widget.AppCompat.Button.Borderless = 0x7f13031e
com.example.uhf:style/Widget.AppCompat.Button = 0x7f13031d
com.example.uhf:style/Widget.AppCompat.ActionMode = 0x7f13031a
com.example.uhf:style/Widget.AppCompat.ActionButton.Overflow = 0x7f130319
com.example.uhf:style/Widget.AppCompat.ActionButton.CloseMode = 0x7f130318
com.example.uhf:style/Widget.AppCompat.ActionBar.TabText = 0x7f130315
com.example.uhf:style/Widget.AppCompat.ActionBar.TabBar = 0x7f130314
com.example.uhf:style/Widget.AppCompat.ActionBar.Solid = 0x7f130313
com.example.uhf:style/Widget.AppCompat.ActionBar = 0x7f130312
com.example.uhf:style/ThemeOverlay.MaterialComponents.Toolbar.Surface = 0x7f130311
com.example.uhf:style/ThemeOverlay.MaterialComponents.Toolbar.Primary = 0x7f130310
com.example.uhf:style/ThemeOverlay.MaterialComponents.Toolbar.Popup.Primary = 0x7f13030f
com.example.uhf:style/ThemeOverlay.MaterialComponents.TimePicker.Display = 0x7f13030d
com.example.uhf:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f130309
com.example.uhf:style/Widget.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f130450
com.example.uhf:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox = 0x7f130308
com.example.uhf:style/ThemeOverlay.MaterialComponents.TextInputEditText = 0x7f130307
com.example.uhf:style/ThemeOverlay.MaterialComponents.MaterialCalendar = 0x7f130305
com.example.uhf:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Spinner = 0x7f130304
com.example.uhf:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text.Day = 0x7f130303
com.example.uhf:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text = 0x7f130302
com.example.uhf:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Calendar = 0x7f130301
com.example.uhf:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date = 0x7f130300
com.example.uhf:style/ThemeOverlay.MaterialComponents.Light = 0x7f1302fc
com.example.uhf:style/ThemeOverlay.MaterialComponents.Dark.ActionBar = 0x7f1302f7
com.example.uhf:style/ThemeOverlay.MaterialComponents.Dark = 0x7f1302f6
com.example.uhf:style/ThemeOverlay.MaterialComponents.BottomAppBar.Surface = 0x7f1302f4
com.example.uhf:style/ThemeOverlay.MaterialComponents.BottomAppBar.Primary = 0x7f1302f3
com.example.uhf:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f1302f1
com.example.uhf:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f1302ef
com.example.uhf:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView = 0x7f1302ee
com.example.uhf:style/ThemeOverlay.MaterialComponents.ActionBar.Surface = 0x7f1302ed
com.example.uhf:style/ThemeOverlay.MaterialComponents.ActionBar = 0x7f1302eb
com.example.uhf:style/ThemeOverlay.MaterialComponents = 0x7f1302ea
com.example.uhf:style/ThemeOverlay.MaterialAlertDialog.Material3.Title.Icon = 0x7f1302e9
com.example.uhf:style/ThemeOverlay.Material3.Toolbar.Surface = 0x7f1302e8
com.example.uhf:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f1302e7
com.example.uhf:style/ThemeOverlay.Material3.TabLayout = 0x7f1302e2
com.example.uhf:style/ThemeOverlay.Material3.Snackbar = 0x7f1302e1
com.example.uhf:style/ThemeOverlay.Material3.SideSheetDialog = 0x7f1302e0
com.example.uhf:style/ThemeOverlay.Material3.Search = 0x7f1302df
com.example.uhf:style/ThemeOverlay.Material3.NavigationView = 0x7f1302dd
com.example.uhf:style/ThemeOverlay.Material3.MaterialTimePicker = 0x7f1302da
com.example.uhf:style/ThemeOverlay.Material3.MaterialCalendar = 0x7f1302d7
com.example.uhf:style/ThemeOverlay.Material3.Light = 0x7f1302d3
com.example.uhf:style/ThemeOverlay.Material3.HarmonizedColors.Empty = 0x7f1302d2
com.example.uhf:style/ThemeOverlay.Material3.HarmonizedColors = 0x7f1302d1
com.example.uhf:style/ThemeOverlay.Material3.FloatingActionButton.Secondary = 0x7f1302ce
com.example.uhf:style/ThemeOverlay.Material3.FloatingActionButton.Primary = 0x7f1302cd
com.example.uhf:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Primary = 0x7f1302c9
com.example.uhf:style/ThemeOverlay.Material3.DynamicColors.Dark = 0x7f1302c6
com.example.uhf:style/ThemeOverlay.Material3.Dialog.Alert.Framework = 0x7f1302c5
com.example.uhf:style/ThemeOverlay.Material3.Dialog = 0x7f1302c3
com.example.uhf:style/ThemeOverlay.Material3.DayNight.SideSheetDialog = 0x7f1302c2
com.example.uhf:style/ThemeOverlay.Material3.DayNight.BottomSheetDialog = 0x7f1302c1
com.example.uhf:style/ThemeOverlay.Material3.Dark.ActionBar = 0x7f1302c0
com.example.uhf:style/ThemeOverlay.Material3.Chip.Assist = 0x7f1302be
com.example.uhf:style/ThemeOverlay.Material3.Chip = 0x7f1302bd
com.example.uhf:style/ThemeOverlay.Material3.Button.TextButton.Snackbar = 0x7f1302bb
com.example.uhf:style/ThemeOverlay.Material3.Button.IconButton.Filled.Tonal = 0x7f1302b9
com.example.uhf:style/ThemeOverlay.Material3.Button.IconButton.Filled = 0x7f1302b8
com.example.uhf:style/ThemeOverlay.Material3.BottomAppBar.Legacy = 0x7f1302b2
com.example.uhf:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f1302ae
com.example.uhf:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox = 0x7f1302ad
com.example.uhf:style/ThemeOverlay.Material3.AutoCompleteTextView = 0x7f1302ac
com.example.uhf:style/ThemeOverlay.Material3.ActionBar = 0x7f1302ab
com.example.uhf:style/ThemeOverlay.Design.TextInputEditText = 0x7f1302a9
com.example.uhf:style/ThemeOverlay.AppCompat.Light = 0x7f1302a8
com.example.uhf:style/ThemeOverlay.AppCompat.DayNight.ActionBar = 0x7f1302a5
com.example.uhf:style/ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f1302a3
com.example.uhf:style/ThemeOverlay.AppCompat.ActionBar = 0x7f1302a1
com.example.uhf:style/ThemeOverlay.AppCompat = 0x7f1302a0
com.example.uhf:style/Theme.Uhfuartdemo = 0x7f13029f
com.example.uhf:style/Theme.MaterialComponents.NoActionBar.Bridge = 0x7f13029e
com.example.uhf:style/Theme.MaterialComponents.NoActionBar = 0x7f13029d
com.example.uhf:style/Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge = 0x7f130299
com.example.uhf:style/Widget.Material3.Badge = 0x7f13036f
com.example.uhf:style/Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f130296
com.example.uhf:style/Theme.MaterialComponents.Light.Dialog.Alert.Bridge = 0x7f130294
com.example.uhf:style/Theme.MaterialComponents.Light.Dialog = 0x7f130292
com.example.uhf:style/Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f130291
com.example.uhf:style/Theme.MaterialComponents.Light.DarkActionBar = 0x7f130290
com.example.uhf:styleable/KeyTimeCycle = 0x7f14004b
com.example.uhf:style/Theme.MaterialComponents.Light.BottomSheetDialog = 0x7f13028e
com.example.uhf:style/Theme.MaterialComponents.Dialog.FixedSize.Bridge = 0x7f130289
com.example.uhf:style/Theme.MaterialComponents.Dialog.FixedSize = 0x7f130288
com.example.uhf:style/Theme.MaterialComponents.Dialog.Bridge = 0x7f130287
com.example.uhf:style/Widget.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f130406
com.example.uhf:style/Theme.MaterialComponents.Dialog.Alert.Bridge = 0x7f130286
com.example.uhf:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge = 0x7f130280
com.example.uhf:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge = 0x7f13027e
com.example.uhf:style/Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge = 0x7f13027b
com.example.uhf:style/Theme.MaterialComponents.DayNight.Dialog = 0x7f130279
com.example.uhf:style/Theme.MaterialComponents.DayNight.DarkActionBar = 0x7f130277
com.example.uhf:style/Theme.MaterialComponents.DayNight.BottomSheetDialog = 0x7f130275
com.example.uhf:style/Theme.MaterialComponents.DayNight = 0x7f130274
com.example.uhf:style/Theme.MaterialComponents.CompactMenu = 0x7f130273
com.example.uhf:style/Theme.MaterialComponents.Bridge = 0x7f130272
com.example.uhf:style/Theme.MaterialComponents.BottomSheetDialog = 0x7f130271
com.example.uhf:style/Theme.MaterialComponents = 0x7f130270
com.example.uhf:style/Theme.Material3.Light.SideSheetDialog = 0x7f13026f
com.example.uhf:style/Theme.Material3.Light.DialogWhenLarge = 0x7f13026d
com.example.uhf:style/Theme.Material3.Light.Dialog.MinWidth = 0x7f13026c
com.example.uhf:style/Theme.Material3.Light.BottomSheetDialog = 0x7f130269
com.example.uhf:style/Theme.Material3.Light = 0x7f130268
com.example.uhf:style/Theme.Material3.DynamicColors.DayNight.NoActionBar = 0x7f130265
com.example.uhf:style/Theme.Material3.DynamicColors.DayNight = 0x7f130264
com.example.uhf:style/Theme.Material3.DayNight.SideSheetDialog = 0x7f130261
com.example.uhf:style/Theme.Material3.DayNight.NoActionBar = 0x7f130260
com.example.uhf:style/Theme.Material3.DayNight.DialogWhenLarge = 0x7f13025f
com.example.uhf:style/Theme.Material3.DayNight.Dialog.MinWidth = 0x7f13025e
com.example.uhf:style/Theme.Material3.DayNight.Dialog.Alert = 0x7f13025d
com.example.uhf:style/Theme.Material3.DayNight.Dialog = 0x7f13025c
com.example.uhf:style/Theme.Material3.DayNight.BottomSheetDialog = 0x7f13025b
com.example.uhf:style/Theme.Material3.DayNight = 0x7f13025a
com.example.uhf:style/Widget.Material3.SearchView = 0x7f1303f2
com.example.uhf:style/Theme.Material3.Dark.NoActionBar = 0x7f130258
com.example.uhf:style/Theme.Material3.Dark.DialogWhenLarge = 0x7f130257
com.example.uhf:style/Theme.Material3.Dark.Dialog.Alert = 0x7f130255
com.example.uhf:style/Theme.Design.NoActionBar = 0x7f130251
com.example.uhf:style/Theme.Design.Light.BottomSheetDialog = 0x7f13024f
com.example.uhf:style/Theme.Design.Light = 0x7f13024e
com.example.uhf:style/Theme.Design.BottomSheetDialog = 0x7f13024d
com.example.uhf:style/ThemeOverlay.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f13030e
com.example.uhf:style/Theme.AppCompat.NoActionBar = 0x7f13024b
com.example.uhf:style/Theme.AppCompat.Light.NoActionBar = 0x7f13024a
com.example.uhf:style/Theme.AppCompat.Light.Dialog.MinWidth = 0x7f130248
com.example.uhf:style/Theme.AppCompat.Light.Dialog.Alert = 0x7f130247
com.example.uhf:style/Theme.AppCompat.Light.Dialog = 0x7f130246
com.example.uhf:style/Theme.AppCompat.Light.DarkActionBar = 0x7f130245
com.example.uhf:styleable/FlowLayout = 0x7f14003b
com.example.uhf:style/Theme.AppCompat.Light = 0x7f130244
com.example.uhf:style/Theme.AppCompat.Empty = 0x7f130243
com.example.uhf:style/Theme.AppCompat.DialogWhenLarge = 0x7f130242
com.example.uhf:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox = 0x7f1302af
com.example.uhf:style/Theme.AppCompat.DayNight.Dialog.MinWidth = 0x7f13023c
com.example.uhf:style/Theme.AppCompat.DayNight.Dialog = 0x7f13023a
com.example.uhf:style/Theme.AppCompat.CompactMenu = 0x7f130237
com.example.uhf:style/Theme.AppCompat = 0x7f130236
com.example.uhf:style/TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f130235
com.example.uhf:style/TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f130233
com.example.uhf:style/TextAppearance.MaterialComponents.Subtitle1 = 0x7f13022f
com.example.uhf:style/TextAppearance.MaterialComponents.Overline = 0x7f13022e
com.example.uhf:style/TextAppearance.MaterialComponents.Headline6 = 0x7f13022d
com.example.uhf:style/TextAppearance.MaterialComponents.Headline5 = 0x7f13022c
com.example.uhf:style/TextAppearance.MaterialComponents.Headline4 = 0x7f13022b
com.example.uhf:style/TextAppearance.MaterialComponents.Chip = 0x7f130227
com.example.uhf:style/TextAppearance.MaterialComponents.Caption = 0x7f130226
com.example.uhf:style/TextAppearance.MaterialComponents.Button = 0x7f130225
com.example.uhf:style/TextAppearance.MaterialComponents.Body1 = 0x7f130223
com.example.uhf:style/TextAppearance.MaterialComponents.Badge = 0x7f130222
com.example.uhf:style/TextAppearance.Material3.TitleSmall = 0x7f130221
com.example.uhf:style/TextAppearance.Material3.TitleMedium = 0x7f130220
com.example.uhf:style/TextAppearance.Material3.TitleLarge = 0x7f13021f
com.example.uhf:style/TextAppearance.Material3.SearchView.Prefix = 0x7f13021e
com.example.uhf:style/TextAppearance.Material3.MaterialTimePicker.Title = 0x7f13021b
com.example.uhf:style/TextAppearance.Material3.LabelSmall = 0x7f13021a
com.example.uhf:style/TextAppearance.Material3.LabelMedium = 0x7f130219
com.example.uhf:style/TextAppearance.Material3.HeadlineMedium = 0x7f130216
com.example.uhf:style/TextAppearance.Material3.DisplaySmall = 0x7f130214
com.example.uhf:style/TextAppearance.Material3.DisplayMedium = 0x7f130213
com.example.uhf:style/TextAppearance.Material3.DisplayLarge = 0x7f130212
com.example.uhf:style/TextAppearance.M3.Sys.Typescale.TitleMedium = 0x7f13020b
com.example.uhf:style/TextAppearance.M3.Sys.Typescale.LabelSmall = 0x7f130209
com.example.uhf:style/TextAppearance.M3.Sys.Typescale.LabelLarge = 0x7f130207
com.example.uhf:style/TextAppearance.M3.Sys.Typescale.HeadlineMedium = 0x7f130205
com.example.uhf:style/TextAppearance.M3.Sys.Typescale.HeadlineLarge = 0x7f130204
com.example.uhf:style/TextAppearance.M3.Sys.Typescale.DisplayMedium = 0x7f130202
com.example.uhf:style/TextAppearance.M3.Sys.Typescale.BodySmall = 0x7f130200
com.example.uhf:style/TextAppearance.M3.Sys.Typescale.BodyMedium = 0x7f1301ff
com.example.uhf:style/TextAppearance.Design.Tab = 0x7f1301fd
com.example.uhf:style/TextAppearance.Design.Suffix = 0x7f1301fc
com.example.uhf:style/TextAppearance.Design.Snackbar.Message = 0x7f1301fb
com.example.uhf:style/TextAppearance.Design.Prefix = 0x7f1301fa
com.example.uhf:style/TextAppearance.Design.HelperText = 0x7f1301f7
com.example.uhf:style/TextAppearance.Design.Error = 0x7f1301f6
com.example.uhf:style/TextAppearance.Design.CollapsingToolbar.Expanded = 0x7f1301f3
com.example.uhf:style/TextAppearance.Compat.Notification.Title = 0x7f1301f2
com.example.uhf:style/TextAppearance.Compat.Notification.Time = 0x7f1301f1
com.example.uhf:style/TextAppearance.Compat.Notification.Info = 0x7f1301ef
com.example.uhf:style/TextAppearance.Compat.Notification = 0x7f1301ee
com.example.uhf:style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f1301ed
com.example.uhf:style/TextAppearance.AppCompat.Widget.Switch = 0x7f1301ec
com.example.uhf:style/TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f1301eb
com.example.uhf:style/ThemeOverlay.AppCompat.Dialog.Alert = 0x7f1302a7
com.example.uhf:style/TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f1301ea
com.example.uhf:style/TextAppearance.AppCompat.Widget.DropDownItem = 0x7f1301e8
com.example.uhf:style/TextAppearance.AppCompat.Widget.Button.Colored = 0x7f1301e6
com.example.uhf:style/TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse = 0x7f1301e3
com.example.uhf:style/TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f1301e2
com.example.uhf:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse = 0x7f1301e1
com.example.uhf:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f1301e0
com.example.uhf:style/TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f1301df
com.example.uhf:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f1301dd
com.example.uhf:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f1301dc
com.example.uhf:style/TextAppearance.AppCompat.Title = 0x7f1301d8
com.example.uhf:style/TextAppearance.AppCompat.Subhead = 0x7f1301d6
com.example.uhf:style/TextAppearance.AppCompat.Small = 0x7f1301d4
com.example.uhf:style/TextAppearance.AppCompat.Medium.Inverse = 0x7f1301d0
com.example.uhf:style/TextAppearance.AppCompat.Medium = 0x7f1301cf
com.example.uhf:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f1301ce
com.example.uhf:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f1301cd
com.example.uhf:style/TextAppearance.AppCompat.Light.SearchResult.Subtitle = 0x7f1301cb
com.example.uhf:style/TextAppearance.AppCompat.Large.Inverse = 0x7f1301ca
com.example.uhf:style/TextAppearance.AppCompat.Inverse = 0x7f1301c8
com.example.uhf:style/TextAppearance.AppCompat.Display2 = 0x7f1301c4
com.example.uhf:style/TextAppearance.AppCompat.Button = 0x7f1301c1
com.example.uhf:style/TextAppearance.AppCompat.Body2 = 0x7f1301c0
com.example.uhf:style/TextAppearance.AppCompat.Body1 = 0x7f1301bf
com.example.uhf:style/ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox = 0x7f1301bd
com.example.uhf:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year = 0x7f1301bc
com.example.uhf:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen = 0x7f1301bb
com.example.uhf:style/ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton = 0x7f1301b8
com.example.uhf:style/ThemeOverlay.Material3.FloatingActionButton.Tertiary = 0x7f1302d0
com.example.uhf:style/ShapeAppearanceOverlay.MaterialComponents.BottomSheet = 0x7f1301b6
com.example.uhf:style/ShapeAppearanceOverlay.Material3.SearchView = 0x7f1301b4
com.example.uhf:style/ShapeAppearanceOverlay.Material3.SearchBar = 0x7f1301b3
com.example.uhf:style/ShapeAppearanceOverlay.Material3.NavigationView.Item = 0x7f1301b2
com.example.uhf:style/Widget.MaterialComponents.Chip.Entry = 0x7f130438
com.example.uhf:style/ShapeAppearanceOverlay.Material3.FloatingActionButton = 0x7f1301b1
com.example.uhf:style/ShapeAppearanceOverlay.Material3.Corner.Top = 0x7f1301b0
com.example.uhf:style/ShapeAppearanceOverlay.Material3.Corner.Left = 0x7f1301ae
com.example.uhf:style/ShapeAppearanceOverlay.Material3.Corner.Bottom = 0x7f1301ad
com.example.uhf:style/ShapeAppearanceOverlay.Material3.Chip = 0x7f1301ac
com.example.uhf:style/ShapeAppearanceOverlay.Material3.Button = 0x7f1301ab
com.example.uhf:style/ShapeAppearance.MaterialComponents.SmallComponent = 0x7f1301a9
com.example.uhf:style/ShapeAppearance.MaterialComponents.MediumComponent = 0x7f1301a8
com.example.uhf:style/ShapeAppearance.MaterialComponents.LargeComponent = 0x7f1301a7
com.example.uhf:style/TextAppearance.Material3.BodyLarge = 0x7f13020f
com.example.uhf:style/ShapeAppearance.Material3.Tooltip = 0x7f1301a4
com.example.uhf:style/ShapeAppearance.Material3.SmallComponent = 0x7f1301a3
com.example.uhf:style/ShapeAppearance.Material3.NavigationBarView.ActiveIndicator = 0x7f1301a2
com.example.uhf:style/ShapeAppearance.Material3.MediumComponent = 0x7f1301a1
com.example.uhf:style/ShapeAppearance.Material3.LargeComponent = 0x7f1301a0
com.example.uhf:style/ShapeAppearance.Material3.Corner.Small = 0x7f13019f
com.example.uhf:style/Widget.AppCompat.TextView.SpinnerItem = 0x7f130358
com.example.uhf:style/ShapeAppearance.Material3.Corner.Large = 0x7f13019c
com.example.uhf:style/ShapeAppearance.Material3.Corner.ExtraSmall = 0x7f13019a
com.example.uhf:style/ShapeAppearance.Material3.Corner.ExtraLarge = 0x7f130199
com.example.uhf:style/ShapeAppearance.M3.Sys.Shape.Corner.Medium = 0x7f130196
com.example.uhf:style/ShapeAppearance.M3.Sys.Shape.Corner.Large = 0x7f130195
com.example.uhf:style/ThemeOverlay.Material3.MaterialAlertDialog.Centered = 0x7f1302d6
com.example.uhf:style/ShapeAppearance.M3.Sys.Shape.Corner.Full = 0x7f130194
com.example.uhf:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraSmall = 0x7f130193
com.example.uhf:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize = 0x7f13027d
com.example.uhf:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraLarge = 0x7f130192
com.example.uhf:style/ShapeAppearance.M3.Comp.TextButton.Container.Shape = 0x7f130191
com.example.uhf:attr/tabIndicatorFullWidth = 0x7f04045e
com.example.uhf:color/material_dynamic_tertiary10 = 0x7f060273
com.example.uhf:style/ShapeAppearance.M3.Comp.Switch.Handle.Shape = 0x7f13018e
com.example.uhf:style/ShapeAppearance.M3.Comp.Sheet.Side.Docked.Container.Shape = 0x7f13018d
com.example.uhf:style/ShapeAppearance.M3.Comp.NavigationRail.Container.Shape = 0x7f130189
com.example.uhf:style/MyDropDownNav = 0x7f130141
com.example.uhf:style/ShapeAppearance.M3.Comp.NavigationRail.ActiveIndicator.Shape = 0x7f130188
com.example.uhf:macro/m3_comp_radio_button_unselected_focus_state_layer_color = 0x7f0d00e0
com.example.uhf:style/ShapeAppearance.M3.Comp.BottomAppBar.Container.Shape = 0x7f130182
com.example.uhf:attr/textLocale = 0x7f0404ad
com.example.uhf:style/ShapeAppearance.M3.Comp.Badge.Shape = 0x7f130181
com.example.uhf:style/ShapeAppearance.M3.Comp.Badge.Large.Shape = 0x7f130180
com.example.uhf:drawable/material_cursor_drawable = 0x7f0800be
com.example.uhf:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text = 0x7f13017c
com.example.uhf:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1 = 0x7f130179
com.example.uhf:styleable/SwitchPreference = 0x7f14009a
com.example.uhf:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Title = 0x7f130177
com.example.uhf:id/uniform = 0x7f0902cd
com.example.uhf:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup = 0x7f130173
com.example.uhf:string/bottomsheet_drag_handle_clicked = 0x7f1200a3
com.example.uhf:style/PreferenceThemeOverlay.v14.Material = 0x7f13016e
com.example.uhf:style/PreferenceFragment.Material = 0x7f130168
com.example.uhf:style/PreferenceFragment = 0x7f130167
com.example.uhf:style/Preference.SeekBarPreference.Material = 0x7f130161
com.example.uhf:style/Preference.PreferenceScreen.Material = 0x7f13015f
com.example.uhf:style/Preference.DialogPreference.Material = 0x7f130158
com.example.uhf:attr/itemHorizontalTranslationEnabled = 0x7f04026b
com.example.uhf:string/preferences_search_country = 0x7f120305
com.example.uhf:style/Preference.SwitchPreferenceCompat = 0x7f130164
com.example.uhf:style/Preference.DialogPreference.EditTextPreference.Material = 0x7f130157
com.example.uhf:string/ap_list_header_channel = 0x7f12004a
com.example.uhf:style/Preference.DialogPreference = 0x7f130155
com.example.uhf:color/m3_sys_color_dark_on_background = 0x7f06017c
com.example.uhf:style/Preference.CheckBoxPreference.Material = 0x7f130154
com.example.uhf:style/Preference.Category.Material = 0x7f130152
com.example.uhf:style/Preference.Category = 0x7f130151
com.example.uhf:attr/methodName = 0x7f040334
com.example.uhf:style/Platform.Widget.AppCompat.Spinner = 0x7f13014f
com.example.uhf:style/Platform.MaterialComponents.Light.Dialog = 0x7f130147
com.example.uhf:dimen/m3_sys_motion_easing_standard_accelerate_control_y1 = 0x7f070215
com.example.uhf:style/Platform.MaterialComponents.Light = 0x7f130146
com.example.uhf:attr/expandedTitleMargin = 0x7f0401d7
com.example.uhf:style/MyDropDownListView = 0x7f130140
com.example.uhf:style/Widget.MaterialComponents.TimePicker.Display.HelperText = 0x7f130487
com.example.uhf:style/Widget.Material3.TabLayout = 0x7f130400
com.example.uhf:array/c4000name = 0x7f03002d
com.example.uhf:style/MyActionBarTabStyle = 0x7f13013e
com.example.uhf:style/MyActionBarStyle = 0x7f13013d
com.example.uhf:style/MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f13013b
com.example.uhf:style/MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f130139
com.example.uhf:style/MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked = 0x7f130138
com.example.uhf:style/MaterialAlertDialog.MaterialComponents.Body.Text = 0x7f130134
com.example.uhf:style/MaterialAlertDialog.Material3.Title.Text = 0x7f130131
com.example.uhf:attr/checkedIconSize = 0x7f0400c2
com.example.uhf:style/MaterialAlertDialog.Material3.Title.Icon.CenterStacked = 0x7f13012e
com.example.uhf:style/Preference.DialogPreference.EditTextPreference = 0x7f130156
com.example.uhf:style/MaterialAlertDialog.Material3.Body.Text = 0x7f13012b
com.example.uhf:style/MaterialAlertDialog.Material3.Animation = 0x7f13012a
com.example.uhf:style/CardView.Light = 0x7f130127
com.example.uhf:string/rfid_msg_data = 0x7f120349
com.example.uhf:style/CardView = 0x7f130125
com.example.uhf:dimen/mtrl_badge_text_horizontal_edge_offset = 0x7f070253
com.example.uhf:string/tvPtr = 0x7f12041b
com.example.uhf:style/Base.Widget.MaterialComponents.TextView = 0x7f130123
com.example.uhf:attr/layout_constraintWidth_default = 0x7f0402c3
com.example.uhf:style/Base.Widget.MaterialComponents.TextInputEditText = 0x7f130121
com.example.uhf:style/Base.Widget.MaterialComponents.PopupMenu.Overflow = 0x7f13011e
com.example.uhf:dimen/m3_btn_stroke_size = 0x7f0700e2
com.example.uhf:style/Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f13011d
com.example.uhf:attr/placeholderTextAppearance = 0x7f0403a4
com.example.uhf:style/Base.Widget.MaterialComponents.PopupMenu = 0x7f13011b
com.example.uhf:style/Base.Widget.MaterialComponents.Chip = 0x7f130118
com.example.uhf:string/uhf_msg_filter_addr_not_null = 0x7f120446
com.example.uhf:style/Base.Widget.MaterialComponents.CheckedTextView = 0x7f130117
com.example.uhf:macro/m3_comp_outlined_button_focus_outline_color = 0x7f0d00a4
com.example.uhf:style/Base.Widget.Material3.TabLayout.Secondary = 0x7f130115
com.example.uhf:style/Base.Widget.Material3.TabLayout = 0x7f130113
com.example.uhf:style/Base.Widget.Material3.Snackbar = 0x7f130112
com.example.uhf:style/Base.Widget.Material3.FloatingActionButton = 0x7f13010d
com.example.uhf:style/Base.Widget.Material3.ExtendedFloatingActionButton.Icon = 0x7f13010c
com.example.uhf:style/Base.Widget.Material3.CompoundButton.Switch = 0x7f13010a
com.example.uhf:style/Base.Widget.Material3.CompoundButton.RadioButton = 0x7f130109
com.example.uhf:dimen/mtrl_btn_inset = 0x7f070267
com.example.uhf:style/Base.Widget.Material3.CardView = 0x7f130105
com.example.uhf:attr/helperTextTextColor = 0x7f040235
com.example.uhf:attr/recyclerViewStyle = 0x7f0403ce
com.example.uhf:style/Base.Widget.AppCompat.Toolbar.Button.Navigation = 0x7f130100
com.example.uhf:style/Base.Widget.AppCompat.SeekBar.Discrete = 0x7f1300fa
com.example.uhf:style/Base.Widget.AppCompat.SeekBar = 0x7f1300f9
com.example.uhf:dimen/m3_simple_item_color_selected_alpha = 0x7f0701ef
com.example.uhf:style/Base.Widget.AppCompat.ProgressBar.Horizontal = 0x7f1300f3
com.example.uhf:style/Base.Widget.AppCompat.ProgressBar = 0x7f1300f2
com.example.uhf:style/Base.Widget.AppCompat.PopupWindow = 0x7f1300f1
com.example.uhf:style/Base.Widget.AppCompat.ListView.Menu = 0x7f1300ee
com.example.uhf:style/Base.Widget.AppCompat.ListMenuView = 0x7f1300ea
com.example.uhf:style/Base.Widget.AppCompat.Light.PopupMenu = 0x7f1300e8
com.example.uhf:style/Base.Widget.AppCompat.CompoundButton.Switch = 0x7f1300dc
com.example.uhf:string/mtrl_picker_end_date_description = 0x7f12029a
com.example.uhf:dimen/m3_comp_fab_primary_hover_container_elevation = 0x7f07011d
com.example.uhf:style/Base.Widget.AppCompat.ButtonBar.AlertDialog = 0x7f1300d9
com.example.uhf:style/Base.Widget.AppCompat.Button.Small = 0x7f1300d7
com.example.uhf:style/Base.Widget.AppCompat.Button.Colored = 0x7f1300d6
com.example.uhf:style/Base.Widget.AppCompat.ActionMode = 0x7f1300cf
com.example.uhf:style/Base.Widget.AppCompat.ActionButton.Overflow = 0x7f1300ce
com.example.uhf:color/abc_tint_seek_thumb = 0x7f060016
com.example.uhf:style/Base.Widget.AppCompat.ActionBar = 0x7f1300c7
com.example.uhf:style/Base.V7.Widget.AppCompat.Toolbar = 0x7f1300c6
com.example.uhf:string/mtrl_picker_toggle_to_calendar_input_mode = 0x7f1202b0
com.example.uhf:style/Base.V7.ThemeOverlay.AppCompat.Dialog = 0x7f1300c3
com.example.uhf:style/Base.V7.Theme.AppCompat.Light.Dialog = 0x7f1300c2
com.example.uhf:style/Base.V7.Theme.AppCompat.Dialog = 0x7f1300c0
com.example.uhf:styleable/ConstraintLayout_ReactiveGuide = 0x7f14002c
com.example.uhf:macro/m3_comp_switch_unselected_pressed_handle_color = 0x7f0d013b
com.example.uhf:style/Base.V26.Theme.AppCompat.Light = 0x7f1300bb
com.example.uhf:string/m3_sys_motion_easing_legacy_accelerate = 0x7f12021a
com.example.uhf:string/num = 0x7f1202de
com.example.uhf:style/Base.V22.Theme.AppCompat = 0x7f1300b2
com.example.uhf:attr/layout_constraintBaseline_toBottomOf = 0x7f04029d
com.example.uhf:style/Base.V21.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1300b1
com.example.uhf:style/Base.V21.ThemeOverlay.AppCompat.Dialog = 0x7f1300ae
com.example.uhf:style/Base.V21.Theme.MaterialComponents.Light.Dialog = 0x7f1300ad
com.example.uhf:style/Base.V21.Theme.MaterialComponents.Light = 0x7f1300ac
com.example.uhf:style/Base.V21.Theme.AppCompat.Light = 0x7f1300a8
com.example.uhf:style/Base.V14.Widget.MaterialComponents.AutoCompleteTextView = 0x7f1300a5
com.example.uhf:style/Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f1300a4
com.example.uhf:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog = 0x7f1300a2
com.example.uhf:style/Platform.ThemeOverlay.AppCompat.Dark = 0x7f130149
com.example.uhf:style/Base.V14.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1300a1
com.example.uhf:color/m3_dynamic_dark_hint_foreground = 0x7f06009b
com.example.uhf:style/Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f13009e
com.example.uhf:style/Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f13009c
com.example.uhf:drawable/ic_clock_black_24dp = 0x7f0800a3
com.example.uhf:style/Base.V14.Theme.MaterialComponents.Dialog.Bridge = 0x7f130099
com.example.uhf:style/Base.V14.Theme.MaterialComponents.Dialog = 0x7f130098
com.example.uhf:style/Base.V14.Theme.MaterialComponents = 0x7f130096
com.example.uhf:style/Base.V14.Theme.Material3.Light = 0x7f130092
com.example.uhf:style/Base.V14.Theme.Material3.Dark.SideSheetDialog = 0x7f130091
com.example.uhf:style/Base.V14.Theme.Material3.Dark = 0x7f13008e
com.example.uhf:id/mtrl_calendar_year_selector_frame = 0x7f0901c3
com.example.uhf:string/preferences_decode_QR_title = 0x7f1202f7
com.example.uhf:style/Base.ThemeOverlay.Material3.TextInputEditText = 0x7f130088
com.example.uhf:style/Base.ThemeOverlay.Material3.SideSheetDialog = 0x7f130087
com.example.uhf:dimen/m3_comp_navigation_bar_focus_state_layer_opacity = 0x7f07013f
com.example.uhf:style/Base.ThemeOverlay.Material3.BottomSheetDialog = 0x7f130085
com.example.uhf:string/sbc_name = 0x7f120360
com.example.uhf:style/Base.ThemeOverlay.AppCompat.Dialog.Alert = 0x7f130082
com.example.uhf:layout/select_dialog_item_material = 0x7f0c008a
com.example.uhf:style/Base.ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f130080
com.example.uhf:style/Base.ThemeOverlay.AppCompat.Dark = 0x7f13007f
com.example.uhf:style/Base.Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f13007a
com.example.uhf:style/Base.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f130078
com.example.uhf:style/Base.Theme.MaterialComponents.Light.Dialog.Alert = 0x7f130077
com.example.uhf:drawable/abc_list_selector_disabled_holo_dark = 0x7f080055
com.example.uhf:style/Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f130075
com.example.uhf:style/Base.Theme.MaterialComponents.Light.Bridge = 0x7f130073
com.example.uhf:color/material_dynamic_color_light_on_error_container = 0x7f06023d
com.example.uhf:style/Base.Theme.MaterialComponents.Dialog.MinWidth = 0x7f130070
com.example.uhf:style/MaterialAlertDialog.MaterialComponents = 0x7f130133
com.example.uhf:macro/m3_comp_extended_fab_tertiary_icon_color = 0x7f0d0035
com.example.uhf:style/Base.Theme.MaterialComponents.Dialog.FixedSize = 0x7f13006f
com.example.uhf:style/Base.Theme.MaterialComponents.Dialog.Alert = 0x7f13006d
com.example.uhf:color/m3_ref_palette_neutral24 = 0x7f060125
com.example.uhf:style/Base.Theme.MaterialComponents = 0x7f130069
com.example.uhf:string/material_clock_toggle_content_description = 0x7f120221
com.example.uhf:style/Base.Theme.Material3.Light.SideSheetDialog = 0x7f130068
com.example.uhf:style/Base.Theme.Material3.Light.DialogWhenLarge = 0x7f130067
com.example.uhf:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y1 = 0x7f070201
com.example.uhf:style/Base.Theme.Material3.Light.Dialog.FixedSize = 0x7f130066
com.example.uhf:style/Base.Theme.Material3.Dark.DialogWhenLarge = 0x7f130061
com.example.uhf:color/material_dynamic_tertiary50 = 0x7f060278
com.example.uhf:style/Base.Theme.AppCompat.Light.DialogWhenLarge = 0x7f13005c
com.example.uhf:style/Base.Theme.AppCompat.Light.Dialog.MinWidth = 0x7f13005b
com.example.uhf:dimen/activity_horizontal_margin = 0x7f070051
com.example.uhf:style/Base.Theme.AppCompat.Light.Dialog.FixedSize = 0x7f13005a
com.example.uhf:layout/activity_test = 0x7f0c001d
com.example.uhf:style/Base.Theme.AppCompat.Dialog.FixedSize = 0x7f130053
com.example.uhf:layout/m3_auto_complete_simple_item = 0x7f0c0042
com.example.uhf:style/Base.Theme.AppCompat.Dialog.Alert = 0x7f130052
com.example.uhf:style/Base.Widget.AppCompat.Light.ActionBar = 0x7f1300e2
com.example.uhf:layout/design_navigation_menu = 0x7f0c002a
com.example.uhf:style/Base.Theme.AppCompat.CompactMenu = 0x7f130050
com.example.uhf:id/realtabcontent = 0x7f090231
com.example.uhf:style/Base.Theme.AppCompat = 0x7f13004f
com.example.uhf:style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f13004c
com.example.uhf:style/Base.TextAppearance.MaterialComponents.Button = 0x7f130049
com.example.uhf:style/Base.TextAppearance.AppCompat.Widget.Switch = 0x7f130045
com.example.uhf:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f130043
com.example.uhf:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f130042
com.example.uhf:style/Base.TextAppearance.AppCompat.Widget.Button = 0x7f13003d
com.example.uhf:style/ShapeAppearance.M3.Comp.NavigationBar.Container.Shape = 0x7f130186
com.example.uhf:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f130038
com.example.uhf:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f130037
com.example.uhf:attr/boxStrokeWidth = 0x7f04008e
com.example.uhf:style/Base.TextAppearance.AppCompat.Title = 0x7f130033
com.example.uhf:style/Base.TextAppearance.AppCompat.Small = 0x7f13002f
com.example.uhf:dimen/abc_action_bar_stacked_tab_max_width = 0x7f07000a
com.example.uhf:style/Base.TextAppearance.AppCompat.Medium.Inverse = 0x7f13002a
com.example.uhf:styleable/TextInputLayout = 0x7f1400a1
com.example.uhf:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f130028
com.example.uhf:style/Base.TextAppearance.AppCompat.Large = 0x7f130025
com.example.uhf:style/Base.TextAppearance.AppCompat.Inverse = 0x7f130024
com.example.uhf:style/Base.TextAppearance.AppCompat.Display3 = 0x7f130021
com.example.uhf:style/Base.TextAppearance.AppCompat.Button = 0x7f13001d
com.example.uhf:attr/statusBarBackground = 0x7f040436
com.example.uhf:style/Base.TextAppearance.AppCompat = 0x7f13001a
com.example.uhf:attr/fabAlignmentModeEndMargin = 0x7f0401e7
com.example.uhf:style/Base.MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f130018
com.example.uhf:string/fingerprint_title_create = 0x7f1201a7
com.example.uhf:style/Base.Widget.AppCompat.ListView = 0x7f1300ec
com.example.uhf:style/Base.DialogWindowTitle.AppCompat = 0x7f130015
com.example.uhf:style/Base.Animation.AppCompat.Tooltip = 0x7f130013
com.example.uhf:id/showHome = 0x7f09025e
com.example.uhf:style/Base.Animation.AppCompat.DropDownUp = 0x7f130012
com.example.uhf:style/Base.AlertDialog.AppCompat = 0x7f13000f
com.example.uhf:style/AppTheme = 0x7f13000e
com.example.uhf:style/Animation.MaterialComponents.BottomSheetDialog = 0x7f13000c
com.example.uhf:style/Theme.MaterialComponents.Dialog.MinWidth = 0x7f13028a
com.example.uhf:style/Animation.AppCompat.Tooltip = 0x7f130006
com.example.uhf:id/center_horizontal = 0x7f0900d6
com.example.uhf:style/Animation.AppCompat.Dialog = 0x7f130004
com.example.uhf:styleable/ColorStateListItem = 0x7f140028
com.example.uhf:style/AlertDialog.AppCompat.Light = 0x7f130003
com.example.uhf:style/ActionBarBaseTheme1 = 0x7f130001
com.example.uhf:style/Base.Widget.AppCompat.ButtonBar = 0x7f1300d8
com.example.uhf:style/ActionBarBaseTheme = 0x7f130000
com.example.uhf:id/etData_filter_deact = 0x7f090127
com.example.uhf:string/zxing_app_name = 0x7f1204c2
com.example.uhf:string/yid_title_scan_compare = 0x7f1204c0
com.example.uhf:string/yid_msg_scan_error = 0x7f1204bc
com.example.uhf:string/yid_msg_scan_cancel = 0x7f1204bb
com.example.uhf:string/word = 0x7f1204b8
com.example.uhf:color/m3_radiobutton_button_tint = 0x7f0600b7
com.example.uhf:string/wifi_ssid_label = 0x7f1204b6
com.example.uhf:string/volum_title_voice_ring = 0x7f1204b4
com.example.uhf:string/volum_title_voice_notification = 0x7f1204b3
com.example.uhf:string/volum_title_voice_music = 0x7f1204b2
com.example.uhf:style/Widget.MaterialComponents.CompoundButton.Switch = 0x7f130442
com.example.uhf:string/volum_title_voice_call = 0x7f1204b1
com.example.uhf:attr/shapeAppearanceCornerSmall = 0x7f0403f2
com.example.uhf:color/m3_filled_icon_button_container_color_selector = 0x7f0600a7
com.example.uhf:macro/m3_comp_outlined_text_field_focus_label_text_color = 0x7f0d00ba
com.example.uhf:string/volum_title_voice_alarm = 0x7f1204b0
com.example.uhf:layout/uhf_readtag_fragment = 0x7f0c0094
com.example.uhf:string/button_wifi = 0x7f1200e2
com.example.uhf:string/title_activity_bdnav = 0x7f1203a8
com.example.uhf:string/volum_title_systen = 0x7f1204af
com.example.uhf:id/action_mode_bar_stub = 0x7f090067
com.example.uhf:string/v7_preference_off = 0x7f1204ad
com.example.uhf:string/update_title_update_now = 0x7f1204aa
com.example.uhf:string/update_title_ok = 0x7f1204a8
com.example.uhf:id/ratio = 0x7f090210
com.example.uhf:string/update_msg_version_info_fail = 0x7f1204a6
com.example.uhf:style/ThemeOverlay.Material3.MaterialCalendar.HeaderCancelButton = 0x7f1302d9
com.example.uhf:string/update_msg_updateing = 0x7f1204a5
com.example.uhf:attr/motionPath = 0x7f040367
com.example.uhf:string/m3_exceed_max_badge_text_suffix = 0x7f120210
com.example.uhf:string/update_msg_diag_title_two = 0x7f1204a3
com.example.uhf:string/update_msg_curr_new = 0x7f1204a1
com.example.uhf:style/TextAppearance.AppCompat.Display1 = 0x7f1301c3
com.example.uhf:string/up_msg_start_time = 0x7f12049c
com.example.uhf:string/up_msg_file_size = 0x7f120499
com.example.uhf:string/ul_title_write_AFI = 0x7f120495
com.example.uhf:string/ul_title_scan = 0x7f120493
com.example.uhf:string/uhf_title_user = 0x7f12048d
com.example.uhf:string/uhf_title_tid = 0x7f12048b
com.example.uhf:id/etPtr_filter_perm = 0x7f09013e
com.example.uhf:style/Base.TextAppearance.AppCompat.Widget.Button.Colored = 0x7f13003f
com.example.uhf:string/uhf_title_read = 0x7f120489
com.example.uhf:dimen/mtrl_calendar_header_content_padding_fullscreen = 0x7f070284
com.example.uhf:string/uhf_title_open = 0x7f120486
com.example.uhf:style/Base.Theme.Material3.Dark.Dialog.FixedSize = 0x7f130060
com.example.uhf:string/uhf_title_kill = 0x7f120483
com.example.uhf:string/uhf_title_filter = 0x7f120482
com.example.uhf:string/uhf_title_dbm = 0x7f120481
com.example.uhf:string/uhf_radar_loaction = 0x7f12047e
com.example.uhf:string/uhf_msg_write_succ = 0x7f12047c
com.example.uhf:macro/m3_comp_time_input_time_input_field_supporting_text_color = 0x7f0d014a
com.example.uhf:string/uhf_msg_write_must_not_null = 0x7f12047b
com.example.uhf:string/uhf_msg_tab_set = 0x7f120473
com.example.uhf:macro/m3_comp_fab_tertiary_icon_color = 0x7f0d0040
com.example.uhf:string/uhf_msg_tab_kill = 0x7f12046e
com.example.uhf:string/uhf_msg_set_protocol_succ = 0x7f12046a
com.example.uhf:string/uhf_msg_set_frequency_succ = 0x7f120466
com.example.uhf:string/uhf_msg_set_frequency_fail = 0x7f120465
com.example.uhf:string/uhf_msg_set_frehop_fail = 0x7f120463
com.example.uhf:macro/m3_comp_navigation_rail_label_text_type = 0x7f0d009f
com.example.uhf:string/uhf_msg_read_power_succ = 0x7f12045b
com.example.uhf:style/Widget.Material3.CircularProgressIndicator.Legacy = 0x7f13039e
com.example.uhf:string/uhf_msg_read_data_fail = 0x7f120457
com.example.uhf:string/uhf_msg_len_not_null = 0x7f120451
com.example.uhf:string/uhf_msg_len_must_decimal = 0x7f120450
com.example.uhf:attr/insetForeground = 0x7f040261
com.example.uhf:string/uhf_msg_get_para_fail = 0x7f12044b
com.example.uhf:string/uhf_msg_filter_len_not_null = 0x7f12044a
com.example.uhf:color/m3_slider_thumb_color_legacy = 0x7f060173
com.example.uhf:string/uhf_msg_filter_len_must_decimal = 0x7f120449
com.example.uhf:attr/compatShadowEnabled = 0x7f04013a
com.example.uhf:layout/preference_widget_seekbar = 0x7f0c0085
com.example.uhf:string/uhf_msg_filter_data_must_hex = 0x7f120447
com.example.uhf:string/uhf_msg_filter_addr_must_decimal = 0x7f120445
com.example.uhf:string/fingerprint_title_PSW = 0x7f1201a5
com.example.uhf:string/uhf_msg_export_data_empty = 0x7f120444
com.example.uhf:string/uhf_msg_addr_must_len8 = 0x7f120442
com.example.uhf:string/uhf_btn_setLinkParams = 0x7f12043a
com.example.uhf:color/black = 0x7f060021
com.example.uhf:string/uhf_btn_memoryBank = 0x7f120439
com.example.uhf:style/MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked = 0x7f13013a
com.example.uhf:string/tvUII_Lock = 0x7f120430
com.example.uhf:string/tvTagUii_Write = 0x7f12042f
com.example.uhf:attr/currentState = 0x7f040168
com.example.uhf:attr/closeItemLayout = 0x7f0400f0
com.example.uhf:string/tvTagUii_Data = 0x7f12042b
com.example.uhf:color/primary_dark_material_dark = 0x7f060310
com.example.uhf:string/tvTagLen = 0x7f120429
com.example.uhf:string/tvTagCount = 0x7f120427
com.example.uhf:string/tvStatus_Data = 0x7f120423
com.example.uhf:string/tvStartFreD = 0x7f120421
com.example.uhf:attr/forceApplySystemWindowInsetTop = 0x7f040225
com.example.uhf:string/tvQTPublic = 0x7f120420
com.example.uhf:string/tvPtr2_Read = 0x7f12041c
com.example.uhf:style/ThemeOverlay.Material3.BottomAppBar = 0x7f1302b1
com.example.uhf:drawable/$avd_show_password__1 = 0x7f080004
com.example.uhf:string/tvOrder = 0x7f120419
com.example.uhf:string/tvOption_Write = 0x7f120418
com.example.uhf:styleable/ActionBar = 0x7f140000
com.example.uhf:string/tvMHz = 0x7f120414
com.example.uhf:string/tvLockCode_Lock = 0x7f120413
com.example.uhf:string/tvLen_Data = 0x7f120411
com.example.uhf:attr/textAppearanceLabelLarge = 0x7f04048a
com.example.uhf:string/tvLen2_Read = 0x7f120410
com.example.uhf:string/tvLen = 0x7f12040f
com.example.uhf:string/tvFreRange = 0x7f120408
com.example.uhf:string/tvFreI = 0x7f120407
com.example.uhf:string/tvFreD = 0x7f120405
com.example.uhf:string/tvData_Write = 0x7f120403
com.example.uhf:string/tvData_Data = 0x7f120401
com.example.uhf:color/white = 0x7f060346
com.example.uhf:string/tvCountOfTags = 0x7f1203ff
com.example.uhf:string/tvChannelCount = 0x7f1203fc
com.example.uhf:string/uhf_title_power = 0x7f120488
com.example.uhf:string/tvBank_Select = 0x7f1203fa
com.example.uhf:string/tvBank_Data = 0x7f1203f9
com.example.uhf:string/tvBank2_Read = 0x7f1203f8
com.example.uhf:string/tvBank = 0x7f1203f7
com.example.uhf:dimen/mtrl_btn_text_size = 0x7f070274
com.example.uhf:string/title_stop = 0x7f1203ef
com.example.uhf:string/title_power_id = 0x7f1203ea
com.example.uhf:style/Base.TextAppearance.AppCompat.SearchResult = 0x7f13002c
com.example.uhf:string/title_ping_wait = 0x7f1203e9
com.example.uhf:string/title_ping_succ = 0x7f1203e7
com.example.uhf:string/gps_msg_Locateing = 0x7f1201c4
com.example.uhf:string/title_ping_downloadresult = 0x7f1203e1
com.example.uhf:string/title_paired_devices = 0x7f1203de
com.example.uhf:string/tv_r2000_fun = 0x7f120434
com.example.uhf:string/title_not_connected = 0x7f1203dc
com.example.uhf:string/tvOption_Read = 0x7f120417
com.example.uhf:string/title_init_barcode = 0x7f1203da
com.example.uhf:string/title_hex = 0x7f1203d8
com.example.uhf:string/title_fail_rate = 0x7f1203d7
com.example.uhf:attr/flow_maxElementsWrap = 0x7f040212
com.example.uhf:dimen/m3_comp_outlined_button_disabled_outline_opacity = 0x7f070153
com.example.uhf:string/bd_title_warm = 0x7f120096
com.example.uhf:string/title_fail_count = 0x7f1203d6
com.example.uhf:string/title_error_rate = 0x7f1203d5
com.example.uhf:style/Widget.Material3.TextInputEditText.FilledBox.Dense = 0x7f130404
com.example.uhf:string/title_error_count = 0x7f1203d4
com.example.uhf:string/title_elapsed_time = 0x7f1203d3
com.example.uhf:string/fingerprint_tab_set = 0x7f1201a4
com.example.uhf:string/title_connected_to = 0x7f1203cf
com.example.uhf:string/title_clear = 0x7f1203ce
com.example.uhf:string/title_analog_call_touch_off = 0x7f1203cc
com.example.uhf:color/m3_ref_palette_dynamic_secondary99 = 0x7f060103
com.example.uhf:string/title_activity_upload = 0x7f1203c9
com.example.uhf:string/title_activity_reboot = 0x7f1203c2
com.example.uhf:color/m3_text_button_ripple_color_selector = 0x7f06021e
com.example.uhf:string/title_activity_ping_pop = 0x7f1203bf
com.example.uhf:string/title_activity_ping = 0x7f1203be
com.example.uhf:string/title_activity_nfc = 0x7f1203bd
com.example.uhf:color/m3_sys_color_dark_surface_bright = 0x7f06018e
com.example.uhf:string/title_activity_network_status = 0x7f1203bc
com.example.uhf:string/title_activity_light_and_psensor = 0x7f1203ba
com.example.uhf:string/title_activity_lf = 0x7f1203b9
com.example.uhf:array/userAreaNumbers = 0x7f03003f
com.example.uhf:color/m3_ref_palette_error90 = 0x7f06011b
com.example.uhf:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0b0001
com.example.uhf:string/title_activity_key_test = 0x7f1203b8
com.example.uhf:string/msg_google_shopper_missing = 0x7f120252
com.example.uhf:style/Preference.DropDown = 0x7f130159
com.example.uhf:color/m3_ref_palette_dynamic_neutral95 = 0x7f0600ce
com.example.uhf:string/title_activity_key_power = 0x7f1203b7
com.example.uhf:style/Base.V24.Theme.Material3.Dark.Dialog = 0x7f1300b7
com.example.uhf:string/title_activity_key_home = 0x7f1203b6
com.example.uhf:string/title_activity_key_back = 0x7f1203b5
com.example.uhf:attr/buttonIcon = 0x7f040098
com.example.uhf:string/title_activity_er_dsoft = 0x7f1203ad
com.example.uhf:dimen/m3_ripple_focused_alpha = 0x7f0701db
com.example.uhf:string/tvBase = 0x7f1203fb
com.example.uhf:string/title_activity_er_d = 0x7f1203ac
com.example.uhf:string/title_activity_bluetooth_print = 0x7f1203a9
com.example.uhf:string/title_activity_battery_monitor = 0x7f1203a7
com.example.uhf:string/title_activity_auto_run_network = 0x7f1203a6
com.example.uhf:string/title_about_storage = 0x7f1203a1
com.example.uhf:string/title_about_model = 0x7f12039e
com.example.uhf:string/title_about_jar_release = 0x7f12039c
com.example.uhf:color/m3_ref_palette_tertiary95 = 0x7f060168
com.example.uhf:string/title_Upload = 0x7f120395
com.example.uhf:string/title_UART = 0x7f120394
com.example.uhf:style/Widget.Material3.CircularProgressIndicator.Legacy.Medium = 0x7f1303a0
com.example.uhf:string/title_Ping = 0x7f120392
com.example.uhf:style/Base.Widget.AppCompat.SearchView = 0x7f1300f7
com.example.uhf:string/time_d_s = 0x7f12038d
com.example.uhf:string/tel_title_Status = 0x7f12038c
com.example.uhf:color/design_default_color_primary_variant = 0x7f06004e
com.example.uhf:string/tagFocus_off = 0x7f120388
com.example.uhf:string/strFilePath = 0x7f12037f
com.example.uhf:color/material_dynamic_secondary40 = 0x7f06026a
com.example.uhf:string/storageArea = 0x7f12037d
com.example.uhf:string/stop_location = 0x7f12037c
com.example.uhf:style/Preference.SwitchPreferenceCompat.Material = 0x7f130165
com.example.uhf:id/accessibility_custom_action_21 = 0x7f090041
com.example.uhf:string/socket_exception_error = 0x7f120379
com.example.uhf:string/sinalogin_check_server = 0x7f120378
com.example.uhf:string/sinalogin_check_pass = 0x7f120377
com.example.uhf:string/sinalogin_check_account = 0x7f120376
com.example.uhf:attr/region_widthLessThan = 0x7f0403d1
com.example.uhf:attr/itemStrokeWidth = 0x7f04027e
com.example.uhf:color/material_personalized_color_text_secondary_and_tertiary_inverse_disabled = 0x7f0602c3
com.example.uhf:string/side_sheet_accessibility_pane_title = 0x7f120374
com.example.uhf:string/setQTParams_fail = 0x7f12036d
com.example.uhf:string/send = 0x7f12036a
com.example.uhf:string/select = 0x7f120368
com.example.uhf:id/beginning = 0x7f090082
com.example.uhf:string/second = 0x7f120367
com.example.uhf:color/m3_ref_palette_neutral70 = 0x7f06012c
com.example.uhf:string/select_device = 0x7f120369
com.example.uhf:string/search_menu_title = 0x7f120363
com.example.uhf:string/msg_sbc_snippet_unavailable = 0x7f120278
com.example.uhf:string/rfid_title_continuous_write_succ_count = 0x7f12035e
com.example.uhf:string/rfid_title_continuous_read_succ_count = 0x7f12035a
com.example.uhf:string/rfid_msg_upgrade_fail = 0x7f120352
com.example.uhf:id/escape = 0x7f090123
com.example.uhf:string/rfid_msg_uid = 0x7f120351
com.example.uhf:style/Base.V24.Theme.Material3.Light.Dialog = 0x7f1300b9
com.example.uhf:string/tvChooseInfo = 0x7f1203fe
com.example.uhf:color/m3_sys_color_dynamic_dark_on_error = 0x7f06019f
com.example.uhf:string/rfid_msg_scan_fail = 0x7f12034f
com.example.uhf:string/rfid_msg_read_fail = 0x7f12034c
com.example.uhf:string/rfid_msg_lock_succ = 0x7f12034b
com.example.uhf:string/rfid_msg_lock_fail = 0x7f12034a
com.example.uhf:string/preferences_try_bsplus_summary = 0x7f120309
com.example.uhf:string/rfid_msg_confirm_flase = 0x7f120346
com.example.uhf:style/Widget.AppCompat.Toolbar.Button.Navigation = 0x7f13035a
com.example.uhf:string/rfid_msg_1byte_fail = 0x7f120343
com.example.uhf:attr/iconEndPadding = 0x7f040245
com.example.uhf:string/gps_msg_gps_not_open = 0x7f1201c5
com.example.uhf:string/rfid_mgs_lock_succ = 0x7f120340
com.example.uhf:style/Base.V26.Theme.AppCompat = 0x7f1300ba
com.example.uhf:string/rfid_mgs_lock_fail = 0x7f12033f
com.example.uhf:string/rfid_mgs_kill_succ = 0x7f12033d
com.example.uhf:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f13008b
com.example.uhf:string/rfid_mgs_error_not_write_null = 0x7f12033a
com.example.uhf:attr/foregroundInsidePadding = 0x7f040227
com.example.uhf:string/title_activity_app_set = 0x7f1203a4
com.example.uhf:string/rfid_mgs_error_nohex = 0x7f120335
com.example.uhf:string/rfid_mgs_error_init = 0x7f120333
com.example.uhf:string/rfid_mgs_error_0not_write = 0x7f120331
com.example.uhf:anim/abc_shrink_fade_out_from_bottom = 0x7f010005
com.example.uhf:string/result_wifi = 0x7f120330
com.example.uhf:string/result_product = 0x7f12032b
com.example.uhf:string/result_geo = 0x7f120329
com.example.uhf:style/Base.TextAppearance.AppCompat.Small.Inverse = 0x7f130030
com.example.uhf:string/result_email_address = 0x7f120328
com.example.uhf:drawable/mtrl_switch_thumb_pressed = 0x7f0800e5
com.example.uhf:drawable/abc_star_black_48dp = 0x7f080068
com.example.uhf:style/Base.Widget.AppCompat.Light.ActionBar.Solid = 0x7f1300e3
com.example.uhf:attr/splitLayoutDirection = 0x7f040419
com.example.uhf:macro/m3_comp_outlined_button_hover_outline_color = 0x7f0d00a5
com.example.uhf:string/result_calendar = 0x7f120327
com.example.uhf:string/result_address_book = 0x7f120326
com.example.uhf:string/reset_succ = 0x7f120325
com.example.uhf:style/ShapeAppearanceOverlay.Material3.Corner.Right = 0x7f1301af
com.example.uhf:id/fitToContents = 0x7f090152
com.example.uhf:string/rbWrite = 0x7f120322
com.example.uhf:color/m3_slider_halo_color_legacy = 0x7f06016f
com.example.uhf:string/rbLock = 0x7f120320
com.example.uhf:macro/m3_comp_navigation_drawer_active_label_text_color = 0x7f0d0080
com.example.uhf:string/rbInventorySingle = 0x7f12031f
com.example.uhf:string/psam_title_voltage = 0x7f12031a
com.example.uhf:attr/orderingFromXml = 0x7f040385
com.example.uhf:string/psam_title_send = 0x7f120317
com.example.uhf:string/psam_title_save = 0x7f120316
com.example.uhf:string/psam_title_parameter = 0x7f120315
com.example.uhf:string/btChoose_Select = 0x7f1200ab
com.example.uhf:string/psam_title_data = 0x7f120314
com.example.uhf:string/psam_title_card_1 = 0x7f120310
com.example.uhf:string/psam_msg_upgrade_fail = 0x7f12030d
com.example.uhf:string/printer_msg_low_pager = 0x7f12030b
com.example.uhf:style/ThemeOverlay.Material3.TextInputEditText.FilledBox = 0x7f1302e4
com.example.uhf:string/app_run_code_error = 0x7f12005e
com.example.uhf:dimen/m3_sys_motion_easing_linear_control_y2 = 0x7f070212
com.example.uhf:string/preferences_supplemental_title = 0x7f120307
com.example.uhf:string/preferences_scanning_title = 0x7f120304
com.example.uhf:string/preferences_front_light_title = 0x7f1202fd
com.example.uhf:integer/mtrl_card_anim_duration_ms = 0x7f0a0034
com.example.uhf:string/preferences_front_light_summary = 0x7f1202fc
com.example.uhf:string/title_07_infrared = 0x7f12038e
com.example.uhf:string/preferences_disable_exposure_title = 0x7f1202fb
com.example.uhf:string/preferences_disable_continuous_focus_title = 0x7f1202fa
com.example.uhf:string/preferences_disable_continuous_focus_summary = 0x7f1202f9
com.example.uhf:color/material_on_primary_emphasis_medium = 0x7f06028f
com.example.uhf:string/preferences_decode_1D_title = 0x7f1202f5
com.example.uhf:string/uhf_msg_write_must_len4 = 0x7f120479
com.example.uhf:string/title_activity_app_setings = 0x7f1203a5
com.example.uhf:string/preferences_custom_product_search_title = 0x7f1202f4
com.example.uhf:string/preferences_custom_product_search_summary = 0x7f1202f3
com.example.uhf:style/Animation.Material3.BottomSheetDialog = 0x7f130008
com.example.uhf:attr/animationMode = 0x7f04003c
com.example.uhf:string/preferences_bulk_mode_summary = 0x7f1202f0
com.example.uhf:attr/animateNavigationIcon = 0x7f04003a
com.example.uhf:attr/tooltipFrameBackground = 0x7f0404e9
com.example.uhf:string/preferences_actions_title = 0x7f1202ee
com.example.uhf:string/mtrl_picker_announce_current_selection = 0x7f120292
com.example.uhf:string/preference_copied = 0x7f1202ed
com.example.uhf:string/please_on = 0x7f1202ec
com.example.uhf:style/Base.TextAppearance.AppCompat.Large.Inverse = 0x7f130026
com.example.uhf:string/ping_msg_not_null = 0x7f1202e8
com.example.uhf:string/psam_title_set = 0x7f120318
com.example.uhf:string/ping_msg_finish = 0x7f1202e7
com.example.uhf:attr/collapsingToolbarLayoutLargeSize = 0x7f0400f7
com.example.uhf:style/Base.Widget.Material3.BottomNavigationView = 0x7f130104
com.example.uhf:string/ping_msg_background_ping = 0x7f1202e5
com.example.uhf:string/path_password_strike_through = 0x7f1202e4
com.example.uhf:macro/m3_comp_search_bar_pressed_state_layer_color = 0x7f0d00ec
com.example.uhf:string/path_password_eye_mask_visible = 0x7f1202e3
com.example.uhf:attr/dragDirection = 0x7f040196
com.example.uhf:string/password_toggle_content_description = 0x7f1202e0
com.example.uhf:attr/textBackgroundPanX = 0x7f04049d
com.example.uhf:string/not_connected = 0x7f1202dc
com.example.uhf:string/normal_set = 0x7f1202db
com.example.uhf:string/fab_transformation_scrim_behavior = 0x7f12016b
com.example.uhf:string/nfc_msg_write_tip = 0x7f1202d6
com.example.uhf:string/nfc_msg_write_succ = 0x7f1202d5
com.example.uhf:dimen/mtrl_high_ripple_hovered_alpha = 0x7f0702c1
com.example.uhf:string/nfc_msg_read_only = 0x7f1202d2
com.example.uhf:string/nfc_msg_no_open = 0x7f1202d0
com.example.uhf:string/network_msg_title_mobile = 0x7f1202c5
com.example.uhf:string/network_msg_sim_not_exist = 0x7f1202c4
com.example.uhf:bool/enable_system_alarm_service_default = 0x7f050003
com.example.uhf:string/network_msg_out_of_service = 0x7f1202c2
com.example.uhf:color/m3_ref_palette_dynamic_primary30 = 0x7f0600ee
com.example.uhf:style/Base.V14.Theme.MaterialComponents.Light = 0x7f13009a
com.example.uhf:string/network_msg_operator_is_null = 0x7f1202c1
com.example.uhf:id/EtAccessPwd_Lock = 0x7f09000a
com.example.uhf:dimen/mtrl_bottomappbar_fab_bottom_margin = 0x7f070259
com.example.uhf:string/network_msg_in_service = 0x7f1202c0
com.example.uhf:style/Base.Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f13011c
com.example.uhf:dimen/design_snackbar_padding_vertical = 0x7f070088
com.example.uhf:string/mtrl_timepicker_confirm = 0x7f1202bd
com.example.uhf:string/mtrl_timepicker_cancel = 0x7f1202bc
com.example.uhf:color/m3_timepicker_clock_text_color = 0x7f060227
com.example.uhf:string/mtrl_switch_thumb_path_pressed = 0x7f1202b8
com.example.uhf:macro/m3_comp_date_picker_modal_range_selection_header_headline_type = 0x7f0d001a
com.example.uhf:string/mtrl_picker_toggle_to_day_selection = 0x7f1202b1
com.example.uhf:string/mtrl_picker_today_description = 0x7f1202af
com.example.uhf:string/mtrl_picker_text_input_month_abbr = 0x7f1202ad
com.example.uhf:attr/subtitleTextAppearance = 0x7f040443
com.example.uhf:string/mtrl_picker_text_input_day_abbr = 0x7f1202ac
com.example.uhf:string/mtrl_picker_text_input_date_range_start_hint = 0x7f1202ab
com.example.uhf:string/up_msg_total_time = 0x7f12049e
com.example.uhf:string/mtrl_picker_text_input_date_hint = 0x7f1202a9
com.example.uhf:string/mtrl_picker_save = 0x7f1202a7
com.example.uhf:string/mtrl_picker_range_header_unselected = 0x7f1202a6
com.example.uhf:style/MaterialAlertDialog.Material3.Title.Panel.CenterStacked = 0x7f130130
com.example.uhf:string/mtrl_picker_range_header_title = 0x7f1202a5
com.example.uhf:style/Widget.MaterialComponents.Button.OutlinedButton = 0x7f13042a
com.example.uhf:string/mtrl_picker_range_header_selected = 0x7f1202a4
com.example.uhf:color/m3_ref_palette_neutral_variant0 = 0x7f060136
com.example.uhf:id/accessibility_custom_action_29 = 0x7f090049
com.example.uhf:string/mtrl_picker_out_of_range = 0x7f1202a1
com.example.uhf:string/mtrl_picker_invalid_format = 0x7f12029b
com.example.uhf:string/mtrl_picker_confirm = 0x7f120295
com.example.uhf:styleable/ThemeEnforcement = 0x7f1400a2
com.example.uhf:string/mtrl_picker_announce_current_selection_none = 0x7f120293
com.example.uhf:string/mtrl_picker_announce_current_range_selection = 0x7f120291
com.example.uhf:style/Base.V14.Theme.Material3.Dark.BottomSheetDialog = 0x7f13008f
com.example.uhf:string/mtrl_picker_a11y_next_month = 0x7f12028f
com.example.uhf:attr/indicatorInset = 0x7f04025c
com.example.uhf:string/mtrl_exceed_max_badge_number_suffix = 0x7f12028e
com.example.uhf:style/Theme.MaterialComponents.Light.NoActionBar.Bridge = 0x7f13029c
com.example.uhf:drawable/$mtrl_switch_thumb_unchecked_checked__0 = 0x7f080026
com.example.uhf:string/mtrl_checkbox_button_path_name = 0x7f120287
com.example.uhf:string/mtrl_checkbox_button_path_group_name = 0x7f120286
com.example.uhf:id/etLength = 0x7f090135
com.example.uhf:dimen/compat_notification_large_icon_max_height = 0x7f07005d
com.example.uhf:string/mtrl_checkbox_button_path_checked = 0x7f120285
com.example.uhf:string/mtrl_checkbox_button_icon_path_indeterminate = 0x7f120283
com.example.uhf:style/Base.Theme.Material3.Dark.BottomSheetDialog = 0x7f13005e
com.example.uhf:string/lp_title_brightness = 0x7f1201fe
com.example.uhf:string/mtrl_checkbox_button_icon_path_checked = 0x7f120281
com.example.uhf:attr/actionBarTheme = 0x7f04000b
com.example.uhf:style/Preference.Information = 0x7f13015b
com.example.uhf:id/buttonPanel = 0x7f0900ac
com.example.uhf:string/mtrl_badge_numberless_content_description = 0x7f120280
com.example.uhf:dimen/m3_comp_snackbar_container_elevation = 0x7f07018e
com.example.uhf:string/msg_unmount_usb = 0x7f12027f
com.example.uhf:string/msg_share_text = 0x7f12027c
com.example.uhf:string/msg_share_subject_line = 0x7f12027b
com.example.uhf:string/msg_sbc_unknown_page = 0x7f120279
com.example.uhf:style/Base.Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f1300e9
com.example.uhf:string/msg_sbc_searching_book = 0x7f120277
com.example.uhf:style/Widget.MaterialComponents.NavigationView = 0x7f130467
com.example.uhf:string/msg_sbc_page = 0x7f120275
com.example.uhf:string/msg_sbc_failed = 0x7f120273
com.example.uhf:color/material_slider_active_track_color = 0x7f0602c9
com.example.uhf:string/msg_redirect = 0x7f120270
com.example.uhf:attr/passwordToggleTintMode = 0x7f040398
com.example.uhf:string/msg_receive = 0x7f12026f
com.example.uhf:string/msg_reboot_complete = 0x7f12026c
com.example.uhf:id/horizontal_only = 0x7f09016a
com.example.uhf:string/msg_read_detail_fail = 0x7f12026b
com.example.uhf:string/msg_noaccess_delete = 0x7f120269
com.example.uhf:attr/counterEnabled = 0x7f040161
com.example.uhf:string/msg_network_none = 0x7f120265
com.example.uhf:string/msg_mac_error = 0x7f120263
com.example.uhf:string/fingerprint_msg_page_id_need_0_to_254 = 0x7f120193
com.example.uhf:attr/trackThickness = 0x7f0404fb
com.example.uhf:string/msg_mac_change = 0x7f120262
com.example.uhf:color/secondary_text_disabled_material_dark = 0x7f06032f
com.example.uhf:string/title_about_device_kernel_version = 0x7f120398
com.example.uhf:string/msg_login_success = 0x7f120260
com.example.uhf:string/msg_login_pwd_null = 0x7f12025f
com.example.uhf:string/msg_login_email_error = 0x7f12025b
com.example.uhf:string/msg_loding = 0x7f12025a
com.example.uhf:string/msg_load_userface_fail = 0x7f120259
com.example.uhf:attr/itemIconPadding = 0x7f04026c
com.example.uhf:string/msg_load_is_null = 0x7f120258
com.example.uhf:string/msg_mac_unmatched = 0x7f120264
com.example.uhf:id/accessibility_custom_action_23 = 0x7f090043
com.example.uhf:string/msg_intent_failed = 0x7f120256
com.example.uhf:id/textinput_prefix_text = 0x7f0902a9
com.example.uhf:style/Base.V14.ThemeOverlay.Material3.SideSheetDialog = 0x7f1300a0
com.example.uhf:attr/paddingTopSystemWindowInsets = 0x7f040390
com.example.uhf:string/msg_install_google_shopper = 0x7f120255
com.example.uhf:string/new_data_toast_none = 0x7f1202ce
com.example.uhf:attr/maxCharacterCount = 0x7f040329
com.example.uhf:string/app_msg_init = 0x7f12005b
com.example.uhf:string/msg_network_ok = 0x7f120266
com.example.uhf:dimen/abc_dropdownitem_text_padding_left = 0x7f07002a
com.example.uhf:color/material_dynamic_neutral_variant95 = 0x7f060256
com.example.uhf:string/msg_free_succ = 0x7f12024f
com.example.uhf:id/tag_on_apply_window_listener = 0x7f090292
com.example.uhf:string/msg_default_type = 0x7f120248
com.example.uhf:style/Widget.MaterialComponents.Badge = 0x7f13041f
com.example.uhf:string/title_stop_Inventory = 0x7f1203f0
com.example.uhf:drawable/abc_scrubber_control_to_pressed_mtrl_000 = 0x7f08005f
com.example.uhf:string/msg_default_time = 0x7f120247
com.example.uhf:id/invisible = 0x7f090176
com.example.uhf:string/msg_default_status = 0x7f120246
com.example.uhf:dimen/mtrl_calendar_days_of_week_height = 0x7f070281
com.example.uhf:string/msg_default_format = 0x7f120243
com.example.uhf:string/msg_data_hex = 0x7f120242
com.example.uhf:string/msg_bulk_mode_scanned = 0x7f12023f
com.example.uhf:string/menu_settings = 0x7f12023a
com.example.uhf:string/menu_encode_mecard = 0x7f120236
com.example.uhf:color/material_dynamic_neutral_variant70 = 0x7f060253
com.example.uhf:string/material_timepicker_text_input_mode_description = 0x7f120235
com.example.uhf:string/bd_msg_Locate_succ = 0x7f120088
com.example.uhf:string/material_timepicker_pm = 0x7f120233
com.example.uhf:string/material_timepicker_clock_mode_description = 0x7f120230
com.example.uhf:string/material_slider_value = 0x7f12022e
com.example.uhf:string/material_motion_easing_standard = 0x7f12022b
com.example.uhf:string/material_motion_easing_linear = 0x7f12022a
com.example.uhf:id/rbFastInventoryOpen = 0x7f090219
com.example.uhf:string/material_motion_easing_emphasized = 0x7f120229
com.example.uhf:string/material_motion_easing_decelerated = 0x7f120228
com.example.uhf:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense = 0x7f13047a
com.example.uhf:id/SpinnerReadLock = 0x7f090025
com.example.uhf:id/expanded_menu = 0x7f090148
com.example.uhf:string/material_minute_suffix = 0x7f120226
com.example.uhf:string/mtrl_checkbox_button_path_unchecked = 0x7f120288
com.example.uhf:string/material_minute_selection = 0x7f120225
com.example.uhf:color/design_fab_stroke_end_inner_color = 0x7f060056
com.example.uhf:string/material_hour_suffix = 0x7f120224
com.example.uhf:string/material_hour_selection = 0x7f120223
com.example.uhf:id/rbUser_light_filter = 0x7f09022a
com.example.uhf:string/material_clock_display_divider = 0x7f120220
com.example.uhf:attr/dragThreshold = 0x7f040198
com.example.uhf:id/beginOnFirstDraw = 0x7f090081
com.example.uhf:string/ap_title_security = 0x7f120053
com.example.uhf:style/Base.TextAppearance.AppCompat.Caption = 0x7f13001e
com.example.uhf:string/m3_sys_motion_easing_standard_decelerate = 0x7f12021f
com.example.uhf:integer/material_motion_duration_long_1 = 0x7f0a0026
com.example.uhf:string/m3_sys_motion_easing_standard_accelerate = 0x7f12021e
com.example.uhf:style/Base.TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f130040
com.example.uhf:string/m3_sys_motion_easing_legacy_decelerate = 0x7f12021b
com.example.uhf:string/m3_sys_motion_easing_emphasized_decelerate = 0x7f120217
com.example.uhf:string/m3_sys_motion_easing_emphasized_accelerate = 0x7f120216
com.example.uhf:string/m3_sys_motion_easing_emphasized = 0x7f120215
com.example.uhf:string/m3_ref_typeface_brand_medium = 0x7f120211
com.example.uhf:string/m1_title_single = 0x7f12020d
com.example.uhf:string/m1_title_key_type = 0x7f120208
com.example.uhf:string/lp_title_r = 0x7f120205
com.example.uhf:string/lp_title_lightsensor = 0x7f120203
com.example.uhf:string/lp_title_light = 0x7f120202
com.example.uhf:string/lp_title_g = 0x7f120200
com.example.uhf:string/lp_title_compass = 0x7f1201ff
com.example.uhf:string/lp_btn_open = 0x7f1201fb
com.example.uhf:attr/editTextStyle = 0x7f0401af
com.example.uhf:macro/m3_comp_outlined_text_field_disabled_input_text_color = 0x7f0d00b2
com.example.uhf:string/load_more = 0x7f1201f6
com.example.uhf:string/load_error = 0x7f1201f3
com.example.uhf:string/load_empty = 0x7f1201f2
com.example.uhf:string/lf_title_pageid = 0x7f1201ef
com.example.uhf:string/lf_title_card_type = 0x7f1201ed
com.example.uhf:attr/extendedFloatingActionButtonSurfaceStyle = 0x7f0401e3
com.example.uhf:dimen/m3_sys_elevation_level0 = 0x7f0701f5
com.example.uhf:macro/m3_comp_navigation_drawer_active_pressed_icon_color = 0x7f0d0081
com.example.uhf:style/ShapeAppearance.M3.Comp.Switch.StateLayer.Shape = 0x7f13018f
com.example.uhf:string/lf_msg_write_succ = 0x7f1201ec
com.example.uhf:string/desfire_title_properties = 0x7f120133
com.example.uhf:style/PreferenceCategoryTitleTextStyle = 0x7f130166
com.example.uhf:string/lf_msg_write_fail = 0x7f1201eb
com.example.uhf:string/lf_msg_data_page_id_err_em4305 = 0x7f1201e9
com.example.uhf:string/lf_msg_data_not_hex4 = 0x7f1201e7
com.example.uhf:string/led_action_close = 0x7f1201e3
com.example.uhf:style/Base.V21.ThemeOverlay.Material3.SideSheetDialog = 0x7f1300b0
com.example.uhf:anim/m3_motion_fade_exit = 0x7f010025
com.example.uhf:string/keyboard_title_multiple = 0x7f1201e1
com.example.uhf:string/io_exception_error = 0x7f1201de
com.example.uhf:styleable/MaterialCheckBox = 0x7f14005b
com.example.uhf:dimen/m3_bottomappbar_fab_cradle_vertical_offset = 0x7f0700cc
com.example.uhf:id/etLen_filter_deact = 0x7f090130
com.example.uhf:string/inputData = 0x7f1201dd
com.example.uhf:style/Base.V26.Widget.AppCompat.Toolbar = 0x7f1300bc
com.example.uhf:id/included = 0x7f090173
com.example.uhf:string/icon_content_description = 0x7f1201dc
com.example.uhf:string/http_exception_error = 0x7f1201da
com.example.uhf:attr/liftOnScroll = 0x7f0402d9
com.example.uhf:string/history_title = 0x7f1201d9
com.example.uhf:string/history_empty_detail = 0x7f1201d7
com.example.uhf:style/Widget.Material3.FloatingActionButton.Large.Tertiary = 0x7f1303b7
com.example.uhf:string/hide_bottom_view_on_scroll_behavior = 0x7f1201d2
com.example.uhf:style/Base.Widget.AppCompat.TextView.SpinnerItem = 0x7f1300fe
com.example.uhf:string/gs_msg_not_one = 0x7f1201cf
com.example.uhf:attr/iconTintMode = 0x7f04024c
com.example.uhf:string/gps_title_Longitude = 0x7f1201cb
com.example.uhf:string/gps_title_Latitude = 0x7f1201ca
com.example.uhf:string/mtrl_picker_start_date_description = 0x7f1202a8
com.example.uhf:string/gps_title_Altitude = 0x7f1201c8
com.example.uhf:string/gps_msg_title_satellite_signal = 0x7f1201c7
com.example.uhf:string/gps_msg_open_gps = 0x7f1201c6
com.example.uhf:string/desfire_title_file_id = 0x7f12012a
com.example.uhf:string/gps_msg_Locate_stop = 0x7f1201c2
com.example.uhf:string/get_succ = 0x7f1201bf
com.example.uhf:string/get_fail = 0x7f1201be
com.example.uhf:attr/actionLayout = 0x7f04000f
com.example.uhf:string/fontGrayscaleName = 0x7f1201b9
com.example.uhf:string/fingerprint_title_threshold = 0x7f1201b6
com.example.uhf:styleable/Insets = 0x7f140044
com.example.uhf:color/m3_sys_color_dynamic_light_on_surface = 0x7f0601c7
com.example.uhf:string/fingerprint_title_show_imgiso = 0x7f1201b5
com.example.uhf:string/fingerprint_title_pageid = 0x7f1201b2
com.example.uhf:style/Base.V23.Theme.AppCompat.Light = 0x7f1300b5
com.example.uhf:string/fingerprint_title_page_mode_1 = 0x7f1201b0
com.example.uhf:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__0 = 0x7f080015
com.example.uhf:string/fingerprint_title_page_mode = 0x7f1201af
com.example.uhf:string/fingerprint_title_index = 0x7f1201a9
com.example.uhf:string/fingerprint_title_get_img_fail = 0x7f1201a8
com.example.uhf:style/Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f130298
com.example.uhf:string/fingerprint_title_auto_acquisition = 0x7f1201a6
com.example.uhf:dimen/abc_action_bar_icon_vertical_padding_material = 0x7f070006
com.example.uhf:dimen/m3_btn_disabled_elevation = 0x7f0700d2
com.example.uhf:string/msg_disable_fail = 0x7f120249
com.example.uhf:string/material_slider_range_end = 0x7f12022c
com.example.uhf:attr/checkedButton = 0x7f0400bc
com.example.uhf:style/Base.Widget.Material3.CollapsingToolbar = 0x7f130107
com.example.uhf:string/fingerprint_msg_verify_PSW_fail = 0x7f12019f
com.example.uhf:string/fingerprint_msg_set_threshold_succ = 0x7f12019d
com.example.uhf:string/fingerprint_msg_set_threshold_fail = 0x7f12019c
com.example.uhf:color/m3_sys_color_dynamic_dark_surface_dim = 0x7f0601b6
com.example.uhf:id/transitionToEnd = 0x7f0902b3
com.example.uhf:string/fingerprint_msg_set_PacketSize_fail = 0x7f12019a
com.example.uhf:macro/m3_comp_search_bar_pressed_supporting_text_color = 0x7f0d00ed
com.example.uhf:string/fingerprint_msg_set_PSW_succ = 0x7f120199
com.example.uhf:string/fingerprint_msg_name_not_null = 0x7f120192
com.example.uhf:string/fingerprint_msg_import_fail = 0x7f12018f
com.example.uhf:string/fingerprint_msg_ident_succ = 0x7f12018e
com.example.uhf:style/Theme.MaterialComponents.Light.Dialog.Alert = 0x7f130293
com.example.uhf:string/msg_camera_framework_bug = 0x7f120240
com.example.uhf:drawable/design_fab_background = 0x7f080093
com.example.uhf:macro/m3_comp_date_picker_modal_weekdays_label_text_type = 0x7f0d001e
com.example.uhf:string/fingerprint_msg_ident_fail = 0x7f12018d
com.example.uhf:string/fingerprint_msg_clear_fail = 0x7f12018b
com.example.uhf:string/fingerprint_msg_acq_fail = 0x7f120189
com.example.uhf:string/fingerprint_menu_delall = 0x7f120188
com.example.uhf:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f1302cc
com.example.uhf:string/fingerprint_menu_del = 0x7f120187
com.example.uhf:string/fingerprint_dailog_ok = 0x7f120185
com.example.uhf:string/fingerprint_dailog_msg = 0x7f120183
com.example.uhf:string/fingerprint_btn_reinit = 0x7f12017c
com.example.uhf:attr/maxActionInlineWidth = 0x7f040327
com.example.uhf:string/tvTID_Lock = 0x7f120425
com.example.uhf:id/closest = 0x7f0900e3
com.example.uhf:string/fingerprint_btn_get_PacketSize = 0x7f120178
com.example.uhf:dimen/mtrl_progress_circular_radius = 0x7f0702dd
com.example.uhf:string/title_activity_to_service_page = 0x7f1203c5
com.example.uhf:attr/materialAlertDialogTitlePanelStyle = 0x7f0402fb
com.example.uhf:string/fingerprint_btn_clear = 0x7f120176
com.example.uhf:string/menu_history = 0x7f120239
com.example.uhf:string/file_title_sel_file_confirm = 0x7f120174
com.example.uhf:macro/m3_comp_date_picker_modal_range_selection_month_subhead_color = 0x7f0d001b
com.example.uhf:string/fastID_off = 0x7f12016f
com.example.uhf:string/exit = 0x7f120167
com.example.uhf:string/error_a11y_label = 0x7f120165
com.example.uhf:color/gray = 0x7f060067
com.example.uhf:string/erd_title = 0x7f120164
com.example.uhf:style/Widget.Material3.SearchBar.Outlined = 0x7f1303f1
com.example.uhf:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2 = 0x7f13017a
com.example.uhf:styleable/SplitPairRule = 0x7f140092
com.example.uhf:string/er_dsoft_tab_set = 0x7f120163
com.example.uhf:string/er_dsoft_get_img_data_fail = 0x7f12015f
com.example.uhf:color/switch_thumb_material_dark = 0x7f06033b
com.example.uhf:string/er_dsoft_get_Set = 0x7f12015e
com.example.uhf:string/er_dsoft_get_ParamVal = 0x7f12015d
com.example.uhf:style/Widget.AppCompat.ButtonBar.AlertDialog = 0x7f130324
com.example.uhf:attr/layout_constraintBottom_toBottomOf = 0x7f0402a0
com.example.uhf:color/m3_dynamic_dark_highlighted_text = 0x7f06009a
com.example.uhf:string/er_dsoft_get_Get = 0x7f12015b
com.example.uhf:string/er_dsoft_Set_succ = 0x7f12015a
com.example.uhf:string/download_title = 0x7f120158
com.example.uhf:string/download_msg_uploading = 0x7f120157
com.example.uhf:string/download_msg_sdcard_not_exist = 0x7f120153
com.example.uhf:attr/itemMinHeight = 0x7f040270
com.example.uhf:string/download_msg_file_path_not_exist = 0x7f120152
com.example.uhf:string/download_msg_downing = 0x7f120151
com.example.uhf:string/download_msg_down_report = 0x7f12014f
com.example.uhf:dimen/design_bottom_navigation_item_max_width = 0x7f070067
com.example.uhf:string/download_msg_down_file = 0x7f12014d
com.example.uhf:color/m3_sys_color_secondary_fixed = 0x7f060212
com.example.uhf:dimen/abc_alert_dialog_button_dimen = 0x7f070011
com.example.uhf:string/download_msg_down_confirm = 0x7f12014b
com.example.uhf:macro/m3_comp_navigation_rail_inactive_focus_state_layer_color = 0x7f0d009a
com.example.uhf:string/download_msg_cancel_upload = 0x7f120148
com.example.uhf:string/download_msg_cancel = 0x7f120146
com.example.uhf:string/desfire_title_value_min = 0x7f120144
com.example.uhf:style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f1300e6
com.example.uhf:string/desfire_title_value_max = 0x7f120143
com.example.uhf:string/desfire_title_value_file = 0x7f120142
com.example.uhf:color/abc_tint_default = 0x7f060014
com.example.uhf:string/desfire_title_update_permission = 0x7f12013d
com.example.uhf:string/mtrl_switch_thumb_path_name = 0x7f1202b7
com.example.uhf:dimen/tooltip_corner_radius = 0x7f07034d
com.example.uhf:string/desfire_title_sta_file = 0x7f12013c
com.example.uhf:string/desfire_title_readwrite_permission = 0x7f120139
com.example.uhf:dimen/m3_large_text_vertical_offset_adjustment = 0x7f0701c0
com.example.uhf:string/desfire_title_pwd_chg = 0x7f120135
com.example.uhf:macro/m3_comp_switch_selected_pressed_track_color = 0x7f0d012d
com.example.uhf:string/desfire_title_get_version = 0x7f12012f
com.example.uhf:string/desfire_title_get_card_version = 0x7f12012e
com.example.uhf:string/mtrl_checkbox_state_description_checked = 0x7f120289
com.example.uhf:string/up_msg_sel_file = 0x7f12049b
com.example.uhf:string/desfire_title_file = 0x7f120129
com.example.uhf:string/desfire_title_comm_setting = 0x7f120125
com.example.uhf:string/desfire_title_change_pwd = 0x7f120124
com.example.uhf:string/desfire_title_card_click_tip = 0x7f120122
com.example.uhf:string/led_action_open = 0x7f1201e4
com.example.uhf:string/desfire_title_appid_input = 0x7f120120
com.example.uhf:style/ShapeAppearance.M3.Comp.NavigationDrawer.ActiveIndicator.Shape = 0x7f130187
com.example.uhf:string/desfire_title_app_oper_pwd = 0x7f12011e
com.example.uhf:string/desfire_title_app_add_del_pwd = 0x7f120119
com.example.uhf:styleable/CompoundButton = 0x7f140029
com.example.uhf:string/desfire_title_add_del_pwd = 0x7f120117
com.example.uhf:color/m3_sys_color_dark_error = 0x7f060177
com.example.uhf:string/desfire_title_add_app = 0x7f120116
com.example.uhf:string/desfire_msg_pwd_vail_fail = 0x7f120115
com.example.uhf:style/Base.Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f130079
com.example.uhf:string/desfire_title_back = 0x7f120121
com.example.uhf:string/desfire_msg_operator_succ = 0x7f120113
com.example.uhf:animator/m3_extended_fab_change_size_collapse_motion_spec = 0x7f020010
com.example.uhf:string/desfire_msg_operator_fail = 0x7f120112
com.example.uhf:string/rfid_mgs_error_not_found = 0x7f120338
com.example.uhf:string/desfire_msg_id_must_len_3 = 0x7f120111
com.example.uhf:string/fab_transformation_sheet_behavior = 0x7f12016c
com.example.uhf:attr/placeholderActivityName = 0x7f0403a2
com.example.uhf:string/desfire_msg_get_info_fail = 0x7f120110
com.example.uhf:string/psam_title_baud = 0x7f12030f
com.example.uhf:string/desfire_msg_file_num_must_len_1 = 0x7f12010d
com.example.uhf:string/desfire_msg_comm_setting_must_len_1 = 0x7f12010b
com.example.uhf:string/mtrl_picker_date_header_selected = 0x7f120296
com.example.uhf:string/delete_image = 0x7f120105
com.example.uhf:string/delete_blog = 0x7f120104
com.example.uhf:string/title_about_ram = 0x7f12039f
com.example.uhf:string/clearwords = 0x7f1200f8
com.example.uhf:string/current_dir = 0x7f120102
com.example.uhf:dimen/text_size_18 = 0x7f07033b
com.example.uhf:string/contents_text = 0x7f120100
com.example.uhf:string/getQTParams_fail = 0x7f1201bc
com.example.uhf:attr/tabIconTint = 0x7f040458
com.example.uhf:string/contents_sms = 0x7f1200ff
com.example.uhf:string/contents_contact = 0x7f1200fb
com.example.uhf:string/clear_text_end_icon_content_description = 0x7f1200f7
com.example.uhf:string/ckWithUii = 0x7f1200f5
com.example.uhf:style/TextAppearance.AppCompat.Title.Inverse = 0x7f1301d9
com.example.uhf:string/ckSecond = 0x7f1200f4
com.example.uhf:string/rfid_msg_read_succ = 0x7f12034d
com.example.uhf:id/decode_succeeded = 0x7f0900fa
com.example.uhf:attr/scrimBackground = 0x7f0403dc
com.example.uhf:dimen/material_clock_display_height = 0x7f070227
com.example.uhf:string/ckLoop_Read = 0x7f1200f3
com.example.uhf:style/Base.Widget.AppCompat.ActivityChooserView = 0x7f1300d0
com.example.uhf:id/cbBlock9 = 0x7f0900c2
com.example.uhf:string/choose_image = 0x7f1200f1
com.example.uhf:macro/m3_comp_badge_large_label_text_color = 0x7f0d0003
com.example.uhf:string/character_counter_pattern = 0x7f1200f0
com.example.uhf:dimen/m3_fab_border_width = 0x7f0701ba
com.example.uhf:string/tel_title_NetworkType = 0x7f120389
com.example.uhf:string/character_counter_overflowed_content_description = 0x7f1200ef
com.example.uhf:styleable/ViewTransition = 0x7f1400ac
com.example.uhf:attr/boxStrokeColor = 0x7f04008c
com.example.uhf:string/call_notification_screening_text = 0x7f1200e9
com.example.uhf:string/uhf_btn_workwait = 0x7f12043d
com.example.uhf:string/desfire_title_scan = 0x7f12013a
com.example.uhf:drawable/$avd_show_password__2 = 0x7f080005
com.example.uhf:string/call_notification_incoming_text = 0x7f1200e7
com.example.uhf:attr/bottomSheetStyle = 0x7f040084
com.example.uhf:attr/layout_anchorGravity = 0x7f040295
com.example.uhf:string/call_notification_hang_up_action = 0x7f1200e6
com.example.uhf:style/Base.TextAppearance.AppCompat.Body2 = 0x7f13001c
com.example.uhf:string/call_notification_answer_action = 0x7f1200e3
com.example.uhf:attr/maxButtonHeight = 0x7f040328
com.example.uhf:string/button_show_map = 0x7f1200df
com.example.uhf:id/btnSetSession = 0x7f0900a6
com.example.uhf:string/ok = 0x7f1202df
com.example.uhf:string/button_share_clipboard = 0x7f1200dd
com.example.uhf:attr/textInputOutlinedExposedDropdownMenuStyle = 0x7f0404aa
com.example.uhf:style/Preference = 0x7f130150
com.example.uhf:string/button_share_by_sms = 0x7f1200dc
com.example.uhf:string/button_share_app = 0x7f1200d9
com.example.uhf:style/Theme.Design = 0x7f13024c
com.example.uhf:attr/snackbarTextViewStyle = 0x7f040414
com.example.uhf:string/button_scan = 0x7f1200d7
com.example.uhf:string/tvFreHop = 0x7f120406
com.example.uhf:string/sharing = 0x7f120373
com.example.uhf:string/button_product_search = 0x7f1200d6
com.example.uhf:string/button_mms = 0x7f1200d3
com.example.uhf:string/button_custom_product_search = 0x7f1200cb
com.example.uhf:string/button_cancel = 0x7f1200ca
com.example.uhf:dimen/m3_chip_hovered_translation_z = 0x7f0700fd
com.example.uhf:macro/m3_comp_navigation_bar_inactive_icon_color = 0x7f0d0072
com.example.uhf:layout/fragment_block_permalock = 0x7f0c0030
com.example.uhf:string/item_view_role_description = 0x7f1201df
com.example.uhf:string/button_back = 0x7f1200c8
com.example.uhf:macro/m3_comp_navigation_drawer_active_focus_icon_color = 0x7f0d0078
com.example.uhf:string/button_add_calendar = 0x7f1200c6
com.example.uhf:string/bt_not_enabled_leaving = 0x7f1200c2
com.example.uhf:style/ShapeAppearance.M3.Comp.Switch.Track.Shape = 0x7f130190
com.example.uhf:drawable/indeterminate_static = 0x7f0800b1
com.example.uhf:string/btStop = 0x7f1200bf
com.example.uhf:string/abc_search_hint = 0x7f120033
com.example.uhf:string/ping_msg_runing = 0x7f1202ea
com.example.uhf:string/btSetFreHop = 0x7f1200bc
com.example.uhf:id/listMode = 0x7f09018a
com.example.uhf:style/Base.TextAppearance.AppCompat.SearchResult.Title = 0x7f13002e
com.example.uhf:string/btReduceI = 0x7f1200ba
com.example.uhf:array/i760icon = 0x7f03003c
com.example.uhf:macro/m3_comp_navigation_drawer_inactive_pressed_state_layer_color = 0x7f0d0090
com.example.uhf:string/btReduceD = 0x7f1200b9
com.example.uhf:layout/abc_dialog_title_material = 0x7f0c000c
com.example.uhf:string/btRead_Select = 0x7f1200b8
com.example.uhf:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query = 0x7f13017b
com.example.uhf:string/btLock = 0x7f1200b5
com.example.uhf:attr/mock_showLabel = 0x7f040340
com.example.uhf:string/result_uri = 0x7f12032f
com.example.uhf:string/btKill = 0x7f1200b4
com.example.uhf:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense = 0x7f13047e
com.example.uhf:id/search_mag_icon = 0x7f090250
com.example.uhf:string/btGetFre = 0x7f1200b1
com.example.uhf:macro/m3_comp_filled_tonal_button_label_text_color = 0x7f0d0053
com.example.uhf:string/btEnter = 0x7f1200af
com.example.uhf:string/btCreateCode = 0x7f1200ad
com.example.uhf:style/Widget.Material3.CollapsingToolbar.Large = 0x7f1303a5
com.example.uhf:string/btAddI = 0x7f1200a6
com.example.uhf:style/Base.V7.Theme.AppCompat.Light = 0x7f1300c1
com.example.uhf:string/bottomsheet_drag_handle_content_description = 0x7f1200a4
com.example.uhf:styleable/ListPreference = 0x7f140052
com.example.uhf:id/never = 0x7f0901da
com.example.uhf:string/bottomsheet_action_expand = 0x7f1200a1
com.example.uhf:string/bottom_sheet_behavior = 0x7f12009f
com.example.uhf:string/bookmark_picker_name = 0x7f12009e
com.example.uhf:string/rfid_title_continuous_write = 0x7f12035c
com.example.uhf:string/bluetooth_title_tip = 0x7f12009d
com.example.uhf:string/bluetooth_msg_not_supp = 0x7f12009b
com.example.uhf:string/bd_title_hot = 0x7f120095
com.example.uhf:color/design_default_color_error = 0x7f060046
com.example.uhf:string/bd_title_gps = 0x7f120094
com.example.uhf:string/bd_title_gb = 0x7f120093
com.example.uhf:string/bd_title_cold = 0x7f120092
com.example.uhf:string/bd_title_bdStatus = 0x7f120091
com.example.uhf:string/bd_title_Longitude = 0x7f12008d
com.example.uhf:dimen/mtrl_extended_fab_translation_z_base = 0x7f0702b8
com.example.uhf:string/bd_msg_title_satellite_signal = 0x7f12008a
com.example.uhf:string/bd_msg_Locateing = 0x7f120089
com.example.uhf:string/bd_msg_Locate_stop = 0x7f120087
com.example.uhf:string/battery_tips_voltage = 0x7f120083
com.example.uhf:color/viewfinder_frame = 0x7f060343
com.example.uhf:string/battery_tips_temperature = 0x7f120082
com.example.uhf:string/battery_tips_level = 0x7f120081
com.example.uhf:string/battery_tips_STATUS_UNKNOWN = 0x7f12007e
com.example.uhf:style/Theme.Material3.DynamicColors.Dark.NoActionBar = 0x7f130263
com.example.uhf:string/battery_tips_STATUS_NOT_CHARGING = 0x7f12007d
com.example.uhf:style/Base.Widget.AppCompat.DrawerArrowToggle.Common = 0x7f1300de
com.example.uhf:string/battery_tips_HEALTH_GOOD = 0x7f120075
com.example.uhf:id/epcLayout = 0x7f090122
com.example.uhf:string/b14443_title_stop = 0x7f120071
com.example.uhf:dimen/abc_seekbar_track_background_height_material = 0x7f070038
com.example.uhf:string/b14443_title_send = 0x7f120070
com.example.uhf:attr/chipMinHeight = 0x7f0400d0
com.example.uhf:string/b14443_title_cmd = 0x7f12006c
com.example.uhf:string/b14443_msg_uid = 0x7f120069
com.example.uhf:string/b14443_msg_rats = 0x7f120065
com.example.uhf:color/m3_ref_palette_dynamic_neutral_variant0 = 0x7f0600d2
com.example.uhf:string/b14443_msg_num = 0x7f120064
com.example.uhf:color/m3_button_ripple_color = 0x7f060083
com.example.uhf:string/b14443_msg_cmd_data = 0x7f120061
com.example.uhf:id/restart_preview = 0x7f090235
com.example.uhf:string/strUpload = 0x7f120382
com.example.uhf:id/design_navigation_view = 0x7f090103
com.example.uhf:string/appbar_scrolling_view_behavior = 0x7f120060
com.example.uhf:attr/layout_constraintBaseline_creator = 0x7f04029b
com.example.uhf:string/app_title = 0x7f12005f
com.example.uhf:color/material_on_background_emphasis_medium = 0x7f06028c
com.example.uhf:string/app_picker_name = 0x7f12005d
com.example.uhf:dimen/m3_comp_search_bar_pressed_state_layer_opacity = 0x7f070177
com.example.uhf:string/app_name = 0x7f12005c
com.example.uhf:string/app_msg_Upgrade = 0x7f120059
com.example.uhf:id/notification_main_column = 0x7f0901e3
com.example.uhf:string/app_error = 0x7f120056
com.example.uhf:string/ap_title_pwd = 0x7f120052
com.example.uhf:style/ShapeAppearance.M3.Comp.SearchView.FullScreen.Container.Shape = 0x7f13018c
com.example.uhf:style/ThemeOverlay.Material3.PersonalizedColors = 0x7f1302de
com.example.uhf:color/m3_ref_palette_secondary95 = 0x7f06015b
com.example.uhf:string/ap_title_goon = 0x7f12004f
com.example.uhf:id/accessibility_custom_action_24 = 0x7f090044
com.example.uhf:string/ap_title_bssid = 0x7f12004e
com.example.uhf:style/Base.Theme.AppCompat.Dialog.MinWidth = 0x7f130054
com.example.uhf:color/m3_tabs_icon_color_secondary = 0x7f060217
com.example.uhf:string/ap_list_header_state = 0x7f12004d
com.example.uhf:string/ap_list_header_level = 0x7f12004b
com.example.uhf:string/ap_dialog_title = 0x7f120049
com.example.uhf:color/m3_dynamic_dark_default_color_secondary_text = 0x7f060099
com.example.uhf:string/androidx_startup = 0x7f120048
com.example.uhf:string/action_uhf_ver = 0x7f120047
com.example.uhf:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f13008a
com.example.uhf:string/action_settings = 0x7f120046
com.example.uhf:styleable/SplitPairFilter = 0x7f140091
com.example.uhf:string/contents_location = 0x7f1200fd
com.example.uhf:string/action_psam_ver = 0x7f120042
com.example.uhf:attr/subtitleCentered = 0x7f040442
com.example.uhf:string/mtrl_picker_a11y_prev_month = 0x7f120290
com.example.uhf:string/action_fingerprint_ver = 0x7f120040
com.example.uhf:macro/m3_comp_switch_unselected_hover_track_color = 0x7f0d0138
com.example.uhf:string/action_back = 0x7f12003e
com.example.uhf:string/action_about = 0x7f12003d
com.example.uhf:style/Base.Widget.AppCompat.RatingBar.Indicator = 0x7f1300f5
com.example.uhf:id/sin = 0x7f090260
com.example.uhf:string/action = 0x7f12003c
com.example.uhf:array/fileEndingPackage = 0x7f030038
com.example.uhf:attr/overlapAnchor = 0x7f040386
com.example.uhf:string/abc_toolbar_collapse_description = 0x7f12003b
com.example.uhf:string/abc_shareactionprovider_share_with = 0x7f120039
com.example.uhf:string/abc_searchview_description_submit = 0x7f120037
com.example.uhf:string/abc_searchview_description_search = 0x7f120036
com.example.uhf:string/bluetooth_btn_yes = 0x7f120098
com.example.uhf:styleable/KeyTrigger = 0x7f14004c
com.example.uhf:string/abc_searchview_description_query = 0x7f120035
com.example.uhf:string/abc_searchview_description_clear = 0x7f120034
com.example.uhf:attr/colorOnBackground = 0x7f040106
com.example.uhf:string/abc_menu_space_shortcut_label = 0x7f120030
com.example.uhf:string/abc_menu_delete_shortcut_label = 0x7f12002b
com.example.uhf:dimen/mtrl_exposed_dropdown_menu_popup_vertical_offset = 0x7f0702a9
com.example.uhf:string/abc_menu_ctrl_shortcut_label = 0x7f12002a
com.example.uhf:style/Widget.Material3.SearchBar = 0x7f1303f0
com.example.uhf:string/abc_capital_on = 0x7f120028
com.example.uhf:string/abc_activity_chooser_view_see_all = 0x7f120025
com.example.uhf:string/abc_action_mode_done = 0x7f120024
com.example.uhf:string/abc_action_menu_overflow_description = 0x7f120023
com.example.uhf:string/Weibo_Share_Success = 0x7f120020
com.example.uhf:color/gray3 = 0x7f06006a
com.example.uhf:string/PrintContent = 0x7f120018
com.example.uhf:macro/m3_comp_outlined_text_field_error_trailing_icon_color = 0x7f0d00b8
com.example.uhf:string/Print = 0x7f120017
com.example.uhf:string/Others = 0x7f120016
com.example.uhf:attr/backgroundInsetEnd = 0x7f040051
com.example.uhf:color/mtrl_btn_text_color_selector = 0x7f0602d9
com.example.uhf:id/etLen_filter = 0x7f09012f
com.example.uhf:string/OAUTH_RequestToken_ERROR = 0x7f120015
com.example.uhf:string/New_Zealand = 0x7f12000f
com.example.uhf:string/keyboard_title_single = 0x7f1201e2
com.example.uhf:id/EtPtr_Write = 0x7f090012
com.example.uhf:string/Morocco = 0x7f12000e
com.example.uhf:string/GB_tag_lock = 0x7f12000a
com.example.uhf:string/mtrl_picker_range_header_only_end_selected = 0x7f1202a2
com.example.uhf:string/Europe = 0x7f120008
com.example.uhf:string/Clear = 0x7f120004
com.example.uhf:style/Widget.Material3.MaterialDivider.Heavy = 0x7f1303dc
com.example.uhf:attr/boxCollapsedPaddingTop = 0x7f040087
com.example.uhf:string/China_Standard_840_845MHz = 0x7f120002
com.example.uhf:drawable/preference_list_divider_material = 0x7f080100
com.example.uhf:string/yid_title = 0x7f1204bf
com.example.uhf:string/China = 0x7f120001
com.example.uhf:attr/controlBackground = 0x7f040153
com.example.uhf:style/Preference.Material = 0x7f13015d
com.example.uhf:string/America = 0x7f120000
com.example.uhf:raw/barcodebeep = 0x7f110000
com.example.uhf:style/Widget.Material3.SearchView.Prefix = 0x7f1303f3
com.example.uhf:id/material_value_index = 0x7f0901af
com.example.uhf:string/ul_title_lock_AFI = 0x7f120491
com.example.uhf:mipmap/ic_launcher = 0x7f0f0000
com.example.uhf:color/m3_ref_palette_dynamic_secondary10 = 0x7f0600f8
com.example.uhf:macro/m3_sys_color_light_surface_tint = 0x7f0d0176
com.example.uhf:macro/m3_comp_top_app_bar_small_trailing_icon_color = 0x7f0d0174
com.example.uhf:macro/m3_comp_top_app_bar_small_leading_icon_color = 0x7f0d0172
com.example.uhf:macro/m3_comp_top_app_bar_small_headline_type = 0x7f0d0171
com.example.uhf:macro/m3_comp_top_app_bar_small_headline_color = 0x7f0d0170
com.example.uhf:macro/m3_comp_top_app_bar_medium_headline_type = 0x7f0d016e
com.example.uhf:macro/m3_comp_time_picker_time_selector_unselected_container_color = 0x7f0d0167
com.example.uhf:macro/m3_comp_time_picker_time_selector_selected_focus_state_layer_color = 0x7f0d0161
com.example.uhf:macro/m3_comp_time_picker_time_selector_label_text_type = 0x7f0d015f
com.example.uhf:macro/m3_comp_time_picker_time_selector_container_shape = 0x7f0d015e
com.example.uhf:style/Widget.MaterialComponents.Chip.Filter = 0x7f130439
com.example.uhf:macro/m3_comp_time_picker_period_selector_unselected_pressed_state_layer_color = 0x7f0d015d
com.example.uhf:dimen/abc_panel_menu_list_width = 0x7f070034
com.example.uhf:macro/m3_comp_time_picker_period_selector_unselected_label_text_color = 0x7f0d015c
com.example.uhf:string/rfid_msg_write_fail = 0x7f120354
com.example.uhf:macro/m3_comp_time_picker_period_selector_unselected_focus_state_layer_color = 0x7f0d015a
com.example.uhf:id/circle_center = 0x7f0900de
com.example.uhf:macro/m3_comp_time_picker_period_selector_selected_label_text_color = 0x7f0d0158
com.example.uhf:macro/m3_comp_time_picker_period_selector_label_text_type = 0x7f0d0153
com.example.uhf:dimen/material_emphasis_disabled_background = 0x7f070239
com.example.uhf:id/accessibility_custom_action_9 = 0x7f090052
com.example.uhf:macro/m3_comp_time_picker_period_selector_container_shape = 0x7f0d0152
com.example.uhf:anim/linear_indeterminate_line2_tail_interpolator = 0x7f010021
com.example.uhf:macro/m3_comp_time_picker_time_selector_unselected_hover_state_layer_color = 0x7f0d0169
com.example.uhf:macro/m3_comp_time_picker_headline_color = 0x7f0d0150
com.example.uhf:attr/flow_horizontalGap = 0x7f04020c
com.example.uhf:string/b14443_msg_rev = 0x7f120067
com.example.uhf:macro/m3_comp_time_picker_container_shape = 0x7f0d014f
com.example.uhf:macro/m3_comp_time_picker_clock_dial_selector_handle_container_color = 0x7f0d014d
com.example.uhf:macro/m3_comp_time_picker_clock_dial_color = 0x7f0d014c
com.example.uhf:id/coordinator = 0x7f0900ed
com.example.uhf:macro/m3_comp_time_input_time_input_field_supporting_text_type = 0x7f0d014b
com.example.uhf:macro/m3_comp_time_input_time_input_field_label_text_color = 0x7f0d0149
com.example.uhf:macro/m3_comp_time_input_time_input_field_container_shape = 0x7f0d0147
com.example.uhf:macro/m3_comp_text_button_pressed_state_layer_color = 0x7f0d0146
com.example.uhf:anim/abc_grow_fade_in_from_bottom = 0x7f010002
com.example.uhf:string/side_sheet_behavior = 0x7f120375
com.example.uhf:string/battery_tips_extra_dl = 0x7f12007f
com.example.uhf:attr/materialCircleRadius = 0x7f040313
com.example.uhf:macro/m3_comp_text_button_label_text_type = 0x7f0d0145
com.example.uhf:attr/preferenceTheme = 0x7f0403b6
com.example.uhf:macro/m3_comp_text_button_hover_state_layer_color = 0x7f0d0143
com.example.uhf:style/Theme.Design.Light.NoActionBar = 0x7f130250
com.example.uhf:macro/m3_comp_switch_unselected_track_outline_color = 0x7f0d0141
com.example.uhf:color/m3_ref_palette_dynamic_neutral40 = 0x7f0600c4
com.example.uhf:macro/m3_comp_switch_unselected_track_color = 0x7f0d0140
com.example.uhf:macro/m3_comp_switch_unselected_pressed_track_outline_color = 0x7f0d013f
com.example.uhf:string/fingerprint_btn_get_Baudrate = 0x7f120177
com.example.uhf:style/TextAppearance.MaterialComponents.Tooltip = 0x7f130232
com.example.uhf:attr/shapeAppearanceCornerExtraSmall = 0x7f0403ef
com.example.uhf:string/btInventory = 0x7f1200b3
com.example.uhf:string/load_ing = 0x7f1201f5
com.example.uhf:macro/m3_comp_filled_text_field_error_active_indicator_color = 0x7f0d004d
com.example.uhf:macro/m3_comp_switch_unselected_icon_color = 0x7f0d013a
com.example.uhf:string/expand_button_title = 0x7f120168
com.example.uhf:macro/m3_comp_switch_unselected_hover_track_outline_color = 0x7f0d0139
com.example.uhf:drawable/$m3_avd_show_password__1 = 0x7f08000b
com.example.uhf:attr/blendSrc = 0x7f04007a
com.example.uhf:style/Base.Widget.AppCompat.ListView.DropDown = 0x7f1300ed
com.example.uhf:style/Base.V7.Widget.AppCompat.AutoCompleteTextView = 0x7f1300c4
com.example.uhf:macro/m3_comp_switch_unselected_hover_state_layer_color = 0x7f0d0137
com.example.uhf:macro/m3_comp_switch_unselected_hover_handle_color = 0x7f0d0135
com.example.uhf:attr/reactiveGuide_valueId = 0x7f0403cd
com.example.uhf:string/menu_help = 0x7f120238
com.example.uhf:style/Widget.Material3.Slider.Legacy = 0x7f1303fb
com.example.uhf:array/arrayChannelSpc1 = 0x7f03000b
com.example.uhf:string/preferences_remember_duplicates_title = 0x7f120302
com.example.uhf:macro/m3_comp_switch_unselected_handle_color = 0x7f0d0134
com.example.uhf:string/fingerprint_btn_set_Baudrate = 0x7f12017f
com.example.uhf:dimen/mtrl_progress_indicator_full_rounded_corner_radius = 0x7f0702e5
com.example.uhf:macro/m3_comp_switch_unselected_focus_state_layer_color = 0x7f0d0131
com.example.uhf:attr/boxBackgroundColor = 0x7f040085
com.example.uhf:dimen/mtrl_calendar_maximum_default_fullscreen_minor_axis = 0x7f07028d
com.example.uhf:dimen/text_size_15 = 0x7f070338
com.example.uhf:macro/m3_comp_switch_unselected_focus_handle_color = 0x7f0d012f
com.example.uhf:string/msg_default_mms_subject = 0x7f120245
com.example.uhf:macro/m3_comp_switch_selected_pressed_state_layer_color = 0x7f0d012c
com.example.uhf:macro/m3_comp_snackbar_container_color = 0x7f0d0113
com.example.uhf:macro/m3_comp_switch_selected_pressed_icon_color = 0x7f0d012b
com.example.uhf:attr/boxCornerRadiusBottomStart = 0x7f040089
com.example.uhf:macro/m3_comp_switch_selected_icon_color = 0x7f0d0129
com.example.uhf:macro/m3_comp_switch_selected_handle_color = 0x7f0d0124
com.example.uhf:macro/m3_comp_switch_selected_focus_track_color = 0x7f0d0123
com.example.uhf:attr/layout_scrollEffect = 0x7f0402d5
com.example.uhf:macro/m3_comp_switch_disabled_unselected_track_outline_color = 0x7f0d011f
com.example.uhf:macro/m3_comp_switch_disabled_unselected_icon_color = 0x7f0d011d
com.example.uhf:dimen/mtrl_badge_text_size = 0x7f070254
com.example.uhf:string/tel_title_OperatorName = 0x7f12038a
com.example.uhf:style/TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f1301e7
com.example.uhf:macro/m3_comp_switch_disabled_selected_track_color = 0x7f0d011b
com.example.uhf:dimen/m3_comp_switch_unselected_focus_state_layer_opacity = 0x7f07019e
com.example.uhf:id/month_navigation_previous = 0x7f0901b8
com.example.uhf:macro/m3_comp_filled_button_label_text_color = 0x7f0d0044
com.example.uhf:macro/m3_comp_switch_disabled_selected_icon_color = 0x7f0d011a
com.example.uhf:attr/checkedIconMargin = 0x7f0400c1
com.example.uhf:macro/m3_comp_suggestion_chip_container_shape = 0x7f0d0117
com.example.uhf:id/view_offset_helper = 0x7f0902d2
com.example.uhf:layout/design_menu_item_action_area = 0x7f0c0025
com.example.uhf:macro/m3_comp_snackbar_supporting_text_type = 0x7f0d0116
com.example.uhf:dimen/mtrl_calendar_action_padding = 0x7f070278
com.example.uhf:macro/m3_comp_snackbar_container_shape = 0x7f0d0114
com.example.uhf:dimen/m3_badge_offset = 0x7f0700b9
com.example.uhf:dimen/m3_side_sheet_width = 0x7f0701ed
com.example.uhf:macro/m3_comp_slider_label_label_text_color = 0x7f0d0112
com.example.uhf:attr/materialIconButtonFilledStyle = 0x7f040318
com.example.uhf:macro/m3_comp_slider_label_container_color = 0x7f0d0111
com.example.uhf:drawable/design_ic_visibility_off = 0x7f080095
com.example.uhf:string/m1_title_sector = 0x7f12020c
com.example.uhf:color/m3_sys_color_dynamic_dark_tertiary_container = 0x7f0601b9
com.example.uhf:macro/m3_comp_slider_active_track_color = 0x7f0d010b
com.example.uhf:attr/logo = 0x7f0402ef
com.example.uhf:layout/material_timepicker = 0x7f0c004f
com.example.uhf:macro/m3_comp_sheet_side_docked_standard_container_color = 0x7f0d010a
com.example.uhf:attr/textureBlurFactor = 0x7f0404b3
com.example.uhf:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner = 0x7f130136
com.example.uhf:macro/m3_comp_sheet_bottom_docked_drag_handle_color = 0x7f0d0106
com.example.uhf:string/uhf_title_uii = 0x7f12048c
com.example.uhf:string/uhf_msg_write_fail = 0x7f120478
com.example.uhf:string/uhf_msg_set_succ = 0x7f12046d
com.example.uhf:macro/m3_comp_sheet_bottom_docked_container_shape = 0x7f0d0105
com.example.uhf:macro/m3_comp_secondary_navigation_tab_with_icon_active_icon_color = 0x7f0d0102
com.example.uhf:string/Korea = 0x7f12000d
com.example.uhf:macro/m3_comp_secondary_navigation_tab_pressed_state_layer_color = 0x7f0d0101
com.example.uhf:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day = 0x7f1301ba
com.example.uhf:id/tvConnectionStatus = 0x7f0902bf
com.example.uhf:macro/m3_comp_secondary_navigation_tab_label_text_type = 0x7f0d0100
com.example.uhf:macro/m3_comp_secondary_navigation_tab_inactive_label_text_color = 0x7f0d00ff
com.example.uhf:macro/m3_comp_secondary_navigation_tab_hover_state_layer_color = 0x7f0d00fe
com.example.uhf:style/Base.Widget.MaterialComponents.MaterialCalendar.NavigationButton = 0x7f13011a
com.example.uhf:macro/m3_comp_secondary_navigation_tab_focus_state_layer_color = 0x7f0d00fd
com.example.uhf:macro/m3_comp_secondary_navigation_tab_active_indicator_color = 0x7f0d00fa
com.example.uhf:macro/m3_comp_search_view_header_supporting_text_type = 0x7f0d00f8
com.example.uhf:string/rfid_msg_type = 0x7f120350
com.example.uhf:color/m3_sys_color_dynamic_dark_tertiary = 0x7f0601b8
com.example.uhf:macro/m3_comp_search_view_header_supporting_text_color = 0x7f0d00f7
com.example.uhf:id/hideable = 0x7f090166
com.example.uhf:style/Base.Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f13007b
com.example.uhf:macro/m3_comp_search_view_header_input_text_color = 0x7f0d00f4
com.example.uhf:macro/m3_comp_search_view_container_color = 0x7f0d00f1
com.example.uhf:attr/activityChooserViewStyle = 0x7f040028
com.example.uhf:attr/suffixTextAppearance = 0x7f040447
com.example.uhf:macro/m3_comp_search_bar_leading_icon_color = 0x7f0d00eb
com.example.uhf:style/TextAppearance.MaterialComponents.TimePicker.Title = 0x7f130231
com.example.uhf:color/material_personalized_color_on_background = 0x7f06029c
com.example.uhf:macro/m3_comp_search_bar_input_text_color = 0x7f0d00e9
com.example.uhf:macro/m3_comp_search_bar_container_color = 0x7f0d00e6
com.example.uhf:macro/m3_comp_radio_button_selected_pressed_state_layer_color = 0x7f0d00de
com.example.uhf:style/Widget.Material3.CompoundButton.MaterialSwitch = 0x7f1303a8
com.example.uhf:macro/m3_comp_radio_button_selected_hover_icon_color = 0x7f0d00da
com.example.uhf:attr/transitionDisable = 0x7f0404ff
com.example.uhf:dimen/m3_comp_slider_disabled_active_track_opacity = 0x7f070189
com.example.uhf:macro/m3_comp_radio_button_disabled_selected_icon_color = 0x7f0d00d6
com.example.uhf:macro/m3_comp_progress_indicator_track_color = 0x7f0d00d5
com.example.uhf:dimen/m3_btn_max_width = 0x7f0700dd
com.example.uhf:macro/m3_comp_progress_indicator_active_indicator_color = 0x7f0d00d4
com.example.uhf:macro/m3_comp_primary_navigation_tab_with_icon_inactive_icon_color = 0x7f0d00d0
com.example.uhf:dimen/m3_navigation_rail_item_padding_bottom_with_large_font = 0x7f0701d6
com.example.uhf:string/update_title_update_last = 0x7f1204a9
com.example.uhf:dimen/material_font_1_3_box_collapsed_padding_top = 0x7f070240
com.example.uhf:macro/m3_comp_primary_navigation_tab_with_icon_active_icon_color = 0x7f0d00cf
com.example.uhf:macro/m3_comp_primary_navigation_tab_inactive_hover_state_layer_color = 0x7f0d00cd
com.example.uhf:id/icon_frame = 0x7f09016c
com.example.uhf:macro/m3_comp_primary_navigation_tab_inactive_focus_state_layer_color = 0x7f0d00cc
com.example.uhf:macro/m3_comp_primary_navigation_tab_container_color = 0x7f0d00cb
com.example.uhf:style/TextAppearance.AppCompat.Display4 = 0x7f1301c6
com.example.uhf:dimen/mtrl_badge_toolbar_action_menu_item_vertical_offset = 0x7f070256
com.example.uhf:macro/m3_comp_primary_navigation_tab_active_indicator_color = 0x7f0d00c9
com.example.uhf:string/rbInventoryAnti = 0x7f12031d
com.example.uhf:macro/m3_comp_primary_navigation_tab_active_hover_state_layer_color = 0x7f0d00c8
com.example.uhf:animator/design_fab_hide_motion_spec = 0x7f020001
com.example.uhf:macro/m3_comp_primary_navigation_tab_active_focus_state_layer_color = 0x7f0d00c7
com.example.uhf:macro/m3_comp_outlined_text_field_supporting_text_type = 0x7f0d00c5
com.example.uhf:attr/waveVariesBy = 0x7f04051e
com.example.uhf:macro/m3_comp_outlined_text_field_supporting_text_color = 0x7f0d00c4
com.example.uhf:string/EPC_TID_off = 0x7f120006
com.example.uhf:dimen/preferences_detail_width = 0x7f07032e
com.example.uhf:macro/m3_comp_outlined_text_field_input_text_color = 0x7f0d00c0
com.example.uhf:macro/m3_comp_outlined_text_field_hover_outline_color = 0x7f0d00be
com.example.uhf:id/animateToEnd = 0x7f090074
com.example.uhf:macro/m3_comp_outlined_text_field_hover_input_text_color = 0x7f0d00bd
com.example.uhf:styleable/MaterialToolbar = 0x7f140064
com.example.uhf:macro/m3_comp_outlined_text_field_focus_supporting_text_color = 0x7f0d00bc
com.example.uhf:macro/m3_comp_outlined_text_field_disabled_outline_color = 0x7f0d00b4
com.example.uhf:macro/m3_comp_outlined_text_field_disabled_label_text_color = 0x7f0d00b3
com.example.uhf:style/Preference.CheckBoxPreference = 0x7f130153
com.example.uhf:macro/m3_comp_outlined_text_field_container_shape = 0x7f0d00b1
com.example.uhf:macro/m3_comp_navigation_bar_active_hover_icon_color = 0x7f0d0062
com.example.uhf:macro/m3_comp_outlined_text_field_caret_color = 0x7f0d00b0
com.example.uhf:macro/m3_comp_outlined_card_hover_outline_color = 0x7f0d00ad
com.example.uhf:macro/m3_comp_outlined_card_focus_outline_color = 0x7f0d00ac
com.example.uhf:macro/m3_comp_outlined_card_dragged_outline_color = 0x7f0d00ab
com.example.uhf:macro/m3_comp_outlined_card_disabled_outline_color = 0x7f0d00aa
com.example.uhf:string/mtrl_picker_toggle_to_text_input_mode = 0x7f1202b2
com.example.uhf:layout/design_bottom_navigation_item = 0x7f0c001f
com.example.uhf:macro/m3_comp_radio_button_unselected_focus_icon_color = 0x7f0d00df
com.example.uhf:macro/m3_comp_outlined_card_container_color = 0x7f0d00a8
com.example.uhf:macro/m3_comp_outlined_button_pressed_outline_color = 0x7f0d00a7
com.example.uhf:style/ThemeOverlay.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1302db
com.example.uhf:macro/m3_comp_outlined_button_disabled_outline_color = 0x7f0d00a3
com.example.uhf:color/abc_search_url_text_pressed = 0x7f06000f
com.example.uhf:string/title_about_mac = 0x7f12039d
com.example.uhf:macro/m3_comp_outlined_autocomplete_text_field_input_text_type = 0x7f0d00a2
com.example.uhf:macro/m3_comp_outlined_autocomplete_text_field_caret_color = 0x7f0d00a1
com.example.uhf:dimen/m3_comp_text_button_hover_state_layer_opacity = 0x7f0701a2
com.example.uhf:macro/m3_comp_outlined_autocomplete_menu_container_color = 0x7f0d00a0
com.example.uhf:macro/m3_comp_switch_unselected_focus_track_color = 0x7f0d0132
com.example.uhf:attr/actionModeFindDrawable = 0x7f040018
com.example.uhf:attr/defaultScrollFlagsEnabled = 0x7f04017d
com.example.uhf:dimen/m3_comp_primary_navigation_tab_with_icon_icon_size = 0x7f070166
com.example.uhf:macro/m3_comp_navigation_rail_inactive_label_text_color = 0x7f0d009d
com.example.uhf:macro/m3_comp_navigation_rail_inactive_hover_state_layer_color = 0x7f0d009b
com.example.uhf:layout/rardar_view = 0x7f0c0089
com.example.uhf:macro/m3_comp_navigation_rail_active_pressed_state_layer_color = 0x7f0d0098
com.example.uhf:color/material_dynamic_tertiary80 = 0x7f06027b
com.example.uhf:macro/m3_comp_navigation_rail_active_indicator_color = 0x7f0d0096
com.example.uhf:macro/m3_comp_navigation_rail_active_hover_state_layer_color = 0x7f0d0094
com.example.uhf:id/accessibility_custom_action_14 = 0x7f090039
com.example.uhf:macro/m3_comp_navigation_rail_active_focus_state_layer_color = 0x7f0d0093
com.example.uhf:macro/m3_comp_navigation_drawer_inactive_pressed_label_text_color = 0x7f0d008f
com.example.uhf:style/PreferenceThemeOverlay = 0x7f13016c
com.example.uhf:macro/m3_comp_navigation_drawer_inactive_hover_label_text_color = 0x7f0d008a
com.example.uhf:macro/m3_comp_navigation_drawer_inactive_focus_state_layer_color = 0x7f0d0088
com.example.uhf:macro/m3_comp_navigation_drawer_inactive_focus_icon_color = 0x7f0d0086
com.example.uhf:style/Base.Widget.Material3.ActionMode = 0x7f130103
com.example.uhf:string/gps_title_SatelliteCount = 0x7f1201cc
com.example.uhf:macro/m3_comp_navigation_drawer_active_indicator_color = 0x7f0d007f
com.example.uhf:color/material_harmonized_color_error = 0x7f060286
com.example.uhf:macro/m3_comp_navigation_drawer_active_focus_state_layer_color = 0x7f0d007a
com.example.uhf:attr/colorPrimaryVariant = 0x7f040124
com.example.uhf:string/tvUSER_Lock = 0x7f120431
com.example.uhf:macro/m3_comp_time_input_time_input_field_focus_outline_color = 0x7f0d0148
com.example.uhf:macro/m3_comp_navigation_bar_inactive_pressed_label_text_color = 0x7f0d0075
com.example.uhf:string/title_activity_needle = 0x7f1203bb
com.example.uhf:macro/m3_comp_navigation_bar_inactive_pressed_icon_color = 0x7f0d0074
com.example.uhf:string/fingerprint_msg_acq_succ = 0x7f12018a
com.example.uhf:dimen/mtrl_btn_padding_right = 0x7f07026c
com.example.uhf:string/share = 0x7f120371
com.example.uhf:macro/m3_comp_navigation_bar_inactive_focus_state_layer_color = 0x7f0d006e
com.example.uhf:macro/m3_comp_navigation_bar_inactive_focus_label_text_color = 0x7f0d006d
com.example.uhf:id/etLockCode = 0x7f090136
com.example.uhf:id/open_search_view_scrim = 0x7f0901f1
com.example.uhf:macro/m3_comp_navigation_bar_container_color = 0x7f0d006b
com.example.uhf:style/Widget.MaterialComponents.MaterialCalendar.Day.Invalid = 0x7f13044b
com.example.uhf:macro/m3_comp_navigation_bar_active_indicator_color = 0x7f0d0066
com.example.uhf:color/material_personalized_color_on_secondary_container = 0x7f0602a2
com.example.uhf:macro/m3_comp_navigation_bar_active_icon_color = 0x7f0d0065
com.example.uhf:macro/m3_comp_navigation_bar_active_hover_state_layer_color = 0x7f0d0064
com.example.uhf:string/fingerprint_msg_verify_PSW_succ = 0x7f1201a0
com.example.uhf:macro/m3_comp_menu_container_color = 0x7f0d005d
com.example.uhf:macro/m3_comp_input_chip_container_shape = 0x7f0d005b
com.example.uhf:attr/textAppearanceHeadline1 = 0x7f040481
com.example.uhf:attr/badgeWithTextHeight = 0x7f040065
com.example.uhf:id/BOTTOM_END = 0x7f090001
com.example.uhf:macro/m3_comp_icon_button_selected_icon_color = 0x7f0d0059
com.example.uhf:macro/m3_comp_filter_chip_label_text_type = 0x7f0d0058
com.example.uhf:macro/m3_comp_filled_tonal_icon_button_toggle_unselected_icon_color = 0x7f0d0056
com.example.uhf:string/ul_title_write_DSFID = 0x7f120496
com.example.uhf:string/ETSI_Standard = 0x7f120007
com.example.uhf:macro/m3_comp_filled_tonal_button_container_color = 0x7f0d0052
com.example.uhf:attr/badgeShapeAppearance = 0x7f04005c
com.example.uhf:macro/m3_comp_filled_text_field_error_trailing_icon_color = 0x7f0d004f
com.example.uhf:attr/clockNumberTextColor = 0x7f0400e8
com.example.uhf:macro/m3_comp_filled_text_field_error_supporting_text_color = 0x7f0d004e
com.example.uhf:style/Base.V14.Theme.Material3.Dark.Dialog = 0x7f130090
com.example.uhf:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f13004d
com.example.uhf:string/rfid_msg_scan_continuous = 0x7f12034e
com.example.uhf:style/Widget.AppCompat.Light.SearchView = 0x7f130340
com.example.uhf:string/b14443_title_rev = 0x7f12006f
com.example.uhf:macro/m3_comp_filled_icon_button_toggle_unselected_icon_color = 0x7f0d004a
com.example.uhf:macro/m3_comp_filled_card_container_shape = 0x7f0d0047
com.example.uhf:style/TextAppearance.Design.Counter = 0x7f1301f4
com.example.uhf:macro/m3_comp_filled_button_container_color = 0x7f0d0043
com.example.uhf:macro/m3_comp_navigation_drawer_active_hover_label_text_color = 0x7f0d007c
com.example.uhf:macro/m3_comp_filled_autocomplete_menu_container_color = 0x7f0d0041
com.example.uhf:macro/m3_comp_fab_surface_icon_color = 0x7f0d003e
com.example.uhf:macro/m3_comp_fab_secondary_icon_color = 0x7f0d003c
com.example.uhf:string/publishing = 0x7f12031b
com.example.uhf:macro/m3_comp_fab_primary_large_container_shape = 0x7f0d0039
com.example.uhf:color/m3_ref_palette_neutral30 = 0x7f060126
com.example.uhf:layout/abc_action_mode_bar = 0x7f0c0004
com.example.uhf:macro/m3_comp_fab_primary_icon_color = 0x7f0d0038
com.example.uhf:id/EtLen_Write = 0x7f090010
com.example.uhf:macro/m3_comp_fab_primary_container_shape = 0x7f0d0037
com.example.uhf:macro/m3_comp_extended_fab_tertiary_container_color = 0x7f0d0034
com.example.uhf:string/gps_btn_no = 0x7f1201c0
com.example.uhf:macro/m3_comp_extended_fab_surface_icon_color = 0x7f0d0033
com.example.uhf:color/m3_assist_chip_icon_tint_color = 0x7f06007d
com.example.uhf:macro/m3_comp_extended_fab_secondary_container_color = 0x7f0d0030
com.example.uhf:styleable/MultiSelectListPreference = 0x7f140070
com.example.uhf:attr/simpleItemSelectedRippleColor = 0x7f04040a
com.example.uhf:attr/textBackgroundPanY = 0x7f04049e
com.example.uhf:macro/m3_comp_extended_fab_primary_container_shape = 0x7f0d002d
com.example.uhf:attr/isMaterialTheme = 0x7f040265
com.example.uhf:dimen/mtrl_calendar_year_height = 0x7f07029c
com.example.uhf:macro/m3_comp_extended_fab_primary_container_color = 0x7f0d002c
com.example.uhf:id/mtrl_picker_text_input_range_end = 0x7f0901ce
com.example.uhf:macro/m3_comp_dialog_headline_type = 0x7f0d0025
com.example.uhf:macro/m3_comp_dialog_container_color = 0x7f0d0022
com.example.uhf:dimen/m3_sys_motion_easing_standard_control_x1 = 0x7f070217
com.example.uhf:layout/material_clock_display = 0x7f0c0045
com.example.uhf:macro/m3_comp_date_picker_modal_year_selection_year_selected_label_text_color = 0x7f0d0020
com.example.uhf:macro/m3_comp_date_picker_modal_year_selection_year_selected_container_color = 0x7f0d001f
com.example.uhf:string/update_msg_checking = 0x7f1204a0
com.example.uhf:dimen/material_font_2_0_box_collapsed_padding_top = 0x7f070241
com.example.uhf:macro/m3_comp_date_picker_modal_weekdays_label_text_color = 0x7f0d001d
com.example.uhf:macro/m3_comp_date_picker_modal_range_selection_month_subhead_type = 0x7f0d001c
com.example.uhf:macro/m3_comp_date_picker_modal_range_selection_active_indicator_container_color = 0x7f0d0019
com.example.uhf:style/Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge = 0x7f130297
com.example.uhf:macro/m3_comp_date_picker_modal_header_supporting_text_color = 0x7f0d0017
com.example.uhf:attr/textAppearanceSubtitle2 = 0x7f040498
com.example.uhf:macro/m3_comp_date_picker_modal_header_headline_type = 0x7f0d0016
com.example.uhf:attr/customReference = 0x7f040174
com.example.uhf:style/Base.Widget.AppCompat.ActionButton = 0x7f1300cc
com.example.uhf:string/er_dsoft_tab_scan = 0x7f120162
com.example.uhf:macro/m3_comp_navigation_drawer_inactive_focus_label_text_color = 0x7f0d0087
com.example.uhf:id/sharedValueSet = 0x7f09025a
com.example.uhf:macro/m3_comp_date_picker_modal_date_selected_label_text_color = 0x7f0d0011
com.example.uhf:string/abc_menu_function_shortcut_label = 0x7f12002d
com.example.uhf:style/ThemeOverlay.Material3.MaterialAlertDialog = 0x7f1302d5
com.example.uhf:macro/m3_comp_checkbox_unselected_outline_color = 0x7f0d000c
com.example.uhf:macro/m3_comp_checkbox_selected_icon_color = 0x7f0d000b
com.example.uhf:style/Base.V21.Theme.MaterialComponents = 0x7f1300aa
com.example.uhf:macro/m3_comp_checkbox_selected_error_container_color = 0x7f0d0009
com.example.uhf:macro/m3_comp_checkbox_selected_container_color = 0x7f0d0006
com.example.uhf:style/Widget.MaterialComponents.FloatingActionButton = 0x7f130445
com.example.uhf:drawable/$mtrl_checkbox_button_icon_indeterminate_checked__0 = 0x7f080014
com.example.uhf:macro/m3_comp_badge_large_label_text_type = 0x7f0d0004
com.example.uhf:dimen/m3_btn_elevated_btn_elevation = 0x7f0700d4
com.example.uhf:string/fingerprint_msg_page_id_need_digits = 0x7f120194
com.example.uhf:id/pressed = 0x7f09020a
com.example.uhf:layout/uhf_read_write_fragment = 0x7f0c0093
com.example.uhf:layout/uhf_lock_fragment = 0x7f0c0092
com.example.uhf:color/m3_ref_palette_neutral96 = 0x7f060133
com.example.uhf:layout/uhf_light_fragment = 0x7f0c0091
com.example.uhf:layout/uhf_dialog_lock_code = 0x7f0c008f
com.example.uhf:color/material_personalized_color_error_container = 0x7f06029b
com.example.uhf:layout/uhf_dialog_frequency = 0x7f0c008e
com.example.uhf:layout/preference_widget_switch_compat = 0x7f0c0088
com.example.uhf:layout/preference_widget_switch = 0x7f0c0087
com.example.uhf:layout/preference_widget_seekbar_material = 0x7f0c0086
com.example.uhf:layout/preference_recyclerview = 0x7f0c0083
com.example.uhf:layout/preference_list_fragment = 0x7f0c0081
com.example.uhf:layout/preference_information = 0x7f0c007f
com.example.uhf:color/abc_hint_foreground_material_light = 0x7f060008
com.example.uhf:color/m3_ref_palette_dynamic_tertiary80 = 0x7f06010d
com.example.uhf:layout/preference_dropdown = 0x7f0c007d
com.example.uhf:styleable/RadarView = 0x7f140080
com.example.uhf:color/design_icon_tint = 0x7f06005a
com.example.uhf:layout/preference = 0x7f0c0079
com.example.uhf:layout/popwindow_filter = 0x7f0c0078
com.example.uhf:style/ThemeOverlay.Material3.TextInputEditText.FilledBox.Dense = 0x7f1302e5
com.example.uhf:attr/panelBackground = 0x7f040391
com.example.uhf:dimen/abc_text_size_button_material = 0x7f070041
com.example.uhf:layout/notification_template_part_chronometer = 0x7f0c0076
com.example.uhf:layout/notification_template_icon_group = 0x7f0c0075
com.example.uhf:layout/notification_template_custom_big = 0x7f0c0074
com.example.uhf:style/Widget.AppCompat.Light.ListView.DropDown = 0x7f13033d
com.example.uhf:macro/m3_comp_slider_disabled_active_track_color = 0x7f0d010c
com.example.uhf:string/fingerprint_title_score = 0x7f1201b3
com.example.uhf:drawable/uponelevel = 0x7f08010b
com.example.uhf:color/m3_sys_color_primary_fixed = 0x7f060210
com.example.uhf:id/cb_filter_wt = 0x7f0900d1
com.example.uhf:macro/m3_comp_navigation_drawer_active_pressed_state_layer_color = 0x7f0d0083
com.example.uhf:layout/mtrl_search_bar = 0x7f0c0070
com.example.uhf:string/download_msg_confirm = 0x7f12014a
com.example.uhf:macro/m3_comp_filled_tonal_icon_button_container_color = 0x7f0d0054
com.example.uhf:style/MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked = 0x7f13013c
com.example.uhf:layout/mtrl_picker_text_input_date_range = 0x7f0c006f
com.example.uhf:string/tvQ = 0x7f12041f
com.example.uhf:attr/closeIconSize = 0x7f0400ec
com.example.uhf:layout/mtrl_picker_text_input_date = 0x7f0c006e
com.example.uhf:id/SpinnerBank = 0x7f090022
com.example.uhf:macro/m3_comp_navigation_drawer_active_focus_label_text_color = 0x7f0d0079
com.example.uhf:layout/mtrl_picker_header_title_text = 0x7f0c006c
com.example.uhf:layout/mtrl_picker_header_selection_text = 0x7f0c006b
com.example.uhf:color/gray2 = 0x7f060069
com.example.uhf:layout/mtrl_picker_header_dialog = 0x7f0c0069
com.example.uhf:layout/mtrl_picker_fullscreen = 0x7f0c0068
com.example.uhf:layout/mtrl_picker_dialog = 0x7f0c0067
com.example.uhf:color/material_personalized_color_background = 0x7f060296
com.example.uhf:style/Base.ThemeOverlay.Material3.AutoCompleteTextView = 0x7f130084
com.example.uhf:layout/mtrl_picker_actions = 0x7f0c0066
com.example.uhf:styleable/PropertySet = 0x7f14007f
com.example.uhf:drawable/mtrl_switch_thumb_checked = 0x7f0800e2
com.example.uhf:layout/mtrl_navigation_rail_item = 0x7f0c0065
com.example.uhf:macro/m3_comp_search_bar_supporting_text_type = 0x7f0d00ef
com.example.uhf:attr/chainUseRtl = 0x7f0400b6
com.example.uhf:layout/mtrl_calendar_year = 0x7f0c0062
com.example.uhf:style/Widget.Material3.FloatingActionButton.Secondary = 0x7f1303b9
com.example.uhf:style/Base.Theme.Material3.Dark = 0x7f13005d
com.example.uhf:attr/endIconTintMode = 0x7f0401bf
com.example.uhf:string/desfire_title_format = 0x7f12012d
com.example.uhf:layout/mtrl_calendar_vertical = 0x7f0c0061
com.example.uhf:layout/mtrl_calendar_horizontal = 0x7f0c005c
com.example.uhf:layout/mtrl_calendar_day = 0x7f0c0059
com.example.uhf:string/mtrl_picker_navigate_to_year_description = 0x7f1202a0
com.example.uhf:dimen/mtrl_extended_fab_disabled_elevation = 0x7f0702ac
com.example.uhf:id/ifRoom = 0x7f09016e
com.example.uhf:layout/mtrl_alert_select_dialog_multichoice = 0x7f0c0056
com.example.uhf:layout/material_timepicker_dialog = 0x7f0c0050
com.example.uhf:attr/layout_constraintGuide_end = 0x7f0402a9
com.example.uhf:layout/material_time_chip = 0x7f0c004d
com.example.uhf:string/hello_world = 0x7f1201d1
com.example.uhf:dimen/m3_snackbar_margin = 0x7f0701f4
com.example.uhf:layout/material_clockface_view = 0x7f0c004a
com.example.uhf:animator/m3_card_elevated_state_list_anim = 0x7f02000c
com.example.uhf:style/MaterialAlertDialog.Material3.Title.Panel = 0x7f13012f
com.example.uhf:layout/material_clock_period_toggle_land = 0x7f0c0048
com.example.uhf:id/supportScrollUp = 0x7f09028b
com.example.uhf:dimen/mtrl_btn_corner_radius = 0x7f07025e
com.example.uhf:layout/material_clock_period_toggle = 0x7f0c0047
com.example.uhf:style/ShapeAppearanceOverlay.MaterialAlertDialog.Material3 = 0x7f1301b5
com.example.uhf:layout/abc_expanded_menu_layout = 0x7f0c000d
com.example.uhf:macro/m3_comp_fab_surface_container_color = 0x7f0d003d
com.example.uhf:layout/mtrl_calendar_month = 0x7f0c005d
com.example.uhf:layout/material_chip_input_combo = 0x7f0c0044
com.example.uhf:color/m3_ref_palette_primary95 = 0x7f06014e
com.example.uhf:drawable/abc_list_selector_background_transition_holo_dark = 0x7f080053
com.example.uhf:layout/ime_base_split_test_activity = 0x7f0c003b
com.example.uhf:layout/fragment_upload_settings = 0x7f0c0039
com.example.uhf:layout/fragment_uhfupgrade = 0x7f0c0038
com.example.uhf:string/desfire_ms_not_null = 0x7f120108
com.example.uhf:style/Base.Widget.AppCompat.AutoCompleteTextView = 0x7f1300d1
com.example.uhf:string/uhf_msg_upgrade_fail = 0x7f120476
com.example.uhf:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Secondary = 0x7f1302ca
com.example.uhf:layout/fragment_uhflocation = 0x7f0c0037
com.example.uhf:color/m3_ref_palette_primary80 = 0x7f06014c
com.example.uhf:layout/fragment_uhf_set = 0x7f0c0036
com.example.uhf:attr/prefixText = 0x7f0403b7
com.example.uhf:layout/fragment_temperature = 0x7f0c0034
com.example.uhf:macro/m3_comp_text_button_label_text_color = 0x7f0d0144
com.example.uhf:string/title_start = 0x7f1203ee
com.example.uhf:layout/fragment_deactivate = 0x7f0c0032
com.example.uhf:string/ping_title = 0x7f1202eb
com.example.uhf:layout/fragment_blank = 0x7f0c002f
com.example.uhf:attr/cardViewStyle = 0x7f0400a8
com.example.uhf:layout/expand_button = 0x7f0c002e
com.example.uhf:layout/design_navigation_menu_item = 0x7f0c002b
com.example.uhf:drawable/abc_item_background_holo_light = 0x7f08004c
com.example.uhf:string/strBrowser = 0x7f12037e
com.example.uhf:layout/design_navigation_item_header = 0x7f0c0027
com.example.uhf:layout/design_navigation_item = 0x7f0c0026
com.example.uhf:layout/design_layout_tab_text = 0x7f0c0024
com.example.uhf:string/button_done = 0x7f1200ce
com.example.uhf:layout/design_layout_tab_icon = 0x7f0c0023
com.example.uhf:layout/design_layout_snackbar_include = 0x7f0c0022
com.example.uhf:color/transparent = 0x7f060342
com.example.uhf:attr/rippleColor = 0x7f0403d5
com.example.uhf:dimen/text_size_17 = 0x7f07033a
com.example.uhf:layout/design_layout_snackbar = 0x7f0c0021
com.example.uhf:dimen/m3_searchview_elevation = 0x7f0701e8
com.example.uhf:layout/design_bottom_sheet_dialog = 0x7f0c0020
com.example.uhf:string/mtrl_picker_text_input_date_range_end_hint = 0x7f1202aa
com.example.uhf:attr/layout_constraintGuide_percent = 0x7f0402aa
com.example.uhf:string/lp_btn_auto = 0x7f1201f9
com.example.uhf:string/tvGetUiiForOperation = 0x7f120409
com.example.uhf:string/battery_title_tips = 0x7f120086
com.example.uhf:layout/activity_main = 0x7f0c001c
com.example.uhf:layout/abc_tooltip = 0x7f0c001b
com.example.uhf:macro/m3_comp_sheet_bottom_docked_container_color = 0x7f0d0104
com.example.uhf:dimen/mtrl_navigation_rail_active_text_size = 0x7f0702d0
com.example.uhf:layout/abc_search_view = 0x7f0c0019
com.example.uhf:layout/abc_search_dropdown_item_icons_2line = 0x7f0c0018
com.example.uhf:layout/abc_popup_menu_header_item_layout = 0x7f0c0012
com.example.uhf:layout/abc_list_menu_item_radio = 0x7f0c0011
com.example.uhf:style/Theme.MaterialComponents.DayNight.DialogWhenLarge = 0x7f130281
com.example.uhf:layout/abc_list_menu_item_icon = 0x7f0c000f
com.example.uhf:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f130036
com.example.uhf:layout/abc_list_menu_item_checkbox = 0x7f0c000e
com.example.uhf:attr/alertDialogButtonGroupStyle = 0x7f04002c
com.example.uhf:macro/m3_comp_time_picker_headline_type = 0x7f0d0151
com.example.uhf:macro/m3_comp_time_picker_container_color = 0x7f0d014e
com.example.uhf:layout/abc_cascading_menu_item_layout = 0x7f0c000b
com.example.uhf:layout/abc_alert_dialog_title_material = 0x7f0c000a
com.example.uhf:layout/abc_alert_dialog_button_bar_material = 0x7f0c0008
com.example.uhf:style/Base.TextAppearance.AppCompat.Subhead = 0x7f130031
com.example.uhf:layout/abc_activity_chooser_view = 0x7f0c0006
com.example.uhf:attr/layout_goneMarginStart = 0x7f0402cf
com.example.uhf:attr/titleCollapseMode = 0x7f0404d6
com.example.uhf:dimen/m3_sys_motion_easing_linear_control_x1 = 0x7f07020f
com.example.uhf:string/R2000_set = 0x7f120019
com.example.uhf:layout/abc_action_bar_title_item = 0x7f0c0000
com.example.uhf:interpolator/mtrl_linear_out_slow_in = 0x7f0b0011
com.example.uhf:style/ShapeAppearance.M3.Comp.FilledButton.Container.Shape = 0x7f130184
com.example.uhf:interpolator/mtrl_linear = 0x7f0b0010
com.example.uhf:interpolator/mtrl_fast_out_slow_in = 0x7f0b000f
com.example.uhf:style/TextAppearance.Material3.ActionBar.Subtitle = 0x7f13020d
com.example.uhf:dimen/m3_navigation_rail_label_padding_horizontal = 0x7f0701d9
com.example.uhf:interpolator/m3_sys_motion_easing_standard_accelerate = 0x7f0b000c
com.example.uhf:interpolator/m3_sys_motion_easing_standard = 0x7f0b000b
com.example.uhf:interpolator/fast_out_slow_in = 0x7f0b0006
com.example.uhf:string/South_Africa_915_919MHz = 0x7f12001a
com.example.uhf:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0b0000
com.example.uhf:integer/show_password_duration = 0x7f0a0045
com.example.uhf:macro/m3_comp_filled_text_field_container_color = 0x7f0d004b
com.example.uhf:dimen/mtrl_card_corner_radius = 0x7f0702a2
com.example.uhf:id/open_search_view_edit_text = 0x7f0901ee
com.example.uhf:integer/preferences_detail_pane_weight = 0x7f0a0043
com.example.uhf:integer/mtrl_view_gone = 0x7f0a003f
com.example.uhf:macro/m3_comp_radio_button_selected_focus_icon_color = 0x7f0d00d8
com.example.uhf:integer/mtrl_switch_track_viewport_width = 0x7f0a003d
com.example.uhf:integer/mtrl_switch_thumb_viewport_size = 0x7f0a003b
com.example.uhf:integer/mtrl_switch_thumb_pressed_duration = 0x7f0a0039
com.example.uhf:color/m3_timepicker_display_text_color = 0x7f06022a
com.example.uhf:integer/mtrl_switch_thumb_motion_duration = 0x7f0a0036
com.example.uhf:integer/mtrl_chip_anim_duration = 0x7f0a0035
com.example.uhf:style/Base.Widget.AppCompat.PopupMenu = 0x7f1300ef
com.example.uhf:attr/titleTextAppearance = 0x7f0404df
com.example.uhf:integer/mtrl_calendar_year_selector_span = 0x7f0a0032
com.example.uhf:color/abc_color_highlight_material = 0x7f060004
com.example.uhf:drawable/ic_call_answer = 0x7f08009c
com.example.uhf:integer/mtrl_calendar_selection_text_lines = 0x7f0a0031
com.example.uhf:style/Base.Widget.AppCompat.DropDownItem.Spinner = 0x7f1300df
com.example.uhf:integer/mtrl_calendar_header_orientation = 0x7f0a0030
com.example.uhf:integer/mtrl_btn_anim_duration_ms = 0x7f0a002f
com.example.uhf:integer/mtrl_btn_anim_delay_ms = 0x7f0a002e
com.example.uhf:string/btErase = 0x7f1200b0
com.example.uhf:attr/cardUseCompatPadding = 0x7f0400a7
com.example.uhf:color/material_dynamic_neutral_variant60 = 0x7f060252
com.example.uhf:integer/material_motion_path = 0x7f0a002c
com.example.uhf:string/button_open_browser = 0x7f1200d5
com.example.uhf:string/b14443_title_init = 0x7f12006e
com.example.uhf:attr/actionModeBackground = 0x7f040012
com.example.uhf:string/up_msg_stop_time = 0x7f12049d
com.example.uhf:integer/material_motion_duration_short_1 = 0x7f0a002a
com.example.uhf:string/msg_google_books = 0x7f120250
com.example.uhf:integer/material_motion_duration_medium_1 = 0x7f0a0028
com.example.uhf:string/menu_encode_vcard = 0x7f120237
com.example.uhf:integer/m3_sys_shape_corner_medium_corner_family = 0x7f0a0024
com.example.uhf:id/TvTagUii = 0x7f09002b
com.example.uhf:integer/m3_sys_shape_corner_extra_large_corner_family = 0x7f0a0020
com.example.uhf:integer/m3_sys_motion_path = 0x7f0a001f
com.example.uhf:string/title_ping_count = 0x7f1203e0
com.example.uhf:string/rfid_title_continuous_read = 0x7f120358
com.example.uhf:integer/m3_sys_motion_duration_short4 = 0x7f0a001e
com.example.uhf:dimen/m3_comp_extended_fab_primary_hover_container_elevation = 0x7f070115
com.example.uhf:integer/m3_sys_motion_duration_short3 = 0x7f0a001d
com.example.uhf:string/preferences_copy_to_clipboard_title = 0x7f1202f2
com.example.uhf:string/fingerprint_title_page_id = 0x7f1201ae
com.example.uhf:integer/m3_sys_motion_duration_short1 = 0x7f0a001b
com.example.uhf:style/Widget.Material3.Button.TonalButton = 0x7f13038a
com.example.uhf:integer/m3_sys_motion_duration_medium4 = 0x7f0a001a
com.example.uhf:integer/m3_sys_motion_duration_medium3 = 0x7f0a0019
com.example.uhf:dimen/network_list_ssid_size = 0x7f070318
com.example.uhf:integer/m3_sys_motion_duration_long4 = 0x7f0a0016
com.example.uhf:id/flip = 0x7f090155
com.example.uhf:id/transitionToStart = 0x7f0902b4
com.example.uhf:integer/m3_sys_motion_duration_long3 = 0x7f0a0015
com.example.uhf:integer/m3_sys_motion_duration_long2 = 0x7f0a0014
com.example.uhf:attr/progressBarPadding = 0x7f0403bd
com.example.uhf:integer/m3_sys_motion_duration_extra_long2 = 0x7f0a0010
com.example.uhf:style/Widget.Material3.MaterialDivider = 0x7f1303db
com.example.uhf:string/uhf_msg_set_protocol_fail = 0x7f120469
com.example.uhf:string/preferences_result_title = 0x7f120303
com.example.uhf:id/middle = 0x7f0901b2
com.example.uhf:attr/startIconCheckable = 0x7f040426
com.example.uhf:integer/m3_card_anim_duration_ms = 0x7f0a000d
com.example.uhf:attr/badgeWithTextRadius = 0x7f040066
com.example.uhf:integer/m3_btn_anim_duration_ms = 0x7f0a000b
com.example.uhf:integer/hide_password_duration = 0x7f0a0008
com.example.uhf:integer/design_tab_indicator_anim_duration_ms = 0x7f0a0007
com.example.uhf:style/Base.MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f130017
com.example.uhf:integer/config_tooltipAnimTime = 0x7f0a0005
com.example.uhf:integer/cancel_button_image_alpha = 0x7f0a0004
com.example.uhf:integer/bottom_sheet_slide_duration = 0x7f0a0003
com.example.uhf:id/x_left = 0x7f0902e1
com.example.uhf:macro/m3_comp_navigation_rail_inactive_icon_color = 0x7f0d009c
com.example.uhf:string/msg_network_unavailable = 0x7f120267
com.example.uhf:macro/m3_comp_extended_fab_primary_icon_color = 0x7f0d002e
com.example.uhf:attr/layout_dodgeInsetEdges = 0x7f0402c7
com.example.uhf:id/spGBAction = 0x7f09026b
com.example.uhf:id/wrap_content = 0x7f0902df
com.example.uhf:id/wrap = 0x7f0902de
com.example.uhf:string/network_msg_wifi_conn_fail = 0x7f1202c7
com.example.uhf:id/with_icon = 0x7f0902dc
com.example.uhf:style/Base.Theme.MaterialComponents.Dialog.Bridge = 0x7f13006e
com.example.uhf:layout/support_simple_spinner_dropdown_item = 0x7f0c008d
com.example.uhf:id/etData = 0x7f090125
com.example.uhf:id/west = 0x7f0902da
com.example.uhf:dimen/material_emphasis_disabled = 0x7f070238
com.example.uhf:id/visible_removing_fragment_view_tag = 0x7f0902d9
com.example.uhf:id/visible = 0x7f0902d8
com.example.uhf:string/psam_title_clear = 0x7f120312
com.example.uhf:id/view_tree_view_model_store_owner = 0x7f0902d7
com.example.uhf:styleable/ForegroundLinearLayout = 0x7f14003e
com.example.uhf:id/view_tree_lifecycle_owner = 0x7f0902d4
com.example.uhf:id/view_transition = 0x7f0902d3
com.example.uhf:id/vertical_only = 0x7f0902d1
com.example.uhf:id/unchecked = 0x7f0902cc
com.example.uhf:id/tvUploadStats = 0x7f0902c9
com.example.uhf:id/tvSpeed = 0x7f0902c7
com.example.uhf:style/Widget.AppCompat.ProgressBar.Horizontal = 0x7f13034b
com.example.uhf:layout/listtag_items = 0x7f0c003e
com.example.uhf:color/m3_sys_color_dynamic_light_primary = 0x7f0601cd
com.example.uhf:id/tvLastUpload = 0x7f0902c3
com.example.uhf:id/tvDeviceId = 0x7f0902c2
com.example.uhf:style/Widget.AppCompat.Light.ActionBar.TabView.Inverse = 0x7f130334
com.example.uhf:string/msg_login_error = 0x7f12025c
com.example.uhf:dimen/mtrl_low_ripple_default_alpha = 0x7f0702c3
com.example.uhf:string/msg_sbc_book_not_searchable = 0x7f120272
com.example.uhf:id/tvContinuous = 0x7f0902c0
com.example.uhf:color/switch_thumb_disabled_material_dark = 0x7f060339
com.example.uhf:id/transition_transform = 0x7f0902bc
com.example.uhf:style/Theme.MaterialComponents.Light = 0x7f13028d
com.example.uhf:id/transition_scene_layoutid_cache = 0x7f0902bb
com.example.uhf:style/Theme.MaterialComponents.DayNight.NoActionBar = 0x7f130282
com.example.uhf:id/transition_position = 0x7f0902ba
com.example.uhf:id/transition_layout_save = 0x7f0902b8
com.example.uhf:id/transition_image_transform = 0x7f0902b7
com.example.uhf:style/Base.TextAppearance.AppCompat.Widget.DropDownItem = 0x7f130041
com.example.uhf:id/transition_clip = 0x7f0902b5
com.example.uhf:string/uhf_title_link = 0x7f120484
com.example.uhf:id/top = 0x7f0902b0
com.example.uhf:id/parentRelative = 0x7f0901fd
com.example.uhf:string/export = 0x7f120169
com.example.uhf:id/titleDividerNoCustom = 0x7f0902ad
com.example.uhf:style/Widget.Material3.CheckedTextView = 0x7f130390
com.example.uhf:id/textinput_suffix_text = 0x7f0902aa
com.example.uhf:macro/m3_comp_date_picker_modal_container_color = 0x7f0d000d
com.example.uhf:id/textinput_helper_text = 0x7f0902a7
com.example.uhf:id/textinput_counter = 0x7f0902a5
com.example.uhf:id/text_input_end_icon = 0x7f0902a2
com.example.uhf:string/uhf_btn_setpower = 0x7f12043c
com.example.uhf:drawable/button_bg3 = 0x7f080086
com.example.uhf:id/textTop = 0x7f0902a1
com.example.uhf:id/textStart = 0x7f0902a0
com.example.uhf:id/textSpacerNoTitle = 0x7f09029f
com.example.uhf:id/tabMode = 0x7f09028d
com.example.uhf:string/title_activity_iso14443_a4_cpu = 0x7f1203b2
com.example.uhf:style/Widget.Material3.MaterialTimePicker.ImageButton = 0x7f1303e5
com.example.uhf:id/textSpacerNoButtons = 0x7f09029e
com.example.uhf:dimen/m3_comp_outlined_card_container_elevation = 0x7f070155
com.example.uhf:id/textEnd = 0x7f09029d
com.example.uhf:id/text = 0x7f09029b
com.example.uhf:style/Animation.Design.BottomSheetDialog = 0x7f130007
com.example.uhf:id/tag_unhandled_key_event_manager = 0x7f090298
com.example.uhf:id/tag_on_receive_content_mime_types = 0x7f090294
com.example.uhf:id/tag_accessibility_pane_title = 0x7f090291
com.example.uhf:id/tag_accessibility_clickable_spans = 0x7f09028f
com.example.uhf:id/tag_accessibility_actions = 0x7f09028e
com.example.uhf:style/RtlOverlay.Widget.AppCompat.PopupMenuItem = 0x7f130172
com.example.uhf:dimen/m3_badge_with_text_vertical_padding = 0x7f0700c0
com.example.uhf:drawable/abc_btn_switch_to_on_mtrl_00012 = 0x7f080037
com.example.uhf:string/title_about_android_release = 0x7f120396
com.example.uhf:string/none_paired = 0x7f1202da
com.example.uhf:id/switchWidget = 0x7f09028c
com.example.uhf:dimen/mtrl_calendar_header_height_fullscreen = 0x7f070287
com.example.uhf:string/title_decode_time = 0x7f1203d1
com.example.uhf:id/submenuarrow = 0x7f090289
com.example.uhf:string/searchRange = 0x7f120362
com.example.uhf:id/stretch = 0x7f090288
com.example.uhf:id/stop = 0x7f090287
com.example.uhf:id/staticLayout = 0x7f090285
com.example.uhf:color/m3_sys_color_dark_surface_container_lowest = 0x7f060193
com.example.uhf:id/start = 0x7f090281
com.example.uhf:id/src_over = 0x7f09027f
com.example.uhf:id/src_in = 0x7f09027e
com.example.uhf:id/src_atop = 0x7f09027d
com.example.uhf:style/Widget.Design.CollapsingToolbar = 0x7f130360
com.example.uhf:string/button_ok = 0x7f1200d4
com.example.uhf:id/spring = 0x7f09027b
com.example.uhf:id/spread_inside = 0x7f09027a
com.example.uhf:style/TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f1301e9
com.example.uhf:id/spread = 0x7f090279
com.example.uhf:styleable/MotionHelper = 0x7f14006b
com.example.uhf:attr/splitRatio = 0x7f04041c
com.example.uhf:string/b14443_msg_send_err = 0x7f120068
com.example.uhf:id/split_action_bar = 0x7f090278
com.example.uhf:style/Widget.MaterialComponents.TextView = 0x7f130481
com.example.uhf:string/ping_msg_pinging = 0x7f1202e9
com.example.uhf:string/fingerprint_msg_clear_succ = 0x7f12018c
com.example.uhf:anim/btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013
com.example.uhf:id/spinner = 0x7f090275
com.example.uhf:integer/m3_sys_shape_corner_full_corner_family = 0x7f0a0022
com.example.uhf:style/Widget.Design.FloatingActionButton = 0x7f130361
com.example.uhf:dimen/abc_edit_text_inset_horizontal_material = 0x7f07002d
com.example.uhf:id/spSessionID = 0x7f090272
com.example.uhf:style/Base.Widget.AppCompat.Light.ActionBar.TabText = 0x7f1300e5
com.example.uhf:id/etLen = 0x7f09012e
com.example.uhf:id/spMemoryBank = 0x7f090270
com.example.uhf:id/spInventoried = 0x7f09026f
com.example.uhf:id/spGBUserAreaNumber = 0x7f09026e
com.example.uhf:attr/spinBars = 0x7f040416
com.example.uhf:id/spGBStorageArea = 0x7f09026d
com.example.uhf:id/spGBConfig = 0x7f09026c
com.example.uhf:id/spFreHop = 0x7f090269
com.example.uhf:id/snackbar_text = 0x7f090265
com.example.uhf:id/snackbar_action = 0x7f090264
com.example.uhf:id/skipCollapsed = 0x7f090261
com.example.uhf:attr/motionDurationExtraLong3 = 0x7f040344
com.example.uhf:style/Base.V21.Theme.MaterialComponents.Dialog = 0x7f1300ab
com.example.uhf:id/shortcut = 0x7f09025c
com.example.uhf:style/Widget.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f13036e
com.example.uhf:id/sharedValueUnset = 0x7f09025b
com.example.uhf:style/Base.Animation.AppCompat.Dialog = 0x7f130011
com.example.uhf:style/Widget.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f130476
com.example.uhf:attr/drawerLayoutStyle = 0x7f0401a5
com.example.uhf:id/selection_type = 0x7f090259
com.example.uhf:id/select_dialog_listview = 0x7f090257
com.example.uhf:id/seekbar = 0x7f090255
com.example.uhf:color/mtrl_navigation_bar_item_tint = 0x7f0602f0
com.example.uhf:id/seekBarPower = 0x7f090254
com.example.uhf:color/m3_slider_thumb_color = 0x7f060172
com.example.uhf:id/search_voice_btn = 0x7f090253
com.example.uhf:string/msg_free_fail = 0x7f12024e
com.example.uhf:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0b0002
com.example.uhf:macro/m3_comp_primary_navigation_tab_with_label_text_label_text_type = 0x7f0d00d3
com.example.uhf:id/search_button = 0x7f09024c
com.example.uhf:id/scrollIndicatorDown = 0x7f090246
com.example.uhf:style/ShapeAppearance.M3.Sys.Shape.Corner.None = 0x7f130197
com.example.uhf:id/scroll = 0x7f090245
com.example.uhf:id/scale = 0x7f090243
com.example.uhf:id/scrollView = 0x7f090248
com.example.uhf:drawable/btn_checkbox_unchecked_mtrl = 0x7f08007e
com.example.uhf:id/rounded = 0x7f09023d
com.example.uhf:color/material_personalized_color_on_tertiary = 0x7f0602a6
com.example.uhf:string/bd_title_Altitude = 0x7f12008b
com.example.uhf:attr/submitBackground = 0x7f040440
com.example.uhf:string/fingerprint_msg_sure_clear = 0x7f12019e
com.example.uhf:id/right_side = 0x7f09023c
com.example.uhf:id/right_icon = 0x7f09023b
com.example.uhf:id/east = 0x7f090118
com.example.uhf:id/rightToLeft = 0x7f09023a
com.example.uhf:id/rgFileType = 0x7f090238
com.example.uhf:id/return_scan_result = 0x7f090236
com.example.uhf:array/arrayCheck = 0x7f03000c
com.example.uhf:id/recycler_view = 0x7f090233
com.example.uhf:id/rectangles = 0x7f090232
com.example.uhf:id/tag_transition_group = 0x7f090297
com.example.uhf:attr/key = 0x7f040285
com.example.uhf:id/rb_uhf_module = 0x7f090230
com.example.uhf:id/rb_ex10 = 0x7f09022f
com.example.uhf:style/ThemeOverlay.Material3.DynamicColors.DayNight = 0x7f1302c7
com.example.uhf:id/rb_china = 0x7f09022e
com.example.uhf:string/m3_ref_typeface_brand_regular = 0x7f120212
com.example.uhf:id/rb_Europe = 0x7f09022c
com.example.uhf:style/Base.Theme.AppCompat.Light = 0x7f130056
com.example.uhf:id/rb_America = 0x7f09022b
com.example.uhf:id/rbUser_filter_perm = 0x7f090228
com.example.uhf:id/tag_screen_reader_focusable = 0x7f090295
com.example.uhf:id/accessibility_custom_action_16 = 0x7f09003b
com.example.uhf:id/rbUser_filter_lock = 0x7f090227
com.example.uhf:string/mtrl_switch_thumb_path_unchecked = 0x7f1202b9
com.example.uhf:color/abc_search_url_text_selected = 0x7f060010
com.example.uhf:id/rbUser_filter = 0x7f090225
com.example.uhf:attr/backgroundTintMode = 0x7f040058
com.example.uhf:anim/abc_tooltip_enter = 0x7f01000a
com.example.uhf:id/rbTID_light_filter = 0x7f090223
com.example.uhf:id/rbTID_filter_wt = 0x7f090222
com.example.uhf:string/btDel_Select = 0x7f1200ae
com.example.uhf:id/rbTID_filter_perm = 0x7f090221
com.example.uhf:id/rbTID_filter_lock = 0x7f090220
com.example.uhf:macro/m3_comp_navigation_bar_label_text_type = 0x7f0d0077
com.example.uhf:macro/m3_comp_navigation_bar_active_label_text_color = 0x7f0d0067
com.example.uhf:id/rbTID_filter_deact = 0x7f09021f
com.example.uhf:dimen/mtrl_calendar_action_height = 0x7f070277
com.example.uhf:id/rbTID_filter = 0x7f09021e
com.example.uhf:color/material_dynamic_secondary50 = 0x7f06026b
com.example.uhf:style/Base.V24.Theme.Material3.Dark = 0x7f1300b6
com.example.uhf:attr/tickMark = 0x7f0404cb
com.example.uhf:id/rbTID = 0x7f09021d
com.example.uhf:styleable/AppCompatTextHelper = 0x7f140012
com.example.uhf:attr/disableDependentsState = 0x7f04018c
com.example.uhf:string/msg_copy_clipboard = 0x7f120241
com.example.uhf:id/rbOpen = 0x7f09021b
com.example.uhf:id/sawtooth = 0x7f090242
com.example.uhf:dimen/mtrl_progress_circular_size = 0x7f0702de
com.example.uhf:id/rbLock = 0x7f09021a
com.example.uhf:string/setQTParams_succ = 0x7f12036e
com.example.uhf:drawable/m3_popupmenu_background_overlay = 0x7f0800b7
com.example.uhf:id/rbFastInventoryClose = 0x7f090218
com.example.uhf:id/radio = 0x7f09020f
com.example.uhf:string/uhf_msg_no_Sensor = 0x7f120454
com.example.uhf:id/progress_circular = 0x7f09020b
com.example.uhf:string/network_msg_wifi_speed = 0x7f1202cb
com.example.uhf:id/view_tree_on_back_pressed_dispatcher_owner = 0x7f0902d5
com.example.uhf:id/preferences_sliding_pane_layout = 0x7f090209
com.example.uhf:style/Widget.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f130488
com.example.uhf:id/preferences_detail = 0x7f090207
com.example.uhf:layout/mtrl_layout_snackbar_include = 0x7f0c0064
com.example.uhf:id/position = 0x7f090205
com.example.uhf:drawable/abc_ic_menu_selectall_mtrl_alpha = 0x7f080047
com.example.uhf:dimen/m3_comp_radio_button_selected_pressed_state_layer_opacity = 0x7f07016e
com.example.uhf:id/pin = 0x7f090204
com.example.uhf:id/pathRelative = 0x7f090201
com.example.uhf:attr/topInsetScrimEnabled = 0x7f0404ec
com.example.uhf:string/ul_title_AFI = 0x7f12048f
com.example.uhf:attr/dropdownListPreferredItemHeight = 0x7f0401a8
com.example.uhf:drawable/abc_ic_menu_overflow_material = 0x7f080045
com.example.uhf:id/report_drawn = 0x7f090234
com.example.uhf:color/m3_sys_color_dynamic_dark_surface_bright = 0x7f0601b0
com.example.uhf:id/password_toggle = 0x7f0901ff
com.example.uhf:id/parent_matrix = 0x7f0901fe
com.example.uhf:id/slide = 0x7f090263
com.example.uhf:styleable/ActivityRule = 0x7f140007
com.example.uhf:color/design_fab_stroke_top_inner_color = 0x7f060058
com.example.uhf:id/parent = 0x7f0901fb
com.example.uhf:id/parallax = 0x7f0901fa
com.example.uhf:macro/m3_comp_fab_primary_container_color = 0x7f0d0036
com.example.uhf:macro/m3_comp_date_picker_modal_date_unselected_label_text_color = 0x7f0d0014
com.example.uhf:attr/motionDurationLong2 = 0x7f040347
com.example.uhf:layout/abc_action_menu_item_layout = 0x7f0c0002
com.example.uhf:id/outline = 0x7f0901f6
com.example.uhf:id/open_search_view_toolbar_container = 0x7f0901f5
com.example.uhf:id/open_search_view_search_prefix = 0x7f0901f2
com.example.uhf:styleable/RadialViewGroup = 0x7f140081
com.example.uhf:id/open_search_view_root = 0x7f0901f0
com.example.uhf:attr/searchViewStyle = 0x7f0403e1
com.example.uhf:string/contents_email = 0x7f1200fc
com.example.uhf:string/path_password_eye = 0x7f1202e1
com.example.uhf:id/open_search_view_header_container = 0x7f0901ef
com.example.uhf:id/open_search_view_dummy_toolbar = 0x7f0901ed
com.example.uhf:id/open_search_view_clear_button = 0x7f0901ea
com.example.uhf:styleable/ActivityFilter = 0x7f140006
com.example.uhf:macro/m3_comp_assist_chip_container_shape = 0x7f0d0000
com.example.uhf:id/open_search_view_background = 0x7f0901e9
com.example.uhf:string/lp_title_b = 0x7f1201fd
com.example.uhf:id/open_search_bar_text_view = 0x7f0901e8
com.example.uhf:id/on = 0x7f0901e6
com.example.uhf:integer/abc_config_activityShortDur = 0x7f0a0001
com.example.uhf:id/off = 0x7f0901e5
com.example.uhf:id/notification_main_column_container = 0x7f0901e4
com.example.uhf:macro/m3_comp_switch_unselected_focus_icon_color = 0x7f0d0130
com.example.uhf:macro/m3_comp_fab_secondary_container_color = 0x7f0d003b
com.example.uhf:id/neverCompleteToStart = 0x7f0901dc
com.example.uhf:style/Base.ThemeOverlay.Material3.Dialog = 0x7f130086
com.example.uhf:string/m1_title_tag_type = 0x7f12020e
com.example.uhf:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Surface = 0x7f1302cb
com.example.uhf:id/btStart = 0x7f09008e
com.example.uhf:id/navigation_bar_item_small_label_view = 0x7f0901d8
com.example.uhf:attr/dividerColor = 0x7f04018f
com.example.uhf:id/navigation_bar_item_icon_container = 0x7f0901d4
com.example.uhf:id/multiply = 0x7f0901d2
com.example.uhf:id/mtrl_view_tag_bottom_padding = 0x7f0901d1
com.example.uhf:attr/circularflow_radiusInDP = 0x7f0400e0
com.example.uhf:id/mtrl_picker_title_text = 0x7f0901d0
com.example.uhf:id/mtrl_picker_header = 0x7f0901c9
com.example.uhf:dimen/m3_back_progress_side_container_max_scale_y_distance = 0x7f0700b7
com.example.uhf:id/rbEPC_light_filter = 0x7f090217
com.example.uhf:id/mtrl_internal_children_alpha_tag = 0x7f0901c6
com.example.uhf:string/tvData_Read = 0x7f120402
com.example.uhf:id/mtrl_calendar_text_input_frame = 0x7f0901c2
com.example.uhf:id/mtrl_calendar_selection_frame = 0x7f0901c1
com.example.uhf:id/mtrl_calendar_main_pane = 0x7f0901bf
com.example.uhf:id/mtrl_calendar_day_selector_frame = 0x7f0901bc
com.example.uhf:id/month_title = 0x7f0901b9
com.example.uhf:dimen/notification_big_circle_margin = 0x7f07031c
com.example.uhf:id/month_navigation_next = 0x7f0901b7
com.example.uhf:style/Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f13033f
com.example.uhf:attr/chipSpacingVertical = 0x7f0400d4
com.example.uhf:color/m3_sys_color_dynamic_dark_on_tertiary = 0x7f0601a7
com.example.uhf:id/matrix = 0x7f0901b0
com.example.uhf:macro/m3_comp_filled_button_label_text_type = 0x7f0d0045
com.example.uhf:id/material_timepicker_view = 0x7f0901ae
com.example.uhf:style/Base.Widget.Material3.Chip = 0x7f130106
com.example.uhf:styleable/TextAppearance = 0x7f14009e
com.example.uhf:string/tvKHz = 0x7f12040c
com.example.uhf:string/button_search_book_contents = 0x7f1200d8
com.example.uhf:attr/switchTextOn = 0x7f040454
com.example.uhf:id/material_minute_tv = 0x7f0901a8
com.example.uhf:mipmap/ic_launcher_round = 0x7f0f0001
com.example.uhf:id/noScroll = 0x7f0901dd
com.example.uhf:string/fingerprint_title_model_num = 0x7f1201ab
com.example.uhf:id/material_label = 0x7f0901a6
com.example.uhf:style/Widget.Material3.ActionMode = 0x7f130369
com.example.uhf:macro/m3_comp_navigation_bar_active_pressed_icon_color = 0x7f0d0068
com.example.uhf:id/material_hour_tv = 0x7f0901a5
com.example.uhf:layout/mtrl_calendar_days_of_week = 0x7f0c005b
com.example.uhf:id/material_hour_text_input = 0x7f0901a4
com.example.uhf:id/material_clock_period_pm_button = 0x7f0901a2
com.example.uhf:attr/motionEasingStandardDecelerateInterpolator = 0x7f04035c
com.example.uhf:id/material_clock_level = 0x7f0901a0
com.example.uhf:id/material_clock_hand = 0x7f09019f
com.example.uhf:color/dim_foreground_material_dark = 0x7f06005e
com.example.uhf:id/material_clock_face = 0x7f09019e
com.example.uhf:id/material_clock_display_and_toggle = 0x7f09019d
com.example.uhf:id/material_clock_display = 0x7f09019c
com.example.uhf:string/nfc_title_writer = 0x7f1202d8
com.example.uhf:dimen/abc_dialog_fixed_height_major = 0x7f07001c
com.example.uhf:id/match_parent = 0x7f09019b
com.example.uhf:string/uhf_msg_addr_not_null = 0x7f120443
com.example.uhf:id/match_constraint = 0x7f09019a
com.example.uhf:id/masked = 0x7f090199
com.example.uhf:id/marquee = 0x7f090197
com.example.uhf:id/ltr = 0x7f090195
com.example.uhf:id/locale = 0x7f090194
com.example.uhf:dimen/tooltip_margin = 0x7f07034f
com.example.uhf:id/ll_freHop = 0x7f090193
com.example.uhf:id/llMemoryBankParams = 0x7f090191
com.example.uhf:string/rfid_title_continuous = 0x7f120356
com.example.uhf:id/llFilter = 0x7f090190
com.example.uhf:macro/m3_comp_navigation_rail_container_color = 0x7f0d0099
com.example.uhf:id/llContinuous = 0x7f09018f
com.example.uhf:string/EPC_TID = 0x7f120005
com.example.uhf:id/llChart = 0x7f09018e
com.example.uhf:id/list_item = 0x7f09018c
com.example.uhf:id/linear = 0x7f090189
com.example.uhf:string/psam_title_tips = 0x7f120319
com.example.uhf:string/OAUTH_RequestToken_ACCESS = 0x7f120014
com.example.uhf:id/line1 = 0x7f090187
com.example.uhf:id/legacy = 0x7f090186
com.example.uhf:string/rfid_msg_confirm_title = 0x7f120347
com.example.uhf:style/Widget.Material3.FloatingActionButton.Small.Secondary = 0x7f1303bb
com.example.uhf:id/left = 0x7f090184
com.example.uhf:style/Widget.AppCompat.TextView = 0x7f130357
com.example.uhf:id/triangle = 0x7f0902bd
com.example.uhf:id/layout_filter = 0x7f090183
com.example.uhf:style/Widget.Material3.TextInputLayout.FilledBox = 0x7f130407
com.example.uhf:drawable/material_ic_menu_arrow_down_black_24dp = 0x7f0800c6
com.example.uhf:layout/notification_template_part_time = 0x7f0c0077
com.example.uhf:id/labeled = 0x7f09017d
com.example.uhf:id/item_touch_helper_previous_elevation = 0x7f090179
com.example.uhf:style/Widget.MaterialComponents.ActionBar.Surface = 0x7f130416
com.example.uhf:style/Base.V28.Theme.AppCompat.Light = 0x7f1300be
com.example.uhf:style/Theme.Material3.DynamicColors.Dark = 0x7f130262
com.example.uhf:array/arrayVoltage = 0x7f03002a
com.example.uhf:id/open_search_view_content_container = 0x7f0901eb
com.example.uhf:layout/select_dialog_singlechoice_material = 0x7f0c008c
com.example.uhf:id/italic = 0x7f090178
com.example.uhf:color/material_dynamic_color_dark_error_container = 0x7f060237
com.example.uhf:id/inward = 0x7f090177
com.example.uhf:id/info = 0x7f090175
com.example.uhf:macro/m3_comp_navigation_bar_active_hover_label_text_color = 0x7f0d0063
com.example.uhf:string/bluetooth_btn_no = 0x7f120097
com.example.uhf:id/immediateStop = 0x7f090172
com.example.uhf:id/image = 0x7f090171
com.example.uhf:id/icon = 0x7f09016b
com.example.uhf:id/honorRequest = 0x7f090169
com.example.uhf:id/home = 0x7f090167
com.example.uhf:string/bottomsheet_action_collapse = 0x7f1200a0
com.example.uhf:id/header_title = 0x7f090164
com.example.uhf:id/hScroller_mytabhostactivity = 0x7f090163
com.example.uhf:color/material_personalized_primary_text_disable_only = 0x7f0602c7
com.example.uhf:id/grouping = 0x7f090161
com.example.uhf:id/group_divider = 0x7f090160
com.example.uhf:id/graph_wrap = 0x7f09015f
com.example.uhf:style/Widget.MaterialComponents.MaterialCalendar.Year.Selected = 0x7f13045e
com.example.uhf:id/fixed = 0x7f090154
com.example.uhf:string/uhf_msg_tab_light = 0x7f12046f
com.example.uhf:id/fitStart = 0x7f090151
com.example.uhf:attr/autoSizePresetSizes = 0x7f040049
com.example.uhf:dimen/design_navigation_max_width = 0x7f07007d
com.example.uhf:id/fitCenter = 0x7f09014f
com.example.uhf:styleable/LinearLayoutCompat_Layout = 0x7f14004f
com.example.uhf:styleable/KeyAttribute = 0x7f140045
com.example.uhf:id/filled = 0x7f09014e
com.example.uhf:id/fill_vertical = 0x7f09014d
com.example.uhf:dimen/material_time_picker_minimum_screen_width = 0x7f07024a
com.example.uhf:id/export = 0x7f090149
com.example.uhf:string/desfire_title_app_id = 0x7f12011c
com.example.uhf:style/Widget.MaterialComponents.CircularProgressIndicator.ExtraSmall = 0x7f13043c
com.example.uhf:id/expand_activities_button = 0x7f090147
com.example.uhf:dimen/preference_seekbar_padding_horizontal = 0x7f07032b
com.example.uhf:id/exitUntilCollapsed = 0x7f090146
com.example.uhf:attr/chipStyle = 0x7f0400d9
com.example.uhf:string/uhf_msg_scaning = 0x7f12045e
com.example.uhf:string/rbErase = 0x7f12031c
com.example.uhf:dimen/design_bottom_navigation_label_padding = 0x7f070069
com.example.uhf:id/et_file = 0x7f090145
com.example.uhf:attr/colorOnPrimary = 0x7f04010b
com.example.uhf:id/etRadarEPC = 0x7f090141
com.example.uhf:string/tvMode = 0x7f120416
com.example.uhf:id/etPtr_filter_lock = 0x7f09013d
com.example.uhf:id/etPtr_filter = 0x7f09013b
com.example.uhf:integer/m3_sys_motion_duration_medium1 = 0x7f0a0017
com.example.uhf:dimen/mtrl_btn_dialog_btn_min_width = 0x7f07025f
com.example.uhf:id/etOffset = 0x7f090139
com.example.uhf:id/etMSA = 0x7f090138
com.example.uhf:color/m3_sys_color_light_surface_container = 0x7f060201
com.example.uhf:id/etLen_filter_wt = 0x7f090133
com.example.uhf:attr/alertDialogTheme = 0x7f04002f
com.example.uhf:id/etLen_filter_perm = 0x7f090132
com.example.uhf:string/uhf_title_temp = 0x7f12048a
com.example.uhf:string/title_activity_uhfset = 0x7f1203c8
com.example.uhf:string/preferences_vibrate_title = 0x7f12030a
com.example.uhf:id/etGBAccessPwd = 0x7f09012d
com.example.uhf:color/m3_ref_palette_error100 = 0x7f060113
com.example.uhf:layout/mtrl_search_view = 0x7f0c0071
com.example.uhf:id/etEPC = 0x7f09012c
com.example.uhf:string/title_activity_infrared = 0x7f1203b1
com.example.uhf:id/etData_filter_wt = 0x7f09012a
com.example.uhf:id/etData_filter_perm = 0x7f090129
com.example.uhf:style/Widget.Material3.MaterialCalendar.HeaderCancelButton = 0x7f1303cc
com.example.uhf:id/etData_filter_lock = 0x7f090128
com.example.uhf:id/etData_filter = 0x7f090126
com.example.uhf:string/lp_title_sensor = 0x7f120206
com.example.uhf:id/etApiKey = 0x7f090124
com.example.uhf:id/enterAlwaysCollapsed = 0x7f090121
com.example.uhf:id/ghost_view = 0x7f09015b
com.example.uhf:id/endToStart = 0x7f09011f
com.example.uhf:attr/textAppearanceHeadline5 = 0x7f040485
com.example.uhf:id/edit_text_id = 0x7f09011b
com.example.uhf:attr/fontProviderFetchTimeout = 0x7f04021e
com.example.uhf:color/m3_ref_palette_dynamic_neutral87 = 0x7f0600ca
com.example.uhf:id/edit_query = 0x7f09011a
com.example.uhf:id/easeInOut = 0x7f090116
com.example.uhf:id/dropdown_menu = 0x7f090114
com.example.uhf:string/title_activity_uhfmain = 0x7f1203c7
com.example.uhf:id/dragLeft = 0x7f090110
com.example.uhf:dimen/mtrl_calendar_header_text_padding = 0x7f070289
com.example.uhf:id/dragEnd = 0x7f09010f
com.example.uhf:style/TextAppearance.AppCompat.Headline = 0x7f1301c7
com.example.uhf:dimen/m3_chip_disabled_translation_z = 0x7f0700fa
com.example.uhf:id/disjoint = 0x7f09010b
com.example.uhf:attr/singleChoiceItemLayout = 0x7f04040c
com.example.uhf:dimen/m3_sys_elevation_level3 = 0x7f0701f8
com.example.uhf:string/uhf_btn_get_link = 0x7f120436
com.example.uhf:attr/state_liftable = 0x7f040433
com.example.uhf:id/dragUp = 0x7f090113
com.example.uhf:id/disableScroll = 0x7f09010a
com.example.uhf:id/disableIntraAutoTransition = 0x7f090108
com.example.uhf:id/design_menu_item_action_area_stub = 0x7f090101
com.example.uhf:string/desfire_title_pwd_pro_chg = 0x7f120136
com.example.uhf:string/yid_title_scan_continuous = 0x7f1204c1
com.example.uhf:id/design_menu_item_action_area = 0x7f090100
com.example.uhf:string/uhf_located_angle = 0x7f12043f
com.example.uhf:id/dependency_ordering = 0x7f0900fe
com.example.uhf:id/deltaRelative = 0x7f0900fd
com.example.uhf:id/decelerateAndComplete = 0x7f0900f7
com.example.uhf:id/date_picker_actions = 0x7f0900f5
com.example.uhf:id/custom = 0x7f0900f2
com.example.uhf:attr/actionBarSize = 0x7f040005
com.example.uhf:id/currentState = 0x7f0900f1
com.example.uhf:macro/m3_comp_time_picker_time_selector_selected_pressed_state_layer_color = 0x7f0d0164
com.example.uhf:layout/design_navigation_item_subheader = 0x7f0c0029
com.example.uhf:id/counterclockwise = 0x7f0900ef
com.example.uhf:id/cos = 0x7f0900ee
com.example.uhf:string/bottomsheet_action_expand_halfway = 0x7f1200a2
com.example.uhf:id/contiguous = 0x7f0900eb
com.example.uhf:string/font_size = 0x7f1201ba
com.example.uhf:string/app_msg_exit_confirm = 0x7f12005a
com.example.uhf:color/material_grey_50 = 0x7f060281
com.example.uhf:id/contentPanel = 0x7f0900ea
com.example.uhf:string/setting_fail = 0x7f12036f
com.example.uhf:attr/textAppearanceCaption = 0x7f04047d
com.example.uhf:color/design_dark_default_color_on_background = 0x7f06003a
com.example.uhf:id/mtrl_picker_header_selection_text = 0x7f0901ca
com.example.uhf:attr/floatingActionButtonLargeTertiaryStyle = 0x7f0401fb
com.example.uhf:id/constraint = 0x7f0900e7
com.example.uhf:id/confirm_button = 0x7f0900e6
com.example.uhf:string/btCalRange = 0x7f1200a9
com.example.uhf:color/abc_primary_text_material_light = 0x7f06000c
com.example.uhf:id/compress = 0x7f0900e5
com.example.uhf:style/Widget.Material3.FloatingActionButton.Small.Surface = 0x7f1303bc
com.example.uhf:id/clip_horizontal = 0x7f0900e0
com.example.uhf:id/clear_text = 0x7f0900df
com.example.uhf:dimen/mtrl_alert_dialog_background_inset_start = 0x7f07024d
com.example.uhf:string/update_msg_unmount = 0x7f1204a4
com.example.uhf:attr/motionDurationExtraLong1 = 0x7f040342
com.example.uhf:id/checked = 0x7f0900dc
com.example.uhf:id/checkbox = 0x7f0900db
com.example.uhf:id/chain2 = 0x7f0900d9
com.example.uhf:string/title_ping_seconds = 0x7f1203e6
com.example.uhf:attr/actionModeShareDrawable = 0x7f04001c
com.example.uhf:id/chain = 0x7f0900d8
com.example.uhf:id/center = 0x7f0900d3
com.example.uhf:styleable/NavigationBarView = 0x7f140072
com.example.uhf:id/cb_light_filter = 0x7f0900d2
com.example.uhf:style/Theme.Material3.Light.NoActionBar = 0x7f13026e
com.example.uhf:id/progress_horizontal = 0x7f09020c
com.example.uhf:id/cb_filter_lock = 0x7f0900d0
com.example.uhf:drawable/abc_list_pressed_holo_dark = 0x7f080051
com.example.uhf:id/cb_filter = 0x7f0900cd
com.example.uhf:id/cbUser = 0x7f0900cc
com.example.uhf:id/cbPerm = 0x7f0900c8
com.example.uhf:id/cbEPC_Tam = 0x7f0900c4
com.example.uhf:style/Widget.Material3.Button.OutlinedButton.Icon = 0x7f130383
com.example.uhf:style/Platform.AppCompat.Light = 0x7f130143
com.example.uhf:id/cbEPC = 0x7f0900c3
com.example.uhf:string/desfire_msg_file_no_less_1 = 0x7f12010c
com.example.uhf:id/cbBlock8 = 0x7f0900c1
com.example.uhf:style/Widget.Material3.NavigationRailView.ActiveIndicator = 0x7f1303e7
com.example.uhf:style/ThemeOverlay.AppCompat.DayNight = 0x7f1302a4
com.example.uhf:id/maskbuf = 0x7f090198
com.example.uhf:string/preferences_general_title = 0x7f1202fe
com.example.uhf:id/elastic = 0x7f09011c
com.example.uhf:id/cbBlock6 = 0x7f0900bf
com.example.uhf:string/uhf_msg_set_pwm_fail = 0x7f12046b
com.example.uhf:id/cbBlock4 = 0x7f0900bd
com.example.uhf:id/cbBlock3 = 0x7f0900bc
com.example.uhf:id/cbBlock16 = 0x7f0900ba
com.example.uhf:attr/colorSurfaceContainerHigh = 0x7f04012d
com.example.uhf:id/cbBlock15 = 0x7f0900b9
com.example.uhf:dimen/mtrl_switch_thumb_elevation = 0x7f0702fc
com.example.uhf:id/cbBlock14 = 0x7f0900b8
com.example.uhf:style/ThemeOverlay.MaterialComponents.DayNight.BottomSheetDialog = 0x7f1302f8
com.example.uhf:style/Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f13008d
com.example.uhf:id/cbBlock12 = 0x7f0900b6
com.example.uhf:dimen/m3_comp_outlined_card_icon_size = 0x7f070157
com.example.uhf:id/standard = 0x7f090280
com.example.uhf:id/cbBlock10 = 0x7f0900b4
com.example.uhf:id/cbTid = 0x7f0900cb
com.example.uhf:dimen/m3_sys_motion_easing_standard_control_x2 = 0x7f070218
com.example.uhf:id/cbBlock1 = 0x7f0900b3
com.example.uhf:macro/m3_comp_switch_unselected_focus_track_outline_color = 0x7f0d0133
com.example.uhf:style/Widget.AppCompat.Light.ActionBar.Solid.Inverse = 0x7f13032e
com.example.uhf:id/cbAccess = 0x7f0900b1
com.example.uhf:id/carryVelocity = 0x7f0900b0
com.example.uhf:dimen/mtrl_textinput_start_icon_margin_end = 0x7f070309
com.example.uhf:id/cancel_button = 0x7f0900af
com.example.uhf:style/Widget.Material3.MaterialCalendar.HeaderToggleButton = 0x7f1303d3
com.example.uhf:integer/status_bar_notification_info_maxnum = 0x7f0a0046
com.example.uhf:attr/viewInflaterClass = 0x7f040511
com.example.uhf:id/btn_light_single = 0x7f0900ab
com.example.uhf:string/ap_title_ssid = 0x7f120054
com.example.uhf:id/btn_light_continuous = 0x7f0900aa
com.example.uhf:attr/collapsingToolbarLayoutMediumSize = 0x7f0400f9
com.example.uhf:attr/layout_constraintStart_toStartOf = 0x7f0402ba
com.example.uhf:macro/m3_comp_time_picker_time_selector_unselected_label_text_color = 0x7f0d016a
com.example.uhf:style/Widget.Support.CoordinatorLayout = 0x7f130491
com.example.uhf:attr/cornerSizeTopLeft = 0x7f04015f
com.example.uhf:drawable/abc_list_selector_disabled_holo_light = 0x7f080056
com.example.uhf:id/btnUpgrade = 0x7f0900a8
com.example.uhf:id/tv = 0x7f0902be
com.example.uhf:style/ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1302f5
com.example.uhf:id/btnTestConnection = 0x7f0900a7
com.example.uhf:style/TextAppearance.AppCompat.Display3 = 0x7f1301c5
com.example.uhf:id/btnSetPower = 0x7f0900a4
com.example.uhf:string/tvTagFocus = 0x7f120428
com.example.uhf:id/btnSetFrequency = 0x7f0900a1
com.example.uhf:attr/minSeparation = 0x7f040338
com.example.uhf:id/btnSetFreHop = 0x7f0900a0
com.example.uhf:id/btnSetFastInventory = 0x7f09009f
com.example.uhf:dimen/m3_badge_size = 0x7f0700ba
com.example.uhf:id/btnSaveSettings = 0x7f09009e
com.example.uhf:dimen/mtrl_textinput_box_corner_radius_medium = 0x7f070301
com.example.uhf:string/delete_tweet = 0x7f120106
com.example.uhf:id/btnOk = 0x7f09009d
com.example.uhf:id/btnOK = 0x7f09009c
com.example.uhf:id/btnKill = 0x7f09009a
com.example.uhf:id/btnGetPower = 0x7f090098
com.example.uhf:color/m3_ref_palette_secondary80 = 0x7f060159
com.example.uhf:id/btnGetMemoryBank = 0x7f090097
com.example.uhf:id/btnGetLinkParams = 0x7f090096
com.example.uhf:attr/badgeRadius = 0x7f04005b
com.example.uhf:dimen/notification_large_icon_width = 0x7f07031f
com.example.uhf:id/fitXY = 0x7f090153
com.example.uhf:style/Widget.MaterialComponents.AppBarLayout.Surface = 0x7f13041a
com.example.uhf:id/etTime = 0x7f090143
com.example.uhf:id/btStop = 0x7f09008f
com.example.uhf:id/btRadarStop = 0x7f09008c
com.example.uhf:id/btRadarStart = 0x7f09008b
com.example.uhf:styleable/ClockHandView = 0x7f140025
com.example.uhf:id/bounceStart = 0x7f090089
com.example.uhf:id/bounceBoth = 0x7f090087
com.example.uhf:id/bounce = 0x7f090086
com.example.uhf:string/msg_domain_ip_bad = 0x7f12024b
com.example.uhf:id/bottom = 0x7f090085
com.example.uhf:id/blocking = 0x7f090084
com.example.uhf:color/m3_sys_color_light_surface_variant = 0x7f060207
com.example.uhf:id/baseline = 0x7f090080
com.example.uhf:id/autoComplete = 0x7f09007c
com.example.uhf:string/uhf_btn_getLinkParams = 0x7f120435
com.example.uhf:string/result_sms = 0x7f12032c
com.example.uhf:drawable/abc_ratingbar_indicator_material = 0x7f08005b
com.example.uhf:id/auto = 0x7f09007b
com.example.uhf:style/Base.V28.Theme.AppCompat = 0x7f1300bd
com.example.uhf:id/async = 0x7f09007a
com.example.uhf:id/row_index_key = 0x7f09023e
com.example.uhf:id/asConfigured = 0x7f090079
com.example.uhf:id/layout12 = 0x7f090180
com.example.uhf:id/antiClockwise = 0x7f090076
com.example.uhf:string/msg_google_product = 0x7f120251
com.example.uhf:id/animateToStart = 0x7f090075
com.example.uhf:id/always = 0x7f090072
com.example.uhf:dimen/m3_comp_top_app_bar_large_container_height = 0x7f0701ad
com.example.uhf:attr/fabCustomSize = 0x7f0401ed
com.example.uhf:id/allStates = 0x7f090071
com.example.uhf:id/all = 0x7f090070
com.example.uhf:id/aligned = 0x7f09006f
com.example.uhf:id/alertTitle = 0x7f09006e
com.example.uhf:style/Widget.MaterialComponents.NavigationRailView.PrimarySurface = 0x7f130466
com.example.uhf:attr/searchHintIcon = 0x7f0403de
com.example.uhf:dimen/m3_comp_radio_button_unselected_focus_state_layer_opacity = 0x7f07016f
com.example.uhf:id/add = 0x7f09006d
com.example.uhf:dimen/mtrl_bottomappbar_fab_cradle_vertical_offset = 0x7f07025c
com.example.uhf:id/activity_chooser_view_content = 0x7f09006c
com.example.uhf:id/actions = 0x7f09006b
com.example.uhf:string/title_activity_analog_call = 0x7f1203a3
com.example.uhf:id/action_text = 0x7f09006a
com.example.uhf:color/help_button_view = 0x7f060072
com.example.uhf:id/action_mode_close_button = 0x7f090068
com.example.uhf:id/action_mode_bar = 0x7f090066
com.example.uhf:id/action_lock = 0x7f090063
com.example.uhf:drawable/abc_list_divider_mtrl_alpha = 0x7f08004e
com.example.uhf:id/action_kill = 0x7f090062
com.example.uhf:id/action_divider = 0x7f090060
com.example.uhf:dimen/abc_dialog_corner_radius_material = 0x7f07001b
com.example.uhf:string/m3_sys_motion_easing_legacy = 0x7f120219
com.example.uhf:id/cbBlock5 = 0x7f0900be
com.example.uhf:id/action_bar_title = 0x7f09005d
com.example.uhf:id/action_bar_subtitle = 0x7f09005c
com.example.uhf:id/action_bar_root = 0x7f09005a
com.example.uhf:attr/mock_labelColor = 0x7f04033e
com.example.uhf:id/action_bar_container = 0x7f090059
com.example.uhf:style/Base.AlertDialog.AppCompat.Light = 0x7f130010
com.example.uhf:id/action_bar_activity_content = 0x7f090058
com.example.uhf:id/actionUp = 0x7f090055
com.example.uhf:id/actionDownUp = 0x7f090054
com.example.uhf:string/title_activity_settings = 0x7f1203c3
com.example.uhf:string/fingerprint_title_packet_size = 0x7f1201ad
com.example.uhf:id/accessibility_custom_action_8 = 0x7f090051
com.example.uhf:id/accessibility_custom_action_7 = 0x7f090050
com.example.uhf:id/accessibility_custom_action_6 = 0x7f09004f
com.example.uhf:macro/m3_comp_outlined_text_field_hover_supporting_text_color = 0x7f0d00bf
com.example.uhf:attr/onShow = 0x7f040381
com.example.uhf:macro/m3_comp_fab_primary_small_container_shape = 0x7f0d003a
com.example.uhf:id/accessibility_custom_action_31 = 0x7f09004c
com.example.uhf:id/accessibility_custom_action_30 = 0x7f09004b
com.example.uhf:id/accessibility_custom_action_25 = 0x7f090045
com.example.uhf:string/mtrl_checkbox_button_icon_path_name = 0x7f120284
com.example.uhf:attr/checkedIconTint = 0x7f0400c3
com.example.uhf:id/cb_filter_2 = 0x7f0900ce
com.example.uhf:id/accessibility_custom_action_22 = 0x7f090042
com.example.uhf:attr/homeAsUpIndicator = 0x7f04023f
com.example.uhf:style/ShapeAppearance.M3.Comp.NavigationBar.ActiveIndicator.Shape = 0x7f130185
com.example.uhf:id/accessibility_custom_action_20 = 0x7f090040
com.example.uhf:id/text2 = 0x7f09029c
com.example.uhf:drawable/m3_tabs_line_indicator = 0x7f0800bb
com.example.uhf:style/Base.Widget.AppCompat.CompoundButton.RadioButton = 0x7f1300db
com.example.uhf:id/accessibility_custom_action_2 = 0x7f09003f
com.example.uhf:attr/colorOnErrorContainer = 0x7f04010a
com.example.uhf:id/accessibility_custom_action_19 = 0x7f09003e
com.example.uhf:attr/tabSelectedTextColor = 0x7f04046d
com.example.uhf:macro/m3_comp_switch_selected_pressed_handle_color = 0x7f0d012a
com.example.uhf:id/accessibility_custom_action_18 = 0x7f09003d
com.example.uhf:color/m3_tabs_text_color_secondary = 0x7f06021b
com.example.uhf:id/accessibility_custom_action_15 = 0x7f09003a
com.example.uhf:macro/m3_comp_date_picker_modal_header_supporting_text_type = 0x7f0d0018
com.example.uhf:id/accessibility_custom_action_12 = 0x7f090037
com.example.uhf:id/EtLen = 0x7f09000f
com.example.uhf:id/indeterminate = 0x7f090174
com.example.uhf:id/accessibility_custom_action_1 = 0x7f090034
com.example.uhf:color/m3_ref_palette_neutral87 = 0x7f06012e
com.example.uhf:string/title_ping_fail = 0x7f1203e2
com.example.uhf:style/Theme.Material3.Dark.SideSheetDialog = 0x7f130259
com.example.uhf:attr/showAnimationBehavior = 0x7f0403fb
com.example.uhf:id/accessibility_custom_action_0 = 0x7f090033
com.example.uhf:dimen/m3_extended_fab_icon_padding = 0x7f0701b6
com.example.uhf:id/anticipate = 0x7f090077
com.example.uhf:id/accelerate = 0x7f090031
com.example.uhf:id/_radarBackgroundView = 0x7f090030
com.example.uhf:style/ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton = 0x7f1301b9
com.example.uhf:id/_llSearchRange = 0x7f09002f
com.example.uhf:id/decode_failed = 0x7f0900f9
com.example.uhf:id/_labelPanelView = 0x7f09002e
com.example.uhf:id/_centerImage = 0x7f09002d
com.example.uhf:id/UHF_ver = 0x7f09002c
com.example.uhf:string/psam_title_card_2 = 0x7f120311
com.example.uhf:id/TOP_START = 0x7f090027
com.example.uhf:id/TOP_END = 0x7f090026
com.example.uhf:color/m3_button_foreground_color_selector = 0x7f060081
com.example.uhf:id/SpinnerBank_T = 0x7f090023
com.example.uhf:style/Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f130320
com.example.uhf:attr/floatingActionButtonSecondaryStyle = 0x7f0401fd
com.example.uhf:string/fingerprint_title_uname = 0x7f1201b7
com.example.uhf:macro/m3_comp_switch_disabled_unselected_track_color = 0x7f0d011e
com.example.uhf:style/TextAppearance.MaterialComponents.Subtitle2 = 0x7f130230
com.example.uhf:id/accessibility_custom_action_13 = 0x7f090038
com.example.uhf:id/SpinnerAgreement = 0x7f090021
com.example.uhf:id/SYM = 0x7f090020
com.example.uhf:attr/tickVisible = 0x7f0404d0
com.example.uhf:id/showCustom = 0x7f09025d
com.example.uhf:style/MaterialAlertDialog.Material3 = 0x7f130129
com.example.uhf:id/SHOW_PROGRESS = 0x7f09001f
com.example.uhf:id/RbInventorySingle = 0x7f09001a
com.example.uhf:macro/m3_comp_elevated_card_container_shape = 0x7f0d002b
com.example.uhf:attr/customStringValue = 0x7f040175
com.example.uhf:id/RbInventoryLoop = 0x7f090019
com.example.uhf:string/result_text = 0x7f12032e
com.example.uhf:id/EtRange = 0x7f090013
com.example.uhf:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f13004e
com.example.uhf:id/EtPtr = 0x7f090011
com.example.uhf:color/sbc_header_view = 0x7f060328
com.example.uhf:id/EtData_Write = 0x7f09000e
com.example.uhf:id/EtData = 0x7f09000d
com.example.uhf:string/abc_shareactionprovider_share_with_application = 0x7f12003a
com.example.uhf:attr/motionEasingStandardAccelerateInterpolator = 0x7f04035b
com.example.uhf:color/m3_sys_color_on_tertiary_fixed = 0x7f06020e
com.example.uhf:dimen/material_clock_face_margin_bottom = 0x7f07022a
com.example.uhf:id/EtAccessPwd_deactivate = 0x7f09000c
com.example.uhf:string/m3_ref_typeface_plain_regular = 0x7f120214
com.example.uhf:id/EtAccessPwd = 0x7f090009
com.example.uhf:id/BtWrite = 0x7f090007
com.example.uhf:id/BtUpload = 0x7f090006
com.example.uhf:id/BtRead = 0x7f090005
com.example.uhf:id/BOTTOM_START = 0x7f090002
com.example.uhf:id/ALT = 0x7f090000
com.example.uhf:drawable/video = 0x7f08010c
com.example.uhf:string/btSetProtocol = 0x7f1200be
com.example.uhf:drawable/tooltip_frame_light = 0x7f080109
com.example.uhf:style/Widget.Material3.MaterialCalendar.Year.Today = 0x7f1303d9
com.example.uhf:string/uhf_btn_get_workwait = 0x7f120437
com.example.uhf:dimen/m3_small_fab_max_image_size = 0x7f0701f1
com.example.uhf:drawable/text = 0x7f080107
com.example.uhf:string/myMenu = 0x7f1202be
com.example.uhf:drawable/shape_seekbar_circle = 0x7f080105
com.example.uhf:attr/colorSurfaceContainerLow = 0x7f04012f
com.example.uhf:drawable/rectangle_bg = 0x7f080102
com.example.uhf:dimen/network_list_proxy_size = 0x7f070316
com.example.uhf:drawable/abc_star_half_black_48dp = 0x7f080069
com.example.uhf:drawable/pop_menu_bg = 0x7f0800ff
com.example.uhf:attr/tabIconTintMode = 0x7f040459
com.example.uhf:drawable/phone = 0x7f0800fe
com.example.uhf:drawable/notify_panel_notification_icon_bg = 0x7f0800fb
com.example.uhf:macro/m3_comp_date_picker_modal_date_selected_container_color = 0x7f0d0010
com.example.uhf:drawable/notification_tile_bg = 0x7f0800fa
com.example.uhf:macro/m3_comp_navigation_drawer_headline_color = 0x7f0d0084
com.example.uhf:id/cbBlock2 = 0x7f0900bb
com.example.uhf:drawable/notification_template_icon_bg = 0x7f0800f8
com.example.uhf:drawable/notification_oversize_large_icon_bg = 0x7f0800f7
com.example.uhf:style/Platform.MaterialComponents = 0x7f130144
com.example.uhf:attr/clearsTag = 0x7f0400e3
com.example.uhf:string/tel_title_Signal = 0x7f12038b
com.example.uhf:drawable/notification_icon_background = 0x7f0800f6
com.example.uhf:drawable/notification_bg_low_pressed = 0x7f0800f3
com.example.uhf:drawable/notification_bg_low_normal = 0x7f0800f2
com.example.uhf:drawable/notification_bg_low = 0x7f0800f1
com.example.uhf:string/btn_light_continuous = 0x7f1200c3
com.example.uhf:drawable/notification_bg = 0x7f0800f0
com.example.uhf:id/special_effects_controller_view_tag = 0x7f090274
com.example.uhf:dimen/mtrl_calendar_navigation_height = 0x7f070291
com.example.uhf:drawable/notification_action_background = 0x7f0800ef
com.example.uhf:string/tv_all = 0x7f120432
com.example.uhf:drawable/navigation_empty_icon = 0x7f0800ee
com.example.uhf:string/ap_list_header_ssid = 0x7f12004c
com.example.uhf:drawable/mtrl_tabs_default_indicator = 0x7f0800ed
com.example.uhf:drawable/mtrl_switch_track_decoration = 0x7f0800ec
com.example.uhf:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f13047b
com.example.uhf:integer/design_snackbar_text_max_lines = 0x7f0a0006
com.example.uhf:array/arrayNum = 0x7f03001c
com.example.uhf:drawable/mtrl_switch_thumb_unchecked_checked = 0x7f0800e9
com.example.uhf:dimen/text_size_29 = 0x7f070346
com.example.uhf:id/dragStart = 0x7f090112
com.example.uhf:drawable/mtrl_switch_thumb_unchecked = 0x7f0800e8
com.example.uhf:drawable/mtrl_switch_thumb_checked_pressed = 0x7f0800e3
com.example.uhf:drawable/mtrl_popupmenu_background_overlay = 0x7f0800e0
com.example.uhf:style/Animation.Material3.SideSheetDialog.Left = 0x7f13000a
com.example.uhf:drawable/mtrl_ic_error = 0x7f0800dc
com.example.uhf:styleable/ListPopupWindow = 0x7f140051
com.example.uhf:drawable/mtrl_ic_checkbox_unchecked = 0x7f0800db
com.example.uhf:animator/design_appbar_state_list_animator = 0x7f020000
com.example.uhf:drawable/mtrl_ic_checkbox_checked = 0x7f0800da
com.example.uhf:id/jumpToEnd = 0x7f09017b
com.example.uhf:drawable/mtrl_ic_check_mark = 0x7f0800d9
com.example.uhf:id/etUploadEndpoint = 0x7f090144
com.example.uhf:string/material_slider_range_start = 0x7f12022d
com.example.uhf:drawable/mtrl_ic_cancel = 0x7f0800d8
com.example.uhf:attr/checkedTextViewStyle = 0x7f0400c6
com.example.uhf:id/scrollIndicatorUp = 0x7f090247
com.example.uhf:drawable/mtrl_ic_arrow_drop_down = 0x7f0800d6
com.example.uhf:macro/m3_comp_text_button_focus_state_layer_color = 0x7f0d0142
com.example.uhf:color/background_material_dark = 0x7f06001f
com.example.uhf:drawable/mtrl_dialog_background = 0x7f0800d4
com.example.uhf:attr/placeholderTextColor = 0x7f0403a5
com.example.uhf:drawable/mtrl_checkbox_button_unchecked_checked = 0x7f0800d3
com.example.uhf:styleable/ConstraintLayout_Layout = 0x7f14002b
com.example.uhf:dimen/mtrl_btn_focused_z = 0x7f070263
com.example.uhf:drawable/mtrl_checkbox_button_icon_unchecked_indeterminate = 0x7f0800d2
com.example.uhf:attr/maxVelocity = 0x7f04032e
com.example.uhf:drawable/mtrl_checkbox_button_icon_indeterminate_unchecked = 0x7f0800d0
com.example.uhf:attr/actionModeCopyDrawable = 0x7f040016
com.example.uhf:attr/arrowShaftLength = 0x7f040041
com.example.uhf:string/network_msg_wifi_conn = 0x7f1202c6
com.example.uhf:attr/image_height = 0x7f040255
com.example.uhf:drawable/mtrl_checkbox_button_icon_indeterminate_checked = 0x7f0800cf
com.example.uhf:layout/mtrl_alert_dialog = 0x7f0c0052
com.example.uhf:color/bright_foreground_inverse_material_light = 0x7f060029
com.example.uhf:drawable/mtrl_checkbox_button_icon_checked_unchecked = 0x7f0800ce
com.example.uhf:drawable/mtrl_checkbox_button_icon_checked_indeterminate = 0x7f0800cd
com.example.uhf:dimen/abc_text_size_display_3_material = 0x7f070045
com.example.uhf:drawable/mtrl_checkbox_button_icon = 0x7f0800cc
com.example.uhf:string/title_activity_camera = 0x7f1203aa
com.example.uhf:drawable/mtrl_checkbox_button_checked_unchecked = 0x7f0800cb
com.example.uhf:string/mtrl_switch_thumb_group_name = 0x7f1202b4
com.example.uhf:macro/m3_comp_checkbox_selected_disabled_container_color = 0x7f0d0007
com.example.uhf:style/Widget.Material3.FloatingActionButton.Large.Primary = 0x7f1303b4
com.example.uhf:id/ignoreRequest = 0x7f090170
com.example.uhf:drawable/mtrl_bottomsheet_drag_handle = 0x7f0800c9
com.example.uhf:drawable/material_ic_menu_arrow_up_black_24dp = 0x7f0800c7
com.example.uhf:drawable/material_ic_keyboard_arrow_right_black_24dp = 0x7f0800c5
com.example.uhf:style/Widget.Material3.MaterialCalendar.HeaderSelection = 0x7f1303d0
com.example.uhf:drawable/material_ic_clear_black_24dp = 0x7f0800c0
com.example.uhf:attr/layout_insetEdge = 0x7f0402d1
com.example.uhf:id/layoutUserAreaNumber = 0x7f090182
com.example.uhf:drawable/m3_tabs_transparent_background = 0x7f0800bd
com.example.uhf:macro/m3_comp_outlined_text_field_disabled_supporting_text_color = 0x7f0d00b5
com.example.uhf:macro/m3_comp_primary_navigation_tab_inactive_pressed_state_layer_color = 0x7f0d00ce
com.example.uhf:drawable/m3_tabs_rounded_line_indicator = 0x7f0800bc
com.example.uhf:styleable/ConstraintLayout_placeholder = 0x7f14002d
com.example.uhf:macro/m3_comp_navigation_rail_active_label_text_color = 0x7f0d0097
com.example.uhf:color/m3_ref_palette_dynamic_primary0 = 0x7f0600ea
com.example.uhf:macro/m3_comp_dialog_headline_color = 0x7f0d0024
com.example.uhf:drawable/m3_selection_control_ripple = 0x7f0800b9
com.example.uhf:drawable/mtrl_switch_thumb_checked_unchecked = 0x7f0800e4
com.example.uhf:drawable/m3_radiobutton_ripple = 0x7f0800b8
com.example.uhf:color/material_dynamic_tertiary70 = 0x7f06027a
com.example.uhf:string/network_msg_wifi_level = 0x7f1202c9
com.example.uhf:drawable/m3_avd_hide_password = 0x7f0800b3
com.example.uhf:layout/mtrl_calendar_month_labeled = 0x7f0c005e
com.example.uhf:attr/textAppearanceHeadline4 = 0x7f040484
com.example.uhf:color/foreground_material_dark = 0x7f060063
com.example.uhf:drawable/ic_mtrl_chip_close_circle = 0x7f0800ae
com.example.uhf:string/file_btn_cancel = 0x7f120170
com.example.uhf:color/material_timepicker_clock_text_color = 0x7f0602d0
com.example.uhf:drawable/ic_mtrl_chip_checked_circle = 0x7f0800ad
com.example.uhf:drawable/ic_mtrl_checked_circle = 0x7f0800ab
com.example.uhf:id/btn_deactivate = 0x7f0900a9
com.example.uhf:drawable/ic_launcher_background = 0x7f0800a6
com.example.uhf:dimen/mtrl_exposed_dropdown_menu_popup_elevation = 0x7f0702a8
com.example.uhf:drawable/ic_keyboard_black_24dp = 0x7f0800a4
com.example.uhf:string/er_dsoft_tab_pic = 0x7f120161
com.example.uhf:attr/summaryOn = 0x7f04044c
com.example.uhf:dimen/m3_comp_sheet_bottom_docked_modal_container_elevation = 0x7f070181
com.example.uhf:drawable/abc_list_divider_material = 0x7f08004d
com.example.uhf:drawable/ic_clear_black_24 = 0x7f0800a2
com.example.uhf:attr/isPreferenceVisible = 0x7f040266
com.example.uhf:drawable/goroot = 0x7f080099
com.example.uhf:drawable/ic_call_decline_low = 0x7f0800a1
com.example.uhf:drawable/ic_call_decline = 0x7f0800a0
com.example.uhf:drawable/ic_call_answer_video = 0x7f08009e
com.example.uhf:attr/shrinkMotionSpec = 0x7f040405
com.example.uhf:drawable/ic_call_answer_low = 0x7f08009d
com.example.uhf:drawable/ic_arrow_back_black_24 = 0x7f08009a
com.example.uhf:id/btnDisable = 0x7f090091
com.example.uhf:drawable/design_snackbar_background = 0x7f080097
com.example.uhf:string/uhf_msg_inventory_stop_fail = 0x7f12044f
com.example.uhf:style/Theme.Material3.Dark.Dialog = 0x7f130254
com.example.uhf:drawable/check_text_color2 = 0x7f080091
com.example.uhf:drawable/button_bg_up3 = 0x7f08008d
com.example.uhf:attr/bottomNavigationStyle = 0x7f040081
com.example.uhf:color/m3_ref_palette_dynamic_neutral6 = 0x7f0600c6
com.example.uhf:drawable/button_bg_up2 = 0x7f08008c
com.example.uhf:styleable/EditTextPreference = 0x7f140036
com.example.uhf:style/TextAppearance.Material3.BodySmall = 0x7f130211
com.example.uhf:drawable/button_bg_up = 0x7f08008b
com.example.uhf:macro/m3_comp_sheet_side_detached_container_shape = 0x7f0d0107
com.example.uhf:dimen/m3_comp_search_bar_container_elevation = 0x7f070174
com.example.uhf:drawable/button_bg_gray = 0x7f08008a
com.example.uhf:string/battery_tips_HEALTH_UNKNOWN = 0x7f120078
com.example.uhf:string/searchbar_scrolling_view_behavior = 0x7f120364
com.example.uhf:string/lf_msg_scan_fail = 0x7f1201ea
com.example.uhf:drawable/button_bg_down3 = 0x7f080089
com.example.uhf:style/Base.Theme.MaterialComponents.Dialog = 0x7f13006c
com.example.uhf:drawable/button_bg_down2 = 0x7f080088
com.example.uhf:attr/counterTextColor = 0x7f040166
com.example.uhf:drawable/button_bg_down = 0x7f080087
com.example.uhf:macro/m3_comp_input_chip_label_text_type = 0x7f0d005c
com.example.uhf:integer/m3_sys_motion_duration_extra_long4 = 0x7f0a0012
com.example.uhf:drawable/btn_radio_on_to_off_mtrl_animation = 0x7f080083
com.example.uhf:string/rfid_mgs_locktip = 0x7f120342
com.example.uhf:drawable/btn_radio_off_to_on_mtrl_animation = 0x7f080081
com.example.uhf:style/ThemeOverlay.MaterialComponents.TimePicker = 0x7f13030c
com.example.uhf:attr/badgeWidth = 0x7f040064
com.example.uhf:drawable/btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f08007f
com.example.uhf:attr/order = 0x7f040384
com.example.uhf:drawable/avd_hide_password = 0x7f08007a
com.example.uhf:id/navigation_bar_item_active_indicator_view = 0x7f0901d3
com.example.uhf:drawable/audio = 0x7f080079
com.example.uhf:style/Widget.Material3.ExtendedFloatingActionButton.Secondary = 0x7f1303b1
com.example.uhf:drawable/abc_vector_test = 0x7f080077
com.example.uhf:string/title_scan = 0x7f1203eb
com.example.uhf:drawable/abc_textfield_search_default_mtrl_alpha = 0x7f080075
com.example.uhf:drawable/abc_textfield_activated_mtrl_alpha = 0x7f080072
com.example.uhf:style/Theme.AppCompat.Dialog = 0x7f13023f
com.example.uhf:drawable/abc_text_select_handle_right_mtrl = 0x7f080071
com.example.uhf:drawable/abc_text_select_handle_middle_mtrl = 0x7f080070
com.example.uhf:attr/state_above_anchor = 0x7f04042d
com.example.uhf:string/psam_title_cmd = 0x7f120313
com.example.uhf:drawable/abc_text_select_handle_left_mtrl = 0x7f08006f
com.example.uhf:string/rfid_title_continuous_count = 0x7f120357
com.example.uhf:dimen/m3_comp_switch_disabled_unselected_handle_opacity = 0x7f070197
com.example.uhf:drawable/abc_tab_indicator_material = 0x7f08006c
com.example.uhf:style/ShapeAppearance.MaterialComponents.Tooltip = 0x7f1301aa
com.example.uhf:attr/spinnerDropDownItemStyle = 0x7f040417
com.example.uhf:drawable/abc_switch_track_mtrl_alpha = 0x7f08006b
com.example.uhf:drawable/abc_spinner_textfield_background_material = 0x7f080067
com.example.uhf:drawable/abc_spinner_mtrl_am_alpha = 0x7f080066
com.example.uhf:id/tag_on_receive_content_listener = 0x7f090293
com.example.uhf:bool/abc_config_actionMenuItemAllCaps = 0x7f050001
com.example.uhf:drawable/abc_seekbar_track_material = 0x7f080065
com.example.uhf:drawable/abc_seekbar_thumb_material = 0x7f080063
com.example.uhf:dimen/m3_btn_icon_btn_padding_left = 0x7f0700d6
com.example.uhf:drawable/abc_scrubber_control_to_pressed_mtrl_005 = 0x7f080060
com.example.uhf:attr/itemMaxLines = 0x7f04026f
com.example.uhf:drawable/abc_scrubber_control_off_mtrl_alpha = 0x7f08005e
com.example.uhf:string/title_ping_restart = 0x7f1203e5
com.example.uhf:drawable/abc_ratingbar_small_material = 0x7f08005d
com.example.uhf:string/button_book_search = 0x7f1200c9
com.example.uhf:style/Base.TextAppearance.MaterialComponents.Subtitle2 = 0x7f13004b
com.example.uhf:string/none_found = 0x7f1202d9
com.example.uhf:drawable/abc_popup_background_mtrl_mult = 0x7f08005a
com.example.uhf:drawable/abc_menu_hardkey_panel_mtrl_mult = 0x7f080059
com.example.uhf:macro/m3_comp_navigation_drawer_headline_type = 0x7f0d0085
com.example.uhf:styleable/OnSwipe = 0x7f140076
com.example.uhf:drawable/abc_list_selector_holo_dark = 0x7f080057
com.example.uhf:id/open_search_view_status_bar_spacer = 0x7f0901f3
com.example.uhf:drawable/abc_list_selector_background_transition_holo_light = 0x7f080054
com.example.uhf:string/button_enable = 0x7f1200d0
com.example.uhf:id/callMeasure = 0x7f0900ae
com.example.uhf:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__1 = 0x7f080016
com.example.uhf:drawable/abc_list_longpressed_holo = 0x7f080050
com.example.uhf:string/er_dsoft_Set_fail = 0x7f120159
com.example.uhf:dimen/abc_dialog_padding_top_material = 0x7f070025
com.example.uhf:style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow = 0x7f13017f
com.example.uhf:id/mtrl_motion_snapshot_view = 0x7f0901c7
com.example.uhf:drawable/abc_item_background_holo_dark = 0x7f08004b
com.example.uhf:macro/m3_comp_sheet_side_docked_modal_container_shape = 0x7f0d0109
com.example.uhf:drawable/abc_ic_voice_search_api_material = 0x7f08004a
com.example.uhf:drawable/abc_ic_search_api_material = 0x7f080049
com.example.uhf:attr/extendedFloatingActionButtonStyle = 0x7f0401e2
com.example.uhf:drawable/abc_ic_menu_share_mtrl_alpha = 0x7f080048
com.example.uhf:style/Base.Widget.AppCompat.EditText = 0x7f1300e0
com.example.uhf:drawable/abc_ic_menu_paste_mtrl_am_alpha = 0x7f080046
com.example.uhf:dimen/m3_appbar_size_medium = 0x7f0700b0
com.example.uhf:style/Base.CardView = 0x7f130014
com.example.uhf:string/Weibo_Message_LONG = 0x7f12001c
com.example.uhf:layout/mtrl_picker_header_fullscreen = 0x7f0c006a
com.example.uhf:drawable/abc_ic_menu_cut_mtrl_alpha = 0x7f080044
com.example.uhf:string/camera_none = 0x7f1200eb
com.example.uhf:color/m3_ref_palette_dynamic_secondary90 = 0x7f060101
com.example.uhf:drawable/abc_ic_menu_copy_mtrl_am_alpha = 0x7f080043
com.example.uhf:drawable/abc_ic_go_search_api_material = 0x7f080042
com.example.uhf:layout/mtrl_alert_select_dialog_item = 0x7f0c0055
com.example.uhf:drawable/abc_ic_commit_search_api_mtrl_alpha = 0x7f080041
com.example.uhf:attr/tintNavigationIcon = 0x7f0404d3
com.example.uhf:drawable/abc_ic_ab_back_material = 0x7f08003e
com.example.uhf:drawable/abc_edit_text_material = 0x7f08003d
com.example.uhf:drawable/abc_dialog_material_background = 0x7f08003c
com.example.uhf:color/mtrl_text_btn_text_color_selector = 0x7f060304
com.example.uhf:drawable/abc_control_background_material = 0x7f08003b
com.example.uhf:style/ThemeOverlay.Material3.Button.IconButton = 0x7f1302b7
com.example.uhf:drawable/abc_cab_background_top_material = 0x7f080039
com.example.uhf:string/uhf_msg_set_power_succ = 0x7f120468
com.example.uhf:string/fingerprint_btn_import = 0x7f12017b
com.example.uhf:attr/dialogIcon = 0x7f040185
com.example.uhf:drawable/abc_cab_background_internal_bg = 0x7f080038
com.example.uhf:style/Widget.Material3.ExtendedFloatingActionButton.Primary = 0x7f1303b0
com.example.uhf:style/CustomTheme = 0x7f130128
com.example.uhf:string/msg_default_meta = 0x7f120244
com.example.uhf:attr/colorOnPrimaryContainer = 0x7f04010c
com.example.uhf:drawable/abc_btn_switch_to_on_mtrl_00001 = 0x7f080036
com.example.uhf:style/PreferenceSummaryTextStyle = 0x7f13016b
com.example.uhf:color/m3_sys_color_dynamic_light_surface_container_highest = 0x7f0601d5
com.example.uhf:string/preferences_device_bug_workarounds_title = 0x7f1202f8
com.example.uhf:drawable/abc_btn_radio_to_on_mtrl_015 = 0x7f080035
com.example.uhf:drawable/abc_btn_radio_material_anim = 0x7f080033
com.example.uhf:attr/buttonBarPositiveButtonStyle = 0x7f040094
com.example.uhf:color/material_personalized_color_primary_container = 0x7f0602ab
com.example.uhf:string/nfc_msg_too_long = 0x7f1202d3
com.example.uhf:drawable/abc_btn_radio_material = 0x7f080032
com.example.uhf:string/wifi_changing_network = 0x7f1204b5
com.example.uhf:string/tvTagUii = 0x7f12042a
com.example.uhf:attr/fabAlignmentMode = 0x7f0401e6
com.example.uhf:drawable/abc_switch_thumb_material = 0x7f08006a
com.example.uhf:drawable/abc_btn_default_mtrl_shape = 0x7f080031
com.example.uhf:drawable/abc_btn_check_to_on_mtrl_000 = 0x7f08002e
com.example.uhf:string/result_tel = 0x7f12032d
com.example.uhf:drawable/abc_btn_check_material_anim = 0x7f08002d
com.example.uhf:id/groups = 0x7f090162
com.example.uhf:drawable/abc_btn_check_material = 0x7f08002c
com.example.uhf:style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f130234
com.example.uhf:string/button_get_directions = 0x7f1200d1
com.example.uhf:drawable/abc_action_bar_item_background_material = 0x7f08002a
com.example.uhf:drawable/abc_ab_share_pack_mtrl_alpha = 0x7f080029
com.example.uhf:attr/extendMotionSpec = 0x7f0401de
com.example.uhf:drawable/$mtrl_switch_thumb_unchecked_pressed__0 = 0x7f080028
com.example.uhf:drawable/$mtrl_switch_thumb_unchecked_checked__1 = 0x7f080027
com.example.uhf:string/lp_title_psensor = 0x7f120204
com.example.uhf:style/Widget.Material3.LinearProgressIndicator = 0x7f1303c1
com.example.uhf:style/Theme.AppCompat.DayNight.Dialog.Alert = 0x7f13023b
com.example.uhf:attr/coplanarSiblingViewId = 0x7f040155
com.example.uhf:id/mtrl_picker_text_input_date = 0x7f0901cd
com.example.uhf:drawable/$mtrl_switch_thumb_pressed_unchecked__0 = 0x7f080025
com.example.uhf:style/Widget.AppCompat.Spinner.Underlined = 0x7f130356
com.example.uhf:macro/m3_comp_navigation_drawer_inactive_label_text_color = 0x7f0d008d
com.example.uhf:drawable/$mtrl_switch_thumb_checked_unchecked__1 = 0x7f080023
com.example.uhf:color/m3_ref_palette_dynamic_neutral10 = 0x7f0600bb
com.example.uhf:drawable/$mtrl_switch_thumb_checked_pressed__0 = 0x7f080021
com.example.uhf:drawable/$mtrl_checkbox_button_unchecked_checked__2 = 0x7f080020
com.example.uhf:styleable/ClockFaceView = 0x7f140024
com.example.uhf:id/etData_light_filter = 0x7f09012b
com.example.uhf:style/Widget.Material3.CompoundButton.CheckBox = 0x7f1303a7
com.example.uhf:dimen/mtrl_calendar_content_padding = 0x7f07027a
com.example.uhf:drawable/$mtrl_checkbox_button_unchecked_checked__0 = 0x7f08001e
com.example.uhf:drawable/actionbar_back = 0x7f080078
com.example.uhf:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__2 = 0x7f08001d
com.example.uhf:drawable/$mtrl_checkbox_button_icon_unchecked_checked__0 = 0x7f080018
com.example.uhf:id/rbEPC_filter_wt = 0x7f090216
com.example.uhf:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__2 = 0x7f080017
com.example.uhf:drawable/$avd_hide_password__0 = 0x7f080000
com.example.uhf:dimen/def_drawer_elevation = 0x7f07005f
com.example.uhf:string/m1_title_read = 0x7f12020b
com.example.uhf:color/design_dark_default_color_primary = 0x7f06003f
com.example.uhf:attr/icon = 0x7f040244
com.example.uhf:drawable/$mtrl_checkbox_button_icon_checked_indeterminate__0 = 0x7f080010
com.example.uhf:style/Base.Widget.AppCompat.ActionBar.TabText = 0x7f1300ca
com.example.uhf:drawable/$mtrl_checkbox_button_checked_unchecked__2 = 0x7f08000f
com.example.uhf:id/path = 0x7f090200
com.example.uhf:attr/searchIcon = 0x7f0403df
com.example.uhf:drawable/$m3_avd_show_password__0 = 0x7f08000a
com.example.uhf:animator/m3_extended_fab_state_list_animator = 0x7f020014
com.example.uhf:drawable/$m3_avd_hide_password__1 = 0x7f080008
com.example.uhf:macro/m3_comp_slider_handle_color = 0x7f0d010f
com.example.uhf:drawable/$m3_avd_hide_password__0 = 0x7f080007
com.example.uhf:drawable/$avd_show_password__0 = 0x7f080003
com.example.uhf:drawable/$avd_hide_password__1 = 0x7f080001
com.example.uhf:id/material_clock_period_toggle = 0x7f0901a3
com.example.uhf:dimen/tooltip_precise_anchor_threshold = 0x7f070351
com.example.uhf:string/HintChannelCount = 0x7f12000b
com.example.uhf:color/m3_sys_color_dynamic_dark_error = 0x7f060199
com.example.uhf:dimen/tooltip_horizontal_padding = 0x7f07034e
com.example.uhf:string/uhf_msg_read_power_fail = 0x7f12045a
com.example.uhf:integer/mtrl_view_invisible = 0x7f0a0040
com.example.uhf:attr/tickColor = 0x7f0404c8
com.example.uhf:dimen/text_size_8 = 0x7f07034b
com.example.uhf:dimen/text_size_32 = 0x7f070349
com.example.uhf:color/m3_sys_color_on_secondary_fixed = 0x7f06020c
com.example.uhf:dimen/text_size_30 = 0x7f070347
com.example.uhf:dimen/cardview_default_radius = 0x7f070056
com.example.uhf:dimen/text_size_28 = 0x7f070345
com.example.uhf:string/desfire_title_read_permission = 0x7f120138
com.example.uhf:dimen/text_size_27 = 0x7f070344
com.example.uhf:color/m3_ref_palette_dynamic_neutral70 = 0x7f0600c8
com.example.uhf:dimen/text_size_20 = 0x7f07033d
com.example.uhf:string/desfire_title_write_permission = 0x7f120145
com.example.uhf:dimen/mtrl_extended_fab_bottom_padding = 0x7f0702ab
com.example.uhf:style/ThemeOverlay.Material3.Button = 0x7f1302b5
com.example.uhf:style/ShapeAppearance.Material3.Corner.Medium = 0x7f13019d
com.example.uhf:id/rbRESERVED = 0x7f09021c
com.example.uhf:drawable/$m3_avd_show_password__2 = 0x7f08000c
com.example.uhf:string/configure = 0x7f1200fa
com.example.uhf:string/OAUTH_AccessToken_ERROR = 0x7f120011
com.example.uhf:string/title_activity_gps = 0x7f1203b0
com.example.uhf:color/material_personalized_color_primary_text = 0x7f0602ad
com.example.uhf:color/material_dynamic_color_dark_on_error_container = 0x7f060239
com.example.uhf:dimen/text_size_14 = 0x7f070337
com.example.uhf:style/TextAppearance.AppCompat.Tooltip = 0x7f1301da
com.example.uhf:color/material_personalized_color_error = 0x7f06029a
com.example.uhf:dimen/shadow_width = 0x7f070330
com.example.uhf:string/material_timepicker_hour = 0x7f120231
com.example.uhf:string/rfid_mgs_error_not_supper_write = 0x7f120339
com.example.uhf:attr/layout_constraintEnd_toStartOf = 0x7f0402a7
com.example.uhf:dimen/preferences_header_width = 0x7f07032f
com.example.uhf:color/mtrl_btn_transparent_bg_color = 0x7f0602da
com.example.uhf:color/m3_chip_text_color = 0x7f060090
com.example.uhf:dimen/preference_seekbar_value_minWidth = 0x7f07032d
com.example.uhf:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text = 0x7f130176
com.example.uhf:attr/windowNoTitle = 0x7f040529
com.example.uhf:dimen/preference_dropdown_padding_start = 0x7f070329
com.example.uhf:array/desfire_array_encry = 0x7f030034
com.example.uhf:array/arrayLock = 0x7f030018
com.example.uhf:dimen/notification_top_pad_large_text = 0x7f070328
com.example.uhf:drawable/mtrl_switch_thumb_unchecked_pressed = 0x7f0800ea
com.example.uhf:color/m3_ref_palette_dynamic_tertiary0 = 0x7f060104
com.example.uhf:dimen/m3_comp_top_app_bar_small_container_elevation = 0x7f0701af
com.example.uhf:dimen/notification_small_icon_background_padding = 0x7f070324
com.example.uhf:dimen/notification_action_icon_size = 0x7f07031a
com.example.uhf:dimen/network_list_channel_width = 0x7f070312
com.example.uhf:dimen/mtrl_tooltip_padding = 0x7f07030f
com.example.uhf:attr/layout_scrollInterpolator = 0x7f0402d7
com.example.uhf:color/material_dynamic_secondary80 = 0x7f06026e
com.example.uhf:dimen/mtrl_tooltip_minWidth = 0x7f07030e
com.example.uhf:dimen/mtrl_tooltip_minHeight = 0x7f07030d
com.example.uhf:string/title_activity_yi_d = 0x7f1203cb
com.example.uhf:dimen/mtrl_tooltip_arrowSize = 0x7f07030b
com.example.uhf:integer/mtrl_switch_thumb_pre_morphing_duration = 0x7f0a0038
com.example.uhf:dimen/mtrl_textinput_end_icon_margin_start = 0x7f070307
com.example.uhf:style/Base.DialogWindowTitleBackground.AppCompat = 0x7f130016
com.example.uhf:drawable/abc_btn_radio_to_on_mtrl_000 = 0x7f080034
com.example.uhf:color/m3_sys_color_light_on_tertiary_container = 0x7f0601f8
com.example.uhf:style/Theme.MaterialComponents.Dialog.Alert = 0x7f130285
com.example.uhf:dimen/m3_fab_translation_z_pressed = 0x7f0701bd
com.example.uhf:dimen/mtrl_textinput_counter_margin_start = 0x7f070306
com.example.uhf:dimen/mtrl_textinput_box_stroke_width_focused = 0x7f070305
com.example.uhf:style/Base.Widget.AppCompat.RatingBar.Small = 0x7f1300f6
com.example.uhf:dimen/mtrl_textinput_box_stroke_width_default = 0x7f070304
com.example.uhf:dimen/mtrl_switch_track_width = 0x7f070300
com.example.uhf:dimen/mtrl_switch_track_height = 0x7f0702ff
com.example.uhf:layout/m3_side_sheet_dialog = 0x7f0c0043
com.example.uhf:style/TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f1301db
com.example.uhf:dimen/m3_extended_fab_min_height = 0x7f0701b7
com.example.uhf:dimen/mtrl_switch_thumb_size = 0x7f0702fe
com.example.uhf:style/Widget.Material3.Button.TextButton.Snackbar = 0x7f130389
com.example.uhf:string/btClear = 0x7f1200ac
com.example.uhf:color/m3_sys_color_dark_inverse_surface = 0x7f06017b
com.example.uhf:color/red = 0x7f060319
com.example.uhf:dimen/mtrl_switch_text_padding = 0x7f0702fb
com.example.uhf:attr/collapsedTitleGravity = 0x7f0400f4
com.example.uhf:id/action_menu_presenter = 0x7f090065
com.example.uhf:string/path_password_eye_mask_strike_through = 0x7f1202e2
com.example.uhf:attr/maxHeight = 0x7f04032a
com.example.uhf:color/m3_dark_highlighted_text = 0x7f060093
com.example.uhf:string/Weibo_Message_NULL = 0x7f12001d
com.example.uhf:dimen/mtrl_snackbar_padding_horizontal = 0x7f0702fa
com.example.uhf:dimen/mtrl_snackbar_message_margin_horizontal = 0x7f0702f9
com.example.uhf:dimen/material_cursor_width = 0x7f070236
com.example.uhf:style/Base.TextAppearance.AppCompat.Headline = 0x7f130023
com.example.uhf:color/m3_sys_color_dark_outline_variant = 0x7f060188
com.example.uhf:attr/defaultMarginsEnabled = 0x7f04017b
com.example.uhf:color/material_slider_halo_color = 0x7f0602ca
com.example.uhf:attr/colorPrimary = 0x7f04011d
com.example.uhf:dimen/mtrl_snackbar_background_overlay_color_alpha = 0x7f0702f7
com.example.uhf:dimen/mtrl_snackbar_background_corner_radius = 0x7f0702f6
com.example.uhf:dimen/mtrl_slider_tick_radius = 0x7f0702f1
com.example.uhf:macro/m3_comp_navigation_bar_inactive_hover_label_text_color = 0x7f0d0070
com.example.uhf:color/m3_sys_color_dynamic_dark_error_container = 0x7f06019a
com.example.uhf:dimen/m3_ripple_selectable_pressed_alpha = 0x7f0701de
com.example.uhf:dimen/mtrl_slider_tick_min_spacing = 0x7f0702f0
com.example.uhf:style/Base.ThemeOverlay.MaterialComponents.Dialog = 0x7f130089
com.example.uhf:dimen/mtrl_slider_thumb_elevation = 0x7f0702ee
com.example.uhf:string/uhf_not_support_rssi = 0x7f12047d
com.example.uhf:dimen/m3_comp_navigation_bar_container_height = 0x7f07013e
com.example.uhf:dimen/mtrl_slider_label_square_side = 0x7f0702ed
com.example.uhf:attr/floatingActionButtonSmallPrimaryStyle = 0x7f0401fe
com.example.uhf:attr/actionBarTabBarStyle = 0x7f040008
com.example.uhf:color/material_dynamic_tertiary90 = 0x7f06027c
com.example.uhf:dimen/mtrl_slider_halo_radius = 0x7f0702ea
com.example.uhf:dimen/mtrl_shape_corner_size_medium_component = 0x7f0702e8
com.example.uhf:attr/setsTag = 0x7f0403ec
com.example.uhf:color/m3_sys_color_dynamic_dark_outline = 0x7f0601a9
com.example.uhf:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0b0003
com.example.uhf:style/Theme.AppCompat.Dialog.MinWidth = 0x7f130241
com.example.uhf:style/TextAppearance.M3.Sys.Typescale.TitleLarge = 0x7f13020a
com.example.uhf:dimen/mtrl_progress_circular_track_thickness_small = 0x7f0702e4
com.example.uhf:color/m3_ref_palette_dynamic_neutral12 = 0x7f0600bd
com.example.uhf:dimen/mtrl_progress_circular_inset_small = 0x7f0702dc
com.example.uhf:dimen/mtrl_progress_circular_inset_medium = 0x7f0702db
com.example.uhf:string/uhf_msg_inventory_fail = 0x7f12044d
com.example.uhf:id/default_activity_button = 0x7f0900fc
com.example.uhf:dimen/mtrl_progress_circular_inset_extra_small = 0x7f0702da
com.example.uhf:dimen/activity_vertical_margin = 0x7f070052
com.example.uhf:attr/iconTint = 0x7f04024b
com.example.uhf:dimen/mtrl_progress_circular_inset = 0x7f0702d9
com.example.uhf:id/collapseActionView = 0x7f0900e4
com.example.uhf:dimen/mtrl_navigation_rail_text_bottom_margin = 0x7f0702d7
com.example.uhf:attr/borderRound = 0x7f04007b
com.example.uhf:color/blue1 = 0x7f060024
com.example.uhf:color/m3_sys_color_dynamic_light_on_error = 0x7f0601c1
com.example.uhf:dimen/mtrl_navigation_rail_icon_size = 0x7f0702d5
com.example.uhf:dimen/mtrl_navigation_rail_icon_margin = 0x7f0702d4
com.example.uhf:style/Widget.Material3.Badge.AdjustToBounds = 0x7f130370
com.example.uhf:attr/crossfade = 0x7f040167
com.example.uhf:dimen/mtrl_navigation_item_shape_horizontal_margin = 0x7f0702ce
com.example.uhf:dimen/mtrl_navigation_item_horizontal_padding = 0x7f0702cb
com.example.uhf:animator/fragment_close_exit = 0x7f020004
com.example.uhf:dimen/mtrl_navigation_elevation = 0x7f0702ca
com.example.uhf:dimen/mtrl_low_ripple_pressed_alpha = 0x7f0702c6
com.example.uhf:dimen/mtrl_low_ripple_hovered_alpha = 0x7f0702c5
com.example.uhf:attr/cursorColor = 0x7f040169
com.example.uhf:id/radarView = 0x7f09020e
com.example.uhf:attr/snackbarButtonStyle = 0x7f040412
com.example.uhf:macro/m3_comp_radio_button_unselected_pressed_state_layer_color = 0x7f0d00e5
com.example.uhf:dimen/mtrl_high_ripple_default_alpha = 0x7f0702bf
com.example.uhf:string/lf_title_read = 0x7f1201f0
com.example.uhf:style/Preference.PreferenceScreen = 0x7f13015e
com.example.uhf:attr/motionStagger = 0x7f04036a
com.example.uhf:dimen/mtrl_fab_translation_z_hovered_focused = 0x7f0702bd
com.example.uhf:macro/m3_comp_search_view_header_leading_icon_color = 0x7f0d00f6
com.example.uhf:dimen/mtrl_extended_fab_translation_z_pressed = 0x7f0702ba
com.example.uhf:style/Theme.MaterialComponents.DayNight.Dialog.Bridge = 0x7f13027c
com.example.uhf:dimen/mtrl_extended_fab_translation_z_hovered_focused = 0x7f0702b9
com.example.uhf:dimen/mtrl_extended_fab_start_padding = 0x7f0702b5
com.example.uhf:style/Base.Theme.MaterialComponents.Bridge = 0x7f13006a
com.example.uhf:dimen/m3_navigation_rail_item_padding_bottom = 0x7f0701d5
com.example.uhf:dimen/mtrl_extended_fab_icon_size = 0x7f0702b1
com.example.uhf:attr/pathMotionArc = 0x7f040399
com.example.uhf:dimen/mtrl_extended_fab_end_padding = 0x7f0702af
com.example.uhf:style/TextAppearance.AppCompat.SearchResult.Title = 0x7f1301d3
com.example.uhf:array/arrayReadLock = 0x7f030024
com.example.uhf:dimen/mtrl_extended_fab_elevation = 0x7f0702ae
com.example.uhf:macro/m3_comp_switch_selected_focus_icon_color = 0x7f0d0121
com.example.uhf:dimen/mtrl_card_elevation = 0x7f0702a4
com.example.uhf:dimen/m3_carousel_small_item_default_corner_size = 0x7f0700f5
com.example.uhf:attr/tooltipStyle = 0x7f0404ea
com.example.uhf:dimen/mtrl_calendar_year_vertical_padding = 0x7f07029e
com.example.uhf:drawable/abc_btn_check_to_on_mtrl_015 = 0x7f08002f
com.example.uhf:attr/chipIconTint = 0x7f0400ce
com.example.uhf:dimen/mtrl_calendar_year_corner = 0x7f07029b
com.example.uhf:dimen/m3_sys_motion_easing_legacy_control_y1 = 0x7f070209
com.example.uhf:dimen/mtrl_calendar_title_baseline_to_top_fullscreen = 0x7f07029a
com.example.uhf:attr/buttonIconTintMode = 0x7f04009b
com.example.uhf:dimen/mtrl_calendar_selection_text_baseline_to_top = 0x7f070297
com.example.uhf:dimen/m3_snackbar_action_text_color_alpha = 0x7f0701f3
com.example.uhf:dimen/design_tab_max_width = 0x7f07008b
com.example.uhf:dimen/mtrl_navigation_rail_elevation = 0x7f0702d3
com.example.uhf:string/battery_title_rb_cust = 0x7f120084
com.example.uhf:dimen/mtrl_calendar_selection_text_baseline_to_bottom_fullscreen = 0x7f070296
com.example.uhf:dimen/mtrl_calendar_navigation_top_padding = 0x7f070292
com.example.uhf:style/Base.Widget.Material3.Light.ActionBar.Solid = 0x7f130110
com.example.uhf:dimen/mtrl_calendar_month_vertical_padding = 0x7f07028f
com.example.uhf:drawable/abc_cab_background_top_mtrl_alpha = 0x7f08003a
com.example.uhf:style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f1301e5
com.example.uhf:dimen/m3_btn_icon_only_default_size = 0x7f0700d9
com.example.uhf:attr/track = 0x7f0404f0
com.example.uhf:dimen/m3_comp_navigation_rail_active_indicator_width = 0x7f07014b
com.example.uhf:dimen/mtrl_calendar_month_horizontal_padding = 0x7f07028e
com.example.uhf:color/abc_btn_colored_text_material = 0x7f060003
com.example.uhf:dimen/mtrl_calendar_landscape_header_width = 0x7f07028c
com.example.uhf:styleable/TextEffects = 0x7f14009f
com.example.uhf:style/Widget.Material3.Slider = 0x7f1303f9
com.example.uhf:string/battery_tips_extra_level = 0x7f120080
com.example.uhf:dimen/mtrl_calendar_header_height = 0x7f070286
com.example.uhf:dimen/mtrl_calendar_header_content_padding = 0x7f070283
com.example.uhf:anim/abc_slide_in_bottom = 0x7f010006
com.example.uhf:attr/collapseIcon = 0x7f0400f2
com.example.uhf:color/m3_ref_palette_dynamic_neutral92 = 0x7f0600cc
com.example.uhf:dimen/mtrl_calendar_day_width = 0x7f070280
com.example.uhf:attr/listItemLayout = 0x7f0402e4
com.example.uhf:dimen/mtrl_calendar_day_height = 0x7f07027c
com.example.uhf:attr/checkMarkTint = 0x7f0400b9
com.example.uhf:attr/tickColorActive = 0x7f0404c9
com.example.uhf:string/abc_action_bar_up_description = 0x7f120022
com.example.uhf:macro/m3_comp_switch_unselected_pressed_state_layer_color = 0x7f0d013d
com.example.uhf:id/rbEPC_filter = 0x7f090212
com.example.uhf:attr/showTitle = 0x7f040404
com.example.uhf:string/battery_msg_background_monitoring = 0x7f120072
com.example.uhf:attr/placeholderText = 0x7f0403a3
com.example.uhf:dimen/mtrl_btn_text_btn_padding_left = 0x7f070272
com.example.uhf:style/Widget.Material3.Chip.Input.Elevated = 0x7f130396
com.example.uhf:drawable/btn_checkbox_checked_mtrl = 0x7f08007c
com.example.uhf:attr/itemRippleColor = 0x7f040274
com.example.uhf:dimen/design_snackbar_padding_horizontal = 0x7f070087
com.example.uhf:color/highlighted_text_material_dark = 0x7f060074
com.example.uhf:dimen/mtrl_btn_padding_left = 0x7f07026b
com.example.uhf:dimen/mtrl_btn_padding_bottom = 0x7f07026a
com.example.uhf:id/peekHeight = 0x7f090202
com.example.uhf:dimen/network_list_features_size = 0x7f070315
com.example.uhf:dimen/mtrl_slider_widget_height = 0x7f0702f4
com.example.uhf:string/rfid_msg_confirm_true = 0x7f120348
com.example.uhf:dimen/mtrl_btn_letter_spacing = 0x7f070268
com.example.uhf:id/save_non_transition_alpha = 0x7f090240
com.example.uhf:attr/chipSpacing = 0x7f0400d2
com.example.uhf:attr/fabCradleVerticalOffset = 0x7f0401ec
com.example.uhf:dimen/mtrl_btn_icon_padding = 0x7f070266
com.example.uhf:dimen/mtrl_btn_icon_btn_padding_left = 0x7f070265
com.example.uhf:attr/textAppearanceLabelMedium = 0x7f04048b
com.example.uhf:color/material_personalized_color_secondary_text_inverse = 0x7f0602b2
com.example.uhf:attr/badgeGravity = 0x7f040059
com.example.uhf:drawable/mtrl_ic_indeterminate = 0x7f0800dd
com.example.uhf:dimen/mtrl_btn_elevation = 0x7f070262
com.example.uhf:id/useLogo = 0x7f0902d0
com.example.uhf:attr/tickRadiusActive = 0x7f0404ce
com.example.uhf:attr/closeIconEndPadding = 0x7f0400eb
com.example.uhf:dimen/mtrl_badge_with_text_size = 0x7f070257
com.example.uhf:dimen/mtrl_badge_toolbar_action_menu_item_horizontal_offset = 0x7f070255
com.example.uhf:dimen/mtrl_bottomappbar_fab_cradle_margin = 0x7f07025a
com.example.uhf:dimen/mtrl_badge_size = 0x7f070252
com.example.uhf:attr/voiceIcon = 0x7f040517
com.example.uhf:dimen/m3_btn_icon_btn_padding_right = 0x7f0700d7
com.example.uhf:dimen/mtrl_badge_long_text_horizontal_padding = 0x7f070251
com.example.uhf:dimen/mtrl_badge_horizontal_edge_offset = 0x7f070250
com.example.uhf:attr/layout_constraintHorizontal_chainStyle = 0x7f0402b1
com.example.uhf:string/mtrl_picker_date_header_title = 0x7f120297
com.example.uhf:dimen/material_textinput_max_width = 0x7f070247
com.example.uhf:dimen/notification_right_icon_size = 0x7f070322
com.example.uhf:attr/finishPrimaryWithSecondary = 0x7f0401f4
com.example.uhf:dimen/material_helper_text_font_1_3_padding_horizontal = 0x7f070243
com.example.uhf:attr/singleLine = 0x7f04040d
com.example.uhf:color/mtrl_tabs_colored_ripple_color = 0x7f0602ff
com.example.uhf:id/none = 0x7f0901df
com.example.uhf:dimen/material_helper_text_default_padding_top = 0x7f070242
com.example.uhf:attr/imageButtonStyle = 0x7f040250
com.example.uhf:dimen/material_filled_edittext_font_2_0_padding_top = 0x7f07023f
com.example.uhf:string/b14443_title_getid = 0x7f12006d
com.example.uhf:dimen/material_emphasis_medium = 0x7f07023b
com.example.uhf:drawable/mtrl_ic_arrow_drop_up = 0x7f0800d7
com.example.uhf:string/uhf_msg_set_filter_succ = 0x7f120462
com.example.uhf:string/lp_btn_stop = 0x7f1201fc
com.example.uhf:string/title_work_time = 0x7f1203f4
com.example.uhf:macro/m3_comp_navigation_drawer_active_pressed_label_text_color = 0x7f0d0082
com.example.uhf:dimen/mtrl_calendar_bottom_padding = 0x7f070279
com.example.uhf:dimen/material_divider_thickness = 0x7f070237
com.example.uhf:dimen/material_clock_period_toggle_horizontal_gap = 0x7f070231
com.example.uhf:dimen/material_clock_hand_stroke_width = 0x7f07022e
com.example.uhf:attr/itemSpacing = 0x7f04027c
com.example.uhf:color/m3_ref_palette_dynamic_primary20 = 0x7f0600ed
com.example.uhf:dimen/material_clock_hand_center_dot_radius = 0x7f07022c
com.example.uhf:macro/m3_comp_navigation_bar_active_focus_label_text_color = 0x7f0d0060
com.example.uhf:dimen/abc_button_inset_vertical_material = 0x7f070013
com.example.uhf:dimen/material_clock_face_margin_top = 0x7f07022b
com.example.uhf:dimen/material_clock_display_padding = 0x7f070228
com.example.uhf:dimen/abc_text_size_title_material = 0x7f07004f
com.example.uhf:id/tag_accessibility_heading = 0x7f090290
com.example.uhf:dimen/material_bottom_sheet_max_width = 0x7f070226
com.example.uhf:dimen/m3_sys_state_hover_state_layer_opacity = 0x7f070221
com.example.uhf:style/TextAppearance.MaterialComponents.Headline2 = 0x7f130229
com.example.uhf:color/material_dynamic_neutral70 = 0x7f060246
com.example.uhf:id/search_bar = 0x7f09024b
com.example.uhf:id/accessibility_action_clickable_span = 0x7f090032
com.example.uhf:dimen/m3_sys_state_dragged_state_layer_opacity = 0x7f07021f
com.example.uhf:string/tvTAndE = 0x7f120424
com.example.uhf:attr/dayTodayStyle = 0x7f040179
com.example.uhf:attr/materialCardViewFilledStyle = 0x7f040310
com.example.uhf:dimen/m3_sys_motion_easing_standard_decelerate_control_y2 = 0x7f07021e
com.example.uhf:drawable/design_ic_visibility = 0x7f080094
com.example.uhf:id/rbEPC_filter_lock = 0x7f090214
com.example.uhf:dimen/m3_sys_motion_easing_standard_decelerate_control_y1 = 0x7f07021d
com.example.uhf:attr/springDamping = 0x7f04041f
com.example.uhf:attr/cursorErrorColor = 0x7f04016a
com.example.uhf:color/mtrl_switch_thumb_tint = 0x7f0602fc
com.example.uhf:dimen/m3_sys_motion_easing_standard_decelerate_control_x1 = 0x7f07021b
com.example.uhf:string/uhf_msg_set_power_fail = 0x7f120467
com.example.uhf:dimen/m3_sys_motion_easing_standard_control_y1 = 0x7f070219
com.example.uhf:anim/grow_from_topleft_to_bottomright = 0x7f01001d
com.example.uhf:style/Base.Theme.MaterialComponents.Light.DarkActionBar = 0x7f130074
com.example.uhf:styleable/CoordinatorLayout_Layout = 0x7f140031
com.example.uhf:dimen/m3_sys_motion_easing_standard_accelerate_control_x2 = 0x7f070214
com.example.uhf:string/result_isbn = 0x7f12032a
com.example.uhf:dimen/m3_sys_motion_easing_linear_control_y1 = 0x7f070211
com.example.uhf:dimen/design_bottom_navigation_active_item_min_width = 0x7f070062
com.example.uhf:string/tagFocus = 0x7f120387
com.example.uhf:macro/m3_comp_switch_selected_hover_handle_color = 0x7f0d0125
com.example.uhf:string/call_notification_decline_action = 0x7f1200e5
com.example.uhf:color/material_dynamic_primary100 = 0x7f06025a
com.example.uhf:attr/circularflow_defaultAngle = 0x7f0400de
com.example.uhf:id/Etcmd = 0x7f090014
com.example.uhf:style/Theme.MaterialComponents.Light.Bridge = 0x7f13028f
com.example.uhf:dimen/m3_extended_fab_top_padding = 0x7f0701b9
com.example.uhf:dimen/m3_sys_motion_easing_linear_control_x2 = 0x7f070210
com.example.uhf:string/download_msg_up_succ = 0x7f120156
com.example.uhf:style/Widget.MaterialComponents.CheckedTextView = 0x7f130435
com.example.uhf:color/call_notification_decline_color = 0x7f06002f
com.example.uhf:string/bd_title_SatelliteCount = 0x7f12008e
com.example.uhf:dimen/m3_sys_motion_easing_legacy_decelerate_control_y2 = 0x7f07020e
com.example.uhf:style/Widget.MaterialComponents.MaterialCalendar.HeaderCancelButton = 0x7f130451
com.example.uhf:attr/spinnerStyle = 0x7f040418
com.example.uhf:string/rbInventoryLoop = 0x7f12031e
com.example.uhf:dimen/m3_sys_motion_easing_legacy_decelerate_control_y1 = 0x7f07020d
com.example.uhf:string/fingerprint_title_page_mode_2 = 0x7f1201b1
com.example.uhf:string/desfire_title_pwd_tip = 0x7f120137
com.example.uhf:id/tvMsg = 0x7f0902c5
com.example.uhf:color/cardview_light_background = 0x7f060031
com.example.uhf:attr/listMenuViewStyle = 0x7f0402e6
com.example.uhf:style/Base.Widget.AppCompat.ActionBar.Solid = 0x7f1300c8
com.example.uhf:dimen/m3_sys_motion_easing_legacy_decelerate_control_x1 = 0x7f07020b
com.example.uhf:dimen/mtrl_calendar_selection_baseline_to_top_fullscreen = 0x7f070294
com.example.uhf:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection = 0x7f130456
com.example.uhf:dimen/m3_sys_motion_easing_legacy_control_x2 = 0x7f070208
com.example.uhf:attr/iconGravity = 0x7f040246
com.example.uhf:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x1 = 0x7f0701ff
com.example.uhf:id/month_grid = 0x7f0901b4
com.example.uhf:color/bright_foreground_disabled_material_light = 0x7f060027
com.example.uhf:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y2 = 0x7f0701fe
com.example.uhf:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y1 = 0x7f0701fd
com.example.uhf:dimen/mtrl_calendar_text_input_padding_top = 0x7f070298
com.example.uhf:macro/m3_comp_slider_disabled_handle_color = 0x7f0d010d
com.example.uhf:dimen/m3_sys_elevation_level4 = 0x7f0701f9
com.example.uhf:dimen/m3_sys_elevation_level1 = 0x7f0701f6
com.example.uhf:attr/flow_firstHorizontalStyle = 0x7f040207
com.example.uhf:string/uhf_locating_tag_value = 0x7f120440
com.example.uhf:dimen/mtrl_btn_disabled_elevation = 0x7f070260
com.example.uhf:dimen/m3_simple_item_color_hovered_alpha = 0x7f0701ee
com.example.uhf:style/Base.Theme.AppCompat.DialogWhenLarge = 0x7f130055
com.example.uhf:string/setting_succ = 0x7f120370
com.example.uhf:color/m3_sys_color_dynamic_light_error_container = 0x7f0601bc
com.example.uhf:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Primary = 0x7f1303ac
com.example.uhf:dimen/mtrl_extended_fab_min_height = 0x7f0702b3
com.example.uhf:dimen/m3_side_sheet_standard_elevation = 0x7f0701ec
com.example.uhf:drawable/abc_ic_arrow_drop_right_black_24dp = 0x7f08003f
com.example.uhf:attr/itemIconTint = 0x7f04026e
com.example.uhf:dimen/text_size_23 = 0x7f070340
com.example.uhf:attr/liftOnScrollTargetViewId = 0x7f0402db
com.example.uhf:dimen/m3_side_sheet_modal_elevation = 0x7f0701eb
com.example.uhf:color/abc_btn_colored_borderless_text_material = 0x7f060002
com.example.uhf:dimen/m3_side_sheet_margin_detached = 0x7f0701ea
com.example.uhf:color/m3_ref_palette_dynamic_secondary95 = 0x7f060102
com.example.uhf:attr/listPreferredItemHeightSmall = 0x7f0402ea
com.example.uhf:dimen/m3_searchbar_text_size = 0x7f0701e6
com.example.uhf:dimen/tooltip_precise_anchor_extra_offset = 0x7f070350
com.example.uhf:dimen/m3_searchbar_text_margin_start_no_navigation_icon = 0x7f0701e5
com.example.uhf:dimen/m3_searchbar_outlined_stroke_width = 0x7f0701e3
com.example.uhf:dimen/m3_searchbar_margin_vertical = 0x7f0701e2
com.example.uhf:dimen/m3_searchbar_margin_horizontal = 0x7f0701e1
com.example.uhf:attr/layout_constraintTop_toTopOf = 0x7f0402be
com.example.uhf:id/search_plate = 0x7f090251
com.example.uhf:dimen/disabled_alpha_material_dark = 0x7f070090
com.example.uhf:drawable/card_normal = 0x7f08008e
com.example.uhf:dimen/mtrl_toolbar_default_height = 0x7f07030a
com.example.uhf:style/Base.V14.Theme.MaterialComponents.Light.Dialog = 0x7f13009d
com.example.uhf:dimen/m3_searchbar_elevation = 0x7f0701df
com.example.uhf:style/Base.Widget.Material3.FloatingActionButton.Large = 0x7f13010e
com.example.uhf:string/action_psam_upgrader = 0x7f120041
com.example.uhf:dimen/preference_icon_minWidth = 0x7f07032a
com.example.uhf:dimen/m3_ripple_pressed_alpha = 0x7f0701dd
com.example.uhf:dimen/m3_ripple_hovered_alpha = 0x7f0701dc
com.example.uhf:anim/abc_popup_exit = 0x7f010004
com.example.uhf:dimen/m3_navigation_rail_item_padding_top_with_large_font = 0x7f0701d8
com.example.uhf:string/desfire_title_value_debit = 0x7f120141
com.example.uhf:macro/m3_comp_top_app_bar_large_headline_type = 0x7f0d016c
com.example.uhf:dimen/m3_timepicker_window_elevation = 0x7f070224
com.example.uhf:string/camera_alert = 0x7f1200ea
com.example.uhf:style/TextAppearance.Material3.ActionBar.Title = 0x7f13020e
com.example.uhf:attr/collapsedTitleTextColor = 0x7f0400f6
com.example.uhf:dimen/m3_navigation_rail_item_min_height = 0x7f0701d4
com.example.uhf:attr/actionModeCutDrawable = 0x7f040017
com.example.uhf:id/iv_dismissDialog = 0x7f09017a
com.example.uhf:dimen/m3_navigation_rail_item_active_indicator_margin_horizontal = 0x7f0701d2
com.example.uhf:color/m3_sys_color_light_on_error = 0x7f0601ef
com.example.uhf:dimen/m3_navigation_rail_item_active_indicator_height = 0x7f0701d1
com.example.uhf:string/tvMask = 0x7f120415
com.example.uhf:attr/fabAnimationMode = 0x7f0401e9
com.example.uhf:attr/isLightTheme = 0x7f040262
com.example.uhf:dimen/m3_navigation_rail_icon_size = 0x7f0701d0
com.example.uhf:style/ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f1302fa
com.example.uhf:id/chronometer = 0x7f0900dd
com.example.uhf:dimen/m3_navigation_rail_elevation = 0x7f0701cf
com.example.uhf:string/title_activity_volum = 0x7f1203ca
com.example.uhf:dimen/m3_navigation_item_vertical_padding = 0x7f0701cb
com.example.uhf:dimen/mtrl_shape_corner_size_small_component = 0x7f0702e9
com.example.uhf:dimen/m3_navigation_item_shape_inset_top = 0x7f0701ca
com.example.uhf:dimen/m3_navigation_item_shape_inset_start = 0x7f0701c9
com.example.uhf:color/m3_dynamic_dark_primary_text_disable_only = 0x7f06009c
com.example.uhf:dimen/m3_navigation_item_shape_inset_end = 0x7f0701c8
com.example.uhf:dimen/mtrl_progress_circular_track_thickness_medium = 0x7f0702e3
com.example.uhf:dimen/mtrl_navigation_item_icon_padding = 0x7f0702cc
com.example.uhf:dimen/m3_navigation_item_horizontal_padding = 0x7f0701c5
com.example.uhf:string/character_counter_content_description = 0x7f1200ee
com.example.uhf:color/listitem_gray = 0x7f06007b
com.example.uhf:dimen/m3_large_fab_size = 0x7f0701bf
com.example.uhf:attr/layout_constrainedHeight = 0x7f040299
com.example.uhf:dimen/text_size_13 = 0x7f070336
com.example.uhf:dimen/m3_extended_fab_start_padding = 0x7f0701b8
com.example.uhf:macro/m3_comp_elevated_card_container_color = 0x7f0d002a
com.example.uhf:dimen/mtrl_calendar_year_width = 0x7f07029f
com.example.uhf:dimen/m3_extended_fab_bottom_padding = 0x7f0701b4
com.example.uhf:attr/yearSelectedStyle = 0x7f04052a
com.example.uhf:id/frost = 0x7f090159
com.example.uhf:color/m3_sys_color_dark_surface_container = 0x7f06018f
com.example.uhf:macro/m3_comp_date_picker_modal_header_headline_color = 0x7f0d0015
com.example.uhf:id/cbBlock13 = 0x7f0900b7
com.example.uhf:dimen/mtrl_calendar_day_today_stroke = 0x7f07027e
com.example.uhf:dimen/m3_datepicker_elevation = 0x7f0701b2
com.example.uhf:attr/preferenceStyle = 0x7f0403b5
com.example.uhf:string/button_dial = 0x7f1200cc
com.example.uhf:id/disablePostScroll = 0x7f090109
com.example.uhf:animator/mtrl_extended_fab_change_size_collapse_motion_spec = 0x7f020019
com.example.uhf:dimen/m3_comp_top_app_bar_medium_container_height = 0x7f0701ae
com.example.uhf:color/m3_textfield_indicator_text_color = 0x7f060220
com.example.uhf:dimen/m3_comp_time_picker_time_selector_hover_state_layer_opacity = 0x7f0701ab
com.example.uhf:dimen/m3_comp_time_picker_period_selector_pressed_state_layer_opacity = 0x7f0701a9
com.example.uhf:macro/m3_comp_filled_icon_button_container_color = 0x7f0d0048
com.example.uhf:attr/itemShapeAppearance = 0x7f040275
com.example.uhf:dimen/m3_comp_time_picker_period_selector_hover_state_layer_opacity = 0x7f0701a7
com.example.uhf:dimen/m3_comp_time_picker_container_elevation = 0x7f0701a5
com.example.uhf:dimen/m3_comp_switch_unselected_pressed_state_layer_opacity = 0x7f0701a0
com.example.uhf:string/rfid_title_continuous_read_write = 0x7f12035b
com.example.uhf:dimen/m3_comp_switch_track_height = 0x7f07019c
com.example.uhf:dimen/m3_comp_extended_fab_primary_icon_size = 0x7f070117
com.example.uhf:macro/m3_comp_navigation_drawer_active_icon_color = 0x7f0d007e
com.example.uhf:dimen/m3_comp_switch_selected_focus_state_layer_opacity = 0x7f070199
com.example.uhf:dimen/m3_comp_switch_disabled_selected_icon_opacity = 0x7f070195
com.example.uhf:style/Widget.MaterialComponents.TextInputLayout.FilledBox = 0x7f130479
com.example.uhf:id/mtrl_calendar_frame = 0x7f0901be
com.example.uhf:attr/quantizeMotionInterpolator = 0x7f0403bf
com.example.uhf:drawable/material_ic_edit_black_24dp = 0x7f0800c1
com.example.uhf:dimen/m3_comp_suggestion_chip_with_leading_icon_leading_icon_size = 0x7f070193
com.example.uhf:attr/motion_postLayoutCollision = 0x7f04036c
com.example.uhf:string/up_msg_avg_speed = 0x7f120497
com.example.uhf:dimen/m3_comp_suggestion_chip_elevated_container_elevation = 0x7f070190
com.example.uhf:dimen/m3_comp_slider_inactive_track_height = 0x7f07018c
com.example.uhf:attr/carousel_firstView = 0x7f0400ac
com.example.uhf:dimen/m3_comp_slider_disabled_inactive_track_opacity = 0x7f07018b
com.example.uhf:color/abc_background_cache_hint_selector_material_light = 0x7f060001
com.example.uhf:dimen/design_tab_scrollable_min_width = 0x7f07008c
com.example.uhf:attr/layout_keyline = 0x7f0402d2
com.example.uhf:string/ckAnti_Read = 0x7f1200f2
com.example.uhf:color/m3_dark_primary_text_disable_only = 0x7f060095
com.example.uhf:dimen/m3_comp_slider_active_handle_width = 0x7f070188
com.example.uhf:dimen/abc_control_padding_material = 0x7f07001a
com.example.uhf:dimen/m3_comp_slider_active_handle_leading_space = 0x7f070187
com.example.uhf:string/msg_sbc_no_page_returned = 0x7f120274
com.example.uhf:dimen/m3_comp_slider_active_handle_height = 0x7f070186
com.example.uhf:attr/statusBarForeground = 0x7f040437
com.example.uhf:dimen/abc_action_button_min_width_material = 0x7f07000e
com.example.uhf:style/Widget.Material3.NavigationRailView.Badge = 0x7f1303e8
com.example.uhf:style/Widget.Material3.DrawerLayout = 0x7f1303ab
com.example.uhf:style/Base.Theme.Material3.Dark.Dialog = 0x7f13005f
com.example.uhf:dimen/m3_comp_sheet_side_docked_modal_container_elevation = 0x7f070184
com.example.uhf:attr/trackColor = 0x7f0404f1
com.example.uhf:string/desfire_ms_filesize_not_null = 0x7f120107
com.example.uhf:dimen/m3_comp_sheet_side_docked_container_width = 0x7f070183
com.example.uhf:interpolator/m3_sys_motion_easing_standard_decelerate = 0x7f0b000d
com.example.uhf:dimen/m3_comp_sheet_bottom_docked_standard_container_elevation = 0x7f070182
com.example.uhf:dimen/mtrl_alert_dialog_background_inset_bottom = 0x7f07024b
com.example.uhf:dimen/m3_comp_badge_size = 0x7f070105
com.example.uhf:color/m3_ref_palette_neutral95 = 0x7f060132
com.example.uhf:dimen/m3_comp_sheet_bottom_docked_drag_handle_width = 0x7f070180
com.example.uhf:string/app_error_message = 0x7f120057
com.example.uhf:color/m3_card_ripple_color = 0x7f060088
com.example.uhf:dimen/m3_comp_navigation_rail_container_width = 0x7f07014d
com.example.uhf:style/PreferenceFragmentList = 0x7f130169
com.example.uhf:dimen/m3_comp_secondary_navigation_tab_pressed_state_layer_opacity = 0x7f07017e
com.example.uhf:attr/cornerRadius = 0x7f04015b
com.example.uhf:dimen/m3_comp_secondary_navigation_tab_focus_state_layer_opacity = 0x7f07017c
com.example.uhf:id/text_input_error_icon = 0x7f0902a3
com.example.uhf:dimen/m3_comp_search_view_full_screen_header_container_height = 0x7f07017a
com.example.uhf:attr/warmth = 0x7f040518
com.example.uhf:attr/expandedHintEnabled = 0x7f0401d5
com.example.uhf:attr/triggerId = 0x7f040504
com.example.uhf:color/mtrl_fab_icon_text_color_selector = 0x7f0602e8
com.example.uhf:dimen/m3_comp_search_view_container_elevation = 0x7f070178
com.example.uhf:attr/colorSecondaryContainer = 0x7f040126
com.example.uhf:attr/fontProviderFetchStrategy = 0x7f04021d
com.example.uhf:color/gold = 0x7f060066
com.example.uhf:dimen/m3_comp_search_bar_hover_state_layer_opacity = 0x7f070176
com.example.uhf:style/Widget.MaterialComponents.Button.UnelevatedButton.Icon = 0x7f130433
com.example.uhf:attr/haloColor = 0x7f04022e
com.example.uhf:color/sliding_menu_item_pressed = 0x7f060334
com.example.uhf:style/MaterialAlertDialog.Material3.Body.Text.CenterStacked = 0x7f13012c
com.example.uhf:string/uhf_msg_tab_scan = 0x7f120472
com.example.uhf:attr/useMaterialThemeColors = 0x7f04050c
com.example.uhf:attr/windowMinWidthMajor = 0x7f040527
com.example.uhf:dimen/mtrl_navigation_bar_item_default_margin = 0x7f0702c9
com.example.uhf:style/Widget.AppCompat.ListView.DropDown = 0x7f130345
com.example.uhf:dimen/m3_comp_navigation_drawer_focus_state_layer_opacity = 0x7f070144
com.example.uhf:dimen/m3_comp_search_bar_avatar_size = 0x7f070173
com.example.uhf:attr/actionModeWebSearchDrawable = 0x7f040020
com.example.uhf:string/fingerprint_dailog_title = 0x7f120186
com.example.uhf:id/material_minute_text_input = 0x7f0901a7
com.example.uhf:dimen/mtrl_high_ripple_focused_alpha = 0x7f0702c0
com.example.uhf:id/tvData = 0x7f0902c1
com.example.uhf:attr/dividerPadding = 0x7f040193
com.example.uhf:attr/materialAlertDialogBodyTextStyle = 0x7f0402f7
com.example.uhf:dimen/m3_comp_radio_button_selected_hover_state_layer_opacity = 0x7f07016d
com.example.uhf:style/Widget.Material3.Button.UnelevatedButton = 0x7f13038c
com.example.uhf:dimen/mtrl_progress_circular_size_medium = 0x7f0702e0
com.example.uhf:color/m3_button_background_color_selector = 0x7f060080
com.example.uhf:dimen/m3_comp_progress_indicator_track_thickness = 0x7f070169
com.example.uhf:style/Theme.MaterialComponents.Light.NoActionBar = 0x7f13029b
com.example.uhf:dimen/m3_comp_navigation_bar_active_indicator_width = 0x7f07013c
com.example.uhf:dimen/m3_comp_text_button_pressed_state_layer_opacity = 0x7f0701a3
com.example.uhf:macro/m3_comp_divider_color = 0x7f0d0028
com.example.uhf:dimen/text_size_16 = 0x7f070339
com.example.uhf:attr/mock_diagonalsColor = 0x7f04033b
com.example.uhf:color/gray1 = 0x7f060068
com.example.uhf:dimen/m3_comp_primary_navigation_tab_active_indicator_height = 0x7f070161
com.example.uhf:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox = 0x7f1302e6
com.example.uhf:dimen/m3_comp_primary_navigation_tab_active_hover_state_layer_opacity = 0x7f070160
com.example.uhf:dimen/m3_comp_primary_navigation_tab_active_focus_state_layer_opacity = 0x7f07015f
com.example.uhf:integer/mtrl_switch_track_viewport_height = 0x7f0a003c
com.example.uhf:color/m3_navigation_bar_ripple_color_selector = 0x7f0600ad
com.example.uhf:dimen/m3_comp_outlined_text_field_disabled_label_text_opacity = 0x7f07015b
com.example.uhf:dimen/m3_comp_outlined_button_outline_width = 0x7f070154
com.example.uhf:macro/m3_comp_search_bar_supporting_text_color = 0x7f0d00ee
com.example.uhf:style/Theme.Material3.DynamicColors.Light = 0x7f130266
com.example.uhf:dimen/m3_comp_outlined_autocomplete_menu_container_elevation = 0x7f070152
com.example.uhf:dimen/mtrl_switch_thumb_icon_size = 0x7f0702fd
com.example.uhf:dimen/m3_comp_navigation_rail_pressed_state_layer_opacity = 0x7f070151
com.example.uhf:dimen/m3_comp_navigation_rail_icon_size = 0x7f070150
com.example.uhf:attr/actionModePasteDrawable = 0x7f040019
com.example.uhf:string/title_scan_count = 0x7f1203ed
com.example.uhf:dimen/m3_comp_navigation_rail_hover_state_layer_opacity = 0x7f07014f
com.example.uhf:color/m3_checkbox_button_tint = 0x7f06008b
com.example.uhf:id/mtrl_anchor_parent = 0x7f0901bb
com.example.uhf:drawable/mtrl_switch_thumb_pressed_unchecked = 0x7f0800e7
com.example.uhf:dimen/m3_comp_navigation_rail_focus_state_layer_opacity = 0x7f07014e
com.example.uhf:string/download_msg_total_time = 0x7f120154
com.example.uhf:dimen/m3_comp_navigation_rail_active_indicator_height = 0x7f07014a
com.example.uhf:style/Base.Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f130119
com.example.uhf:color/m3_sys_color_dynamic_secondary_fixed = 0x7f0601e4
com.example.uhf:dimen/m3_comp_navigation_drawer_hover_state_layer_opacity = 0x7f070145
com.example.uhf:dimen/m3_navigation_menu_divider_horizontal_padding = 0x7f0701cc
com.example.uhf:attr/selectionRequired = 0x7f0403ea
com.example.uhf:dimen/m3_comp_navigation_drawer_container_width = 0x7f070143
com.example.uhf:color/m3_ref_palette_neutral_variant100 = 0x7f060138
com.example.uhf:attr/windowFixedHeightMajor = 0x7f040523
com.example.uhf:dimen/m3_comp_navigation_bar_container_elevation = 0x7f07013d
com.example.uhf:color/mtrl_navigation_item_background_color = 0x7f0602f2
com.example.uhf:string/Weibo_Share_Error = 0x7f12001e
com.example.uhf:dimen/m3_comp_input_chip_with_leading_icon_leading_icon_size = 0x7f070139
com.example.uhf:style/Base.TextAppearance.AppCompat.Menu = 0x7f13002b
com.example.uhf:dimen/m3_comp_input_chip_with_avatar_avatar_size = 0x7f070138
com.example.uhf:string/tvFastID = 0x7f120404
com.example.uhf:color/red5 = 0x7f06031e
com.example.uhf:style/Base.Widget.MaterialComponents.Snackbar = 0x7f130120
com.example.uhf:dimen/abc_search_view_preferred_height = 0x7f070036
com.example.uhf:dimen/m3_comp_input_chip_container_height = 0x7f070136
com.example.uhf:style/Theme.MaterialComponents.DayNight.Dialog.Alert = 0x7f13027a
com.example.uhf:dimen/m3_comp_input_chip_container_elevation = 0x7f070135
com.example.uhf:dimen/m3_comp_filter_chip_with_icon_icon_size = 0x7f070134
com.example.uhf:drawable/item_list_goods_show_list_bg = 0x7f0800b2
com.example.uhf:dimen/m3_comp_filter_chip_flat_container_elevation = 0x7f070132
com.example.uhf:attr/percentX = 0x7f04039d
com.example.uhf:id/parentPanel = 0x7f0901fc
com.example.uhf:dimen/m3_comp_filled_card_hover_state_layer_opacity = 0x7f07012c
com.example.uhf:drawable/mtrl_popupmenu_background = 0x7f0800df
com.example.uhf:attr/colorControlHighlight = 0x7f040102
com.example.uhf:dimen/m3_comp_filled_autocomplete_menu_container_elevation = 0x7f070126
com.example.uhf:dimen/m3_comp_fab_primary_pressed_container_elevation = 0x7f070122
com.example.uhf:color/result_view = 0x7f060324
com.example.uhf:dimen/m3_comp_fab_primary_large_icon_size = 0x7f070121
com.example.uhf:dimen/m3_comp_fab_primary_container_height = 0x7f07011b
com.example.uhf:styleable/BottomSheetBehavior_Layout = 0x7f14001a
com.example.uhf:attr/useCompatPadding = 0x7f04050a
com.example.uhf:style/ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f1302fd
com.example.uhf:dimen/m3_comp_extended_fab_primary_pressed_state_layer_opacity = 0x7f070119
com.example.uhf:dimen/m3_comp_slider_disabled_handle_opacity = 0x7f07018a
com.example.uhf:dimen/m3_comp_extended_fab_primary_pressed_container_elevation = 0x7f070118
com.example.uhf:dimen/m3_comp_extended_fab_primary_hover_state_layer_opacity = 0x7f070116
com.example.uhf:dimen/m3_comp_extended_fab_primary_container_elevation = 0x7f070111
com.example.uhf:dimen/material_clock_period_toggle_vertical_gap = 0x7f070232
com.example.uhf:color/m3_ref_palette_dynamic_primary60 = 0x7f0600f1
com.example.uhf:attr/number = 0x7f04037a
com.example.uhf:string/uhf_btn_getpower = 0x7f120438
com.example.uhf:dimen/m3_comp_elevated_button_container_elevation = 0x7f07010d
com.example.uhf:dimen/m3_comp_elevated_button_disabled_container_elevation = 0x7f07010e
com.example.uhf:style/Base.Widget.MaterialComponents.AutoCompleteTextView = 0x7f130116
com.example.uhf:string/msg_login_username_null = 0x7f120261
com.example.uhf:attr/waveShape = 0x7f04051d
com.example.uhf:dimen/m3_comp_divider_thickness = 0x7f07010c
com.example.uhf:dimen/m3_comp_assist_chip_with_icon_icon_size = 0x7f070103
com.example.uhf:color/m3_ref_palette_dynamic_tertiary20 = 0x7f060107
com.example.uhf:dimen/m3_comp_assist_chip_flat_outline_width = 0x7f070102
com.example.uhf:macro/m3_comp_outlined_text_field_focus_input_text_color = 0x7f0d00b9
com.example.uhf:id/tv_total = 0x7f0902cb
com.example.uhf:attr/saturation = 0x7f0403d9
com.example.uhf:dimen/m3_comp_assist_chip_container_height = 0x7f0700ff
com.example.uhf:dimen/m3_chip_icon_size = 0x7f0700fe
com.example.uhf:dimen/m3_comp_slider_stop_indicator_size = 0x7f07018d
com.example.uhf:style/PreferenceFragmentList.Material = 0x7f13016a
com.example.uhf:string/fingerprint_msg_init_fail = 0x7f120191
com.example.uhf:id/title_template = 0x7f0902ae
com.example.uhf:color/m3_ref_palette_dynamic_neutral_variant40 = 0x7f0600dc
com.example.uhf:dimen/m3_chip_checked_hovered_translation_z = 0x7f0700f8
com.example.uhf:string/ul_title_lock_DSFID = 0x7f120492
com.example.uhf:drawable/$mtrl_checkbox_button_checked_unchecked__1 = 0x7f08000e
com.example.uhf:dimen/m3_carousel_small_item_size_min = 0x7f0700f7
com.example.uhf:color/m3_sys_color_dark_primary_container = 0x7f06018a
com.example.uhf:dimen/mtrl_progress_track_thickness = 0x7f0702e6
com.example.uhf:style/Base.Widget.AppCompat.ActionBar.TabView = 0x7f1300cb
com.example.uhf:dimen/m3_comp_radio_button_unselected_hover_state_layer_opacity = 0x7f070170
com.example.uhf:style/Widget.MaterialComponents.TabLayout.Colored = 0x7f130473
com.example.uhf:dimen/m3_card_stroke_width = 0x7f0700f1
com.example.uhf:dimen/m3_card_elevation = 0x7f0700ef
com.example.uhf:dimen/m3_card_dragged_z = 0x7f0700ea
com.example.uhf:dimen/m3_card_disabled_z = 0x7f0700e9
com.example.uhf:layout/m3_alert_dialog_title = 0x7f0c0041
com.example.uhf:dimen/m3_btn_translation_z_hovered = 0x7f0700e8
com.example.uhf:dimen/m3_btn_translation_z_base = 0x7f0700e7
com.example.uhf:dimen/m3_btn_text_btn_padding_right = 0x7f0700e6
com.example.uhf:id/rbUser_filter_wt = 0x7f090229
com.example.uhf:dimen/appcompat_dialog_background_inset = 0x7f070053
com.example.uhf:dimen/abc_dropdownitem_text_padding_right = 0x7f07002b
com.example.uhf:macro/m3_comp_switch_selected_hover_state_layer_color = 0x7f0d0127
com.example.uhf:dimen/m3_btn_padding_top = 0x7f0700e1
com.example.uhf:dimen/m3_btn_padding_left = 0x7f0700df
com.example.uhf:attr/layout_constraintEnd_toEndOf = 0x7f0402a6
com.example.uhf:id/accessibility_custom_action_26 = 0x7f090046
com.example.uhf:dimen/m3_btn_icon_only_default_padding = 0x7f0700d8
com.example.uhf:drawable/$mtrl_checkbox_button_icon_checked_unchecked__2 = 0x7f080013
com.example.uhf:interpolator/m3_sys_motion_easing_emphasized_decelerate = 0x7f0b0009
com.example.uhf:dimen/mtrl_navigation_rail_margin = 0x7f0702d6
com.example.uhf:dimen/m3_btn_disabled_translation_z = 0x7f0700d3
com.example.uhf:string/desfire_title_value_curr = 0x7f120140
com.example.uhf:anim/abc_tooltip_exit = 0x7f01000b
com.example.uhf:color/material_dynamic_tertiary95 = 0x7f06027d
com.example.uhf:string/uhf_msg_offset_error = 0x7f120455
com.example.uhf:dimen/m3_bottomappbar_fab_end_margin = 0x7f0700cd
com.example.uhf:string/start_location = 0x7f12037a
com.example.uhf:color/m3_ref_palette_neutral_variant10 = 0x7f060137
com.example.uhf:color/material_grey_300 = 0x7f060280
com.example.uhf:dimen/m3_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f0700cb
com.example.uhf:string/action_display = 0x7f12003f
com.example.uhf:dimen/m3_bottomappbar_fab_cradle_margin = 0x7f0700ca
com.example.uhf:attr/materialCalendarHeaderCancelButton = 0x7f040303
com.example.uhf:dimen/m3_bottom_sheet_drag_handle_bottom_padding = 0x7f0700c7
com.example.uhf:color/material_slider_inactive_tick_marks_color = 0x7f0602cb
com.example.uhf:dimen/m3_bottom_nav_min_height = 0x7f0700c6
com.example.uhf:string/mtrl_picker_toggle_to_year_selection = 0x7f1202b3
com.example.uhf:dimen/m3_badge_with_text_vertical_offset = 0x7f0700bf
com.example.uhf:string/uhf_gyro_loaction = 0x7f12043e
com.example.uhf:array/fileEndingImage = 0x7f030037
com.example.uhf:dimen/m3_badge_with_text_size = 0x7f0700be
com.example.uhf:style/Widget.MaterialComponents.TimePicker.Display = 0x7f130485
com.example.uhf:dimen/m3_badge_with_text_offset = 0x7f0700bd
com.example.uhf:string/fastID = 0x7f12016e
com.example.uhf:dimen/m3_bottom_nav_item_active_indicator_margin_horizontal = 0x7f0700c2
com.example.uhf:dimen/m3_badge_with_text_horizontal_offset = 0x7f0700bc
com.example.uhf:dimen/m3_back_progress_side_container_max_scale_x_distance_grow = 0x7f0700b5
com.example.uhf:color/material_dynamic_neutral_variant10 = 0x7f06024c
com.example.uhf:dimen/m3_back_progress_main_container_max_translation_y = 0x7f0700b3
com.example.uhf:style/Base.Widget.AppCompat.CompoundButton.CheckBox = 0x7f1300da
com.example.uhf:macro/m3_comp_top_app_bar_small_container_color = 0x7f0d016f
com.example.uhf:attr/startIconDrawable = 0x7f040428
com.example.uhf:dimen/m3_appbar_size_large = 0x7f0700af
com.example.uhf:style/Preference.DropDown.Material = 0x7f13015a
com.example.uhf:array/arraySession = 0x7f030025
com.example.uhf:dimen/abc_list_item_height_small_material = 0x7f070032
com.example.uhf:dimen/m3_appbar_scrim_height_trigger_medium = 0x7f0700ad
com.example.uhf:style/RtlOverlay.Widget.AppCompat.DialogTitle.Icon = 0x7f130171
com.example.uhf:attr/yearTodayStyle = 0x7f04052c
com.example.uhf:style/ShapeAppearanceOverlay.MaterialComponents.Chip = 0x7f1301b7
com.example.uhf:color/m3_tabs_ripple_color_secondary = 0x7f060219
com.example.uhf:dimen/m3_appbar_expanded_title_margin_horizontal = 0x7f0700aa
com.example.uhf:dimen/m3_alert_dialog_title_bottom_margin = 0x7f0700a8
com.example.uhf:color/m3_ref_palette_primary70 = 0x7f06014b
com.example.uhf:dimen/m3_alert_dialog_icon_margin = 0x7f0700a6
com.example.uhf:array/storageArea = 0x7f03003e
com.example.uhf:dimen/m3_alert_dialog_elevation = 0x7f0700a5
com.example.uhf:color/m3_sys_color_dark_surface_container_low = 0x7f060192
com.example.uhf:dimen/m3_fab_translation_z_hovered_focused = 0x7f0701bc
com.example.uhf:id/line3 = 0x7f090188
com.example.uhf:anim/slide_left_out = 0x7f01002e
com.example.uhf:dimen/key_height = 0x7f0700a0
com.example.uhf:dimen/item_touch_helper_swipe_escape_velocity = 0x7f07009f
com.example.uhf:attr/badgeText = 0x7f04005f
com.example.uhf:dimen/item_touch_helper_swipe_escape_max_velocity = 0x7f07009e
com.example.uhf:drawable/ic_call_answer_video_low = 0x7f08009f
com.example.uhf:dimen/hint_pressed_alpha_material_dark = 0x7f07009b
com.example.uhf:dimen/hint_alpha_material_light = 0x7f07009a
com.example.uhf:style/Base.Theme.AppCompat.Light.Dialog.Alert = 0x7f130059
com.example.uhf:attr/colorButtonNormal = 0x7f0400ff
com.example.uhf:dimen/highlight_alpha_material_colored = 0x7f070096
com.example.uhf:string/history_clear_text = 0x7f1201d4
com.example.uhf:styleable/TabItem = 0x7f14009c
com.example.uhf:dimen/notification_main_column_padding_top = 0x7f070320
com.example.uhf:dimen/half_padding = 0x7f070095
com.example.uhf:style/Base.Widget.AppCompat.ListPopupWindow = 0x7f1300eb
com.example.uhf:dimen/fastscroll_minimum_range = 0x7f070094
com.example.uhf:dimen/fastscroll_margin = 0x7f070093
com.example.uhf:string/desfire_title_app_change_pwd_permission = 0x7f12011b
com.example.uhf:id/postLayout = 0x7f090206
com.example.uhf:dimen/fastscroll_default_thickness = 0x7f070092
com.example.uhf:dimen/m3_carousel_extra_small_item_size = 0x7f0700f3
com.example.uhf:string/desfire_msg_get_files_fail = 0x7f12010f
com.example.uhf:dimen/m3_comp_outlined_icon_button_unselected_outline_width = 0x7f070159
com.example.uhf:dimen/design_textinput_caption_translate_y = 0x7f07008f
com.example.uhf:id/etPtr = 0x7f09013a
com.example.uhf:attr/titleTextStyle = 0x7f0404e2
com.example.uhf:dimen/m3_comp_navigation_rail_container_elevation = 0x7f07014c
com.example.uhf:style/Base.Widget.AppCompat.SearchView.ActionBar = 0x7f1300f8
com.example.uhf:array/arrayLinkValue = 0x7f030017
com.example.uhf:dimen/material_clock_number_text_size = 0x7f07022f
com.example.uhf:macro/m3_comp_date_picker_modal_date_label_text_type = 0x7f0d000f
com.example.uhf:id/outward = 0x7f0901f7
com.example.uhf:dimen/design_snackbar_padding_vertical_2lines = 0x7f070089
com.example.uhf:string/clear = 0x7f1200f6
com.example.uhf:color/abc_hint_foreground_material_dark = 0x7f060007
com.example.uhf:attr/transitionEasing = 0x7f040500
com.example.uhf:dimen/design_snackbar_min_width = 0x7f070086
com.example.uhf:id/spFrequency = 0x7f09026a
com.example.uhf:dimen/mtrl_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f07025b
com.example.uhf:dimen/design_snackbar_max_width = 0x7f070085
com.example.uhf:style/Base.Theme.MaterialComponents.DialogWhenLarge = 0x7f130071
com.example.uhf:dimen/m3_fab_corner_size = 0x7f0701bb
com.example.uhf:attr/horizontalOffsetWithText = 0x7f040242
com.example.uhf:dimen/design_snackbar_extra_spacing_horizontal = 0x7f070084
com.example.uhf:attr/navigationContentDescription = 0x7f040370
com.example.uhf:dimen/design_snackbar_action_text_color_alpha = 0x7f070081
com.example.uhf:attr/textureWidth = 0x7f0404b6
com.example.uhf:string/uhf_tag_count = 0x7f12047f
com.example.uhf:color/secondary_text_default_material_dark = 0x7f06032d
com.example.uhf:dimen/m3_comp_radio_button_disabled_selected_icon_opacity = 0x7f07016a
com.example.uhf:dimen/design_snackbar_action_inline_max_width = 0x7f070080
com.example.uhf:macro/m3_comp_radio_button_unselected_hover_state_layer_color = 0x7f0d00e2
com.example.uhf:dimen/design_navigation_item_vertical_padding = 0x7f07007c
com.example.uhf:dimen/abc_control_inset_material = 0x7f070019
com.example.uhf:drawable/notification_bg_normal_pressed = 0x7f0800f5
com.example.uhf:dimen/design_navigation_item_horizontal_padding = 0x7f07007a
com.example.uhf:dimen/mtrl_chip_text_size = 0x7f0702a7
com.example.uhf:color/m3_ref_palette_dynamic_neutral_variant50 = 0x7f0600dd
com.example.uhf:style/ThemeOverlay.MaterialComponents.ActionBar.Primary = 0x7f1302ec
com.example.uhf:attr/defaultValue = 0x7f04017f
com.example.uhf:drawable/$mtrl_checkbox_button_icon_unchecked_checked__1 = 0x7f080019
com.example.uhf:id/TvTagCount = 0x7f090029
com.example.uhf:style/Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f13029a
com.example.uhf:dimen/design_navigation_icon_size = 0x7f070079
com.example.uhf:dimen/design_fab_image_size = 0x7f070072
com.example.uhf:style/Base.Theme.AppCompat.Light.DarkActionBar = 0x7f130057
com.example.uhf:dimen/material_filled_edittext_font_1_3_padding_top = 0x7f07023d
com.example.uhf:dimen/m3_sys_motion_easing_legacy_accelerate_control_x1 = 0x7f070203
com.example.uhf:dimen/m3_alert_dialog_action_bottom_padding = 0x7f0700a2
com.example.uhf:dimen/design_bottom_sheet_peek_height_min = 0x7f07006f
com.example.uhf:dimen/m3_card_elevated_elevation = 0x7f0700ed
com.example.uhf:dimen/mtrl_calendar_pre_l_text_clip_padding = 0x7f070293
com.example.uhf:dimen/design_bottom_sheet_modal_elevation = 0x7f07006e
com.example.uhf:id/leftToRight = 0x7f090185
com.example.uhf:dimen/design_bottom_sheet_elevation = 0x7f07006d
com.example.uhf:style/Widget.MaterialComponents.MaterialCalendar.HeaderDivider = 0x7f130453
com.example.uhf:style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon = 0x7f13017d
com.example.uhf:dimen/design_bottom_navigation_text_size = 0x7f07006c
com.example.uhf:dimen/design_bottom_navigation_margin = 0x7f07006a
com.example.uhf:dimen/m3_badge_vertical_offset = 0x7f0700bb
com.example.uhf:dimen/design_bottom_navigation_active_text_size = 0x7f070063
com.example.uhf:dimen/design_bottom_navigation_active_item_max_width = 0x7f070061
com.example.uhf:id/search_badge = 0x7f09024a
com.example.uhf:string/m1_title_key_value = 0x7f120209
com.example.uhf:dimen/compat_notification_large_icon_max_width = 0x7f07005e
com.example.uhf:string/mtrl_switch_thumb_path_morphing = 0x7f1202b6
com.example.uhf:string/location = 0x7f1201f7
com.example.uhf:attr/clockHandColor = 0x7f0400e6
com.example.uhf:dimen/m3_bottom_nav_item_padding_top = 0x7f0700c5
com.example.uhf:dimen/compat_button_padding_vertical_material = 0x7f07005b
com.example.uhf:style/Widget.MaterialComponents.ProgressIndicator = 0x7f13046c
com.example.uhf:dimen/design_bottom_navigation_item_min_width = 0x7f070068
com.example.uhf:dimen/compat_button_inset_horizontal_material = 0x7f070058
com.example.uhf:dimen/cardview_default_elevation = 0x7f070055
com.example.uhf:string/tvHop = 0x7f12040a
com.example.uhf:attr/closeIconStartPadding = 0x7f0400ed
com.example.uhf:dimen/cardview_compat_inset_shadow = 0x7f070054
com.example.uhf:dimen/abc_text_size_title_material_toolbar = 0x7f070050
com.example.uhf:string/uhf_msg_tab_read_write = 0x7f120471
com.example.uhf:dimen/abc_text_size_subtitle_material_toolbar = 0x7f07004e
com.example.uhf:color/material_dynamic_neutral10 = 0x7f06023f
com.example.uhf:dimen/abc_text_size_menu_material = 0x7f07004b
com.example.uhf:style/Widget.Material3.MaterialCalendar.Day.Today = 0x7f1303c8
com.example.uhf:plurals/mtrl_badge_content_description = 0x7f100000
com.example.uhf:dimen/abc_text_size_large_material = 0x7f070048
com.example.uhf:anim/m3_motion_fade_enter = 0x7f010024
com.example.uhf:attr/hintAnimationEnabled = 0x7f04023b
com.example.uhf:dimen/material_clock_hand_padding = 0x7f07022d
com.example.uhf:dimen/abc_text_size_headline_material = 0x7f070047
com.example.uhf:attr/nestedScrollViewStyle = 0x7f040378
com.example.uhf:attr/colorTertiaryContainer = 0x7f040136
com.example.uhf:color/result_minor_text = 0x7f060321
com.example.uhf:macro/m3_comp_suggestion_chip_label_text_type = 0x7f0d0118
com.example.uhf:color/m3_slider_inactive_track_color_legacy = 0x7f060171
com.example.uhf:color/sliding_menu_item_text_color_pressed = 0x7f060336
com.example.uhf:dimen/abc_text_size_display_2_material = 0x7f070044
com.example.uhf:dimen/m3_back_progress_bottom_container_max_scale_x_distance = 0x7f0700b1
com.example.uhf:color/m3_ref_palette_dynamic_neutral24 = 0x7f0600c1
com.example.uhf:dimen/mtrl_calendar_navigation_bottom_padding = 0x7f070290
com.example.uhf:string/b14443_msg_data = 0x7f120062
com.example.uhf:attr/closeIconEnabled = 0x7f0400ea
com.example.uhf:dimen/abc_text_size_caption_material = 0x7f070042
com.example.uhf:id/material_textinput_timepicker = 0x7f0901a9
com.example.uhf:dimen/abc_text_size_body_1_material = 0x7f07003f
com.example.uhf:dimen/abc_star_medium = 0x7f07003c
com.example.uhf:string/switch_cam = 0x7f120386
com.example.uhf:layout/material_timepicker_textinput_display = 0x7f0c0051
com.example.uhf:attr/state_error = 0x7f040431
com.example.uhf:dimen/abc_star_big = 0x7f07003b
com.example.uhf:dimen/abc_select_dialog_padding_start_material = 0x7f07003a
com.example.uhf:color/material_dynamic_neutral40 = 0x7f060243
com.example.uhf:style/TextAppearance.MaterialComponents.Headline1 = 0x7f130228
com.example.uhf:dimen/m3_badge_horizontal_offset = 0x7f0700b8
com.example.uhf:drawable/ic_m3_chip_checked_circle = 0x7f0800a9
com.example.uhf:attr/cardCornerRadius = 0x7f0400a2
com.example.uhf:dimen/abc_seekbar_track_progress_height_material = 0x7f070039
com.example.uhf:color/design_default_color_surface = 0x7f060051
com.example.uhf:attr/colorPrimaryContainer = 0x7f04011e
com.example.uhf:dimen/m3_btn_inset = 0x7f0700dc
com.example.uhf:dimen/abc_search_view_preferred_width = 0x7f070037
com.example.uhf:dimen/design_bottom_navigation_height = 0x7f070065
com.example.uhf:string/tvTag = 0x7f120426
com.example.uhf:attr/pivotAnchor = 0x7f0403a1
com.example.uhf:id/cb_filter_deact = 0x7f0900cf
com.example.uhf:dimen/abc_list_item_padding_horizontal_material = 0x7f070033
com.example.uhf:string/material_hour_24h_suffix = 0x7f120222
com.example.uhf:style/Widget.Material3.Slider.Label = 0x7f1303fa
com.example.uhf:dimen/abc_floating_window_z = 0x7f07002f
com.example.uhf:string/msg_disable_succ = 0x7f12024a
com.example.uhf:layout/material_time_input = 0x7f0c004e
com.example.uhf:id/llButton = 0x7f09018d
com.example.uhf:dimen/abc_edit_text_inset_top_material = 0x7f07002e
com.example.uhf:anim/abc_popup_enter = 0x7f010003
com.example.uhf:style/Base.Widget.AppCompat.DrawerArrowToggle = 0x7f1300dd
com.example.uhf:attr/badgeTextColor = 0x7f040061
com.example.uhf:dimen/compat_control_corner_material = 0x7f07005c
com.example.uhf:color/m3_navigation_item_ripple_color = 0x7f0600b0
com.example.uhf:dimen/mtrl_btn_snackbar_margin_horizontal = 0x7f07026f
com.example.uhf:style/TextAppearance.Material3.SearchView = 0x7f13021d
com.example.uhf:attr/windowFixedWidthMajor = 0x7f040525
com.example.uhf:dimen/abc_dropdownitem_icon_width = 0x7f070029
com.example.uhf:color/m3_ref_palette_dynamic_primary40 = 0x7f0600ef
com.example.uhf:dimen/abc_disabled_alpha_material_light = 0x7f070028
com.example.uhf:id/cbBlock7 = 0x7f0900c0
com.example.uhf:id/actionDown = 0x7f090053
com.example.uhf:attr/guidelineUseRtl = 0x7f04022d
com.example.uhf:dimen/text_size_24 = 0x7f070341
com.example.uhf:color/material_dynamic_neutral_variant100 = 0x7f06024d
com.example.uhf:attr/layout_constraintWidth = 0x7f0402c2
com.example.uhf:dimen/m3_chip_dragged_translation_z = 0x7f0700fb
com.example.uhf:string/button_share_bookmark = 0x7f1200da
com.example.uhf:dimen/abc_disabled_alpha_material_dark = 0x7f070027
com.example.uhf:attr/constraintRotate = 0x7f04013b
com.example.uhf:attr/startIconScaleType = 0x7f04042a
com.example.uhf:color/mtrl_indicator_text_color = 0x7f0602ed
com.example.uhf:dimen/abc_dialog_min_width_minor = 0x7f070023
com.example.uhf:id/centerCrop = 0x7f0900d4
com.example.uhf:attr/addElevationShadow = 0x7f04002a
com.example.uhf:dimen/abc_dialog_list_padding_top_no_title = 0x7f070021
com.example.uhf:anim/slide_right_in = 0x7f01002f
com.example.uhf:color/m3_ref_palette_dynamic_tertiary10 = 0x7f060105
com.example.uhf:dimen/abc_dialog_fixed_width_minor = 0x7f07001f
com.example.uhf:style/Base.ThemeOverlay.AppCompat.ActionBar = 0x7f13007e
com.example.uhf:string/mtrl_chip_close_icon_content_description = 0x7f12028c
com.example.uhf:string/ap_title_wifi_size = 0x7f120055
com.example.uhf:attr/panelMenuListTheme = 0x7f040392
com.example.uhf:layout/abc_screen_toolbar = 0x7f0c0017
com.example.uhf:dimen/abc_dialog_fixed_width_major = 0x7f07001e
com.example.uhf:color/m3_ref_palette_dynamic_primary70 = 0x7f0600f2
com.example.uhf:dimen/abc_dialog_fixed_height_minor = 0x7f07001d
com.example.uhf:string/save = 0x7f12035f
com.example.uhf:layout/abc_screen_simple_overlay_action_mode = 0x7f0c0016
com.example.uhf:dimen/mtrl_btn_hovered_z = 0x7f070264
com.example.uhf:attr/motionEffect_translationY = 0x7f040364
com.example.uhf:string/http_status_code_error = 0x7f1201db
com.example.uhf:dimen/abc_button_padding_vertical_material = 0x7f070015
com.example.uhf:dimen/text_size_12 = 0x7f070335
com.example.uhf:color/m3_ref_palette_tertiary100 = 0x7f06015f
com.example.uhf:id/quit = 0x7f09020d
com.example.uhf:id/homeAsUp = 0x7f090168
com.example.uhf:color/m3_text_button_foreground_color_selector = 0x7f06021d
com.example.uhf:dimen/m3_comp_switch_disabled_track_opacity = 0x7f070196
com.example.uhf:string/title_about_device_release = 0x7f120399
com.example.uhf:dimen/abc_button_padding_horizontal_material = 0x7f070014
com.example.uhf:dimen/abc_button_inset_horizontal_material = 0x7f070012
com.example.uhf:dimen/abc_alert_dialog_button_bar_height = 0x7f070010
com.example.uhf:string/btCancel = 0x7f1200aa
com.example.uhf:macro/m3_comp_switch_selected_focus_handle_color = 0x7f0d0120
com.example.uhf:dimen/abc_action_button_min_width_overflow_material = 0x7f07000f
com.example.uhf:color/result_image_border = 0x7f060320
com.example.uhf:style/Base.Theme.AppCompat.Light.Dialog = 0x7f130058
com.example.uhf:dimen/abc_action_button_min_height_material = 0x7f07000d
com.example.uhf:string/fingerprint_title_local_num = 0x7f1201aa
com.example.uhf:string/tvTagUii_Lock = 0x7f12042d
com.example.uhf:color/m3_radiobutton_ripple_tint = 0x7f0600b8
com.example.uhf:dimen/mtrl_progress_circular_size_small = 0x7f0702e1
com.example.uhf:dimen/abc_action_bar_stacked_max_height = 0x7f070009
com.example.uhf:string/preferences_remember_duplicates_summary = 0x7f120301
com.example.uhf:dimen/abc_action_bar_overflow_padding_start_material = 0x7f070008
com.example.uhf:id/rbEPC = 0x7f090211
com.example.uhf:string/fingerprint_msg_set_PacketSize_succ = 0x7f12019b
com.example.uhf:dimen/abc_action_bar_default_padding_start_material = 0x7f070004
com.example.uhf:dimen/abc_action_bar_default_padding_end_material = 0x7f070003
com.example.uhf:dimen/abc_action_bar_content_inset_material = 0x7f070000
com.example.uhf:dimen/m3_comp_time_picker_period_selector_outline_width = 0x7f0701a8
com.example.uhf:style/Base.Widget.MaterialComponents.TextInputLayout = 0x7f130122
com.example.uhf:color/yellow = 0x7f060348
com.example.uhf:id/notification_background = 0x7f0901e2
com.example.uhf:color/m3_dynamic_hint_foreground = 0x7f0600a0
com.example.uhf:color/tooltip_background_light = 0x7f060341
com.example.uhf:color/switch_thumb_normal_material_light = 0x7f06033e
com.example.uhf:attr/backgroundSplit = 0x7f040055
com.example.uhf:color/switch_thumb_normal_material_dark = 0x7f06033d
com.example.uhf:macro/m3_comp_time_picker_period_selector_selected_hover_state_layer_color = 0x7f0d0157
com.example.uhf:string/lp_title_gyroscope = 0x7f120201
com.example.uhf:attr/colorTertiary = 0x7f040135
com.example.uhf:color/status_text = 0x7f060338
com.example.uhf:color/sliding_menu_title_text_color = 0x7f060337
com.example.uhf:dimen/m3_comp_progress_indicator_stop_indicator_size = 0x7f070168
com.example.uhf:color/sliding_menu_item_text_color = 0x7f060335
com.example.uhf:style/Animation.Material3.SideSheetDialog.Right = 0x7f13000b
com.example.uhf:id/listView_frequency = 0x7f09018b
com.example.uhf:macro/m3_comp_radio_button_unselected_icon_color = 0x7f0d00e3
com.example.uhf:dimen/mtrl_alert_dialog_background_inset_top = 0x7f07024e
com.example.uhf:string/fingerprint_msg_page_id_not_null = 0x7f120195
com.example.uhf:id/hide_ime_id = 0x7f090165
com.example.uhf:color/sliding_menu_back = 0x7f060332
com.example.uhf:array/allname = 0x7f030004
com.example.uhf:macro/m3_comp_dialog_supporting_text_color = 0x7f0d0026
com.example.uhf:id/dialog_button = 0x7f090104
com.example.uhf:color/share_text = 0x7f060331
com.example.uhf:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Centered = 0x7f1302ff
com.example.uhf:attr/dependency = 0x7f040182
com.example.uhf:string/mtrl_picker_invalid_format_example = 0x7f12029c
com.example.uhf:dimen/m3_bottom_sheet_elevation = 0x7f0700c8
com.example.uhf:attr/thumbWidth = 0x7f0404c7
com.example.uhf:color/secondary_text_disabled_material_light = 0x7f060330
com.example.uhf:attr/maxLines = 0x7f04032c
com.example.uhf:string/msg_barcode_ck = 0x7f12023d
com.example.uhf:string/title_about_imei = 0x7f12039b
com.example.uhf:string/btn_light_stop = 0x7f1200c5
com.example.uhf:color/m3_dark_default_color_secondary_text = 0x7f060092
com.example.uhf:color/m3_switch_thumb_tint = 0x7f060174
com.example.uhf:color/sbc_page_number_text = 0x7f06032b
com.example.uhf:string/nfc_title_reader = 0x7f1202d7
com.example.uhf:attr/floatingActionButtonSmallStyle = 0x7f040200
com.example.uhf:color/sbc_layout_view = 0x7f060329
com.example.uhf:dimen/m3_ripple_default_alpha = 0x7f0701da
com.example.uhf:dimen/mtrl_progress_circular_size_extra_small = 0x7f0702df
com.example.uhf:dimen/m3_chip_corner_size = 0x7f0700f9
com.example.uhf:attr/layout_scrollFlags = 0x7f0402d6
com.example.uhf:color/material_personalized_color_text_hint_foreground_inverse = 0x7f0602bf
com.example.uhf:color/sbc_header_text = 0x7f060327
com.example.uhf:color/ripple_material_light = 0x7f060326
com.example.uhf:color/result_text = 0x7f060323
com.example.uhf:color/m3_ref_palette_primary40 = 0x7f060148
com.example.uhf:color/primary_text_disabled_material_light = 0x7f060317
com.example.uhf:color/primary_text_default_material_light = 0x7f060315
com.example.uhf:array/arrayFreHop_us = 0x7f030010
com.example.uhf:color/primary_material_light = 0x7f060313
com.example.uhf:string/rfid_mgs_error_nopwd = 0x7f120337
com.example.uhf:attr/preferenceCategoryTitleTextColor = 0x7f0403af
com.example.uhf:color/primary_material_dark = 0x7f060312
com.example.uhf:attr/shapeCornerFamily = 0x7f0403f7
com.example.uhf:color/m3_ref_palette_error0 = 0x7f060111
com.example.uhf:color/mtrl_textinput_focused_box_stroke_color = 0x7f060308
com.example.uhf:style/Widget.AppCompat.ActionButton = 0x7f130317
com.example.uhf:attr/switchTextAppearance = 0x7f040452
com.example.uhf:color/m3_sys_color_dynamic_on_tertiary_fixed = 0x7f0601e0
com.example.uhf:color/mtrl_tabs_icon_color_selector = 0x7f060300
com.example.uhf:string/tv_phase = 0x7f120433
com.example.uhf:string/button_add_contact = 0x7f1200c7
com.example.uhf:id/navigation_bar_item_labels_group = 0x7f0901d6
com.example.uhf:color/mtrl_switch_track_tint = 0x7f0602fe
com.example.uhf:color/mtrl_switch_thumb_icon_tint = 0x7f0602fb
com.example.uhf:attr/state_lifted = 0x7f040434
com.example.uhf:color/mtrl_outlined_stroke_color = 0x7f0602f8
com.example.uhf:color/mtrl_outlined_icon_tint = 0x7f0602f7
com.example.uhf:style/Base.Theme.MaterialComponents.Light.Dialog = 0x7f130076
com.example.uhf:dimen/m3_comp_elevated_card_icon_size = 0x7f070110
com.example.uhf:style/Widget.Design.ScrimInsetsFrameLayout = 0x7f130363
com.example.uhf:attr/thickness = 0x7f0404b8
com.example.uhf:color/mtrl_navigation_item_icon_tint = 0x7f0602f3
com.example.uhf:color/mtrl_navigation_bar_colored_item_tint = 0x7f0602ee
com.example.uhf:attr/closeIconVisible = 0x7f0400ef
com.example.uhf:macro/m3_comp_switch_selected_hover_track_color = 0x7f0d0128
com.example.uhf:attr/queryHint = 0x7f0403c3
com.example.uhf:string/uhf_msg_read_frequency_succ = 0x7f120459
com.example.uhf:string/desfire_title_modify = 0x7f120131
com.example.uhf:drawable/abc_list_selector_holo_light = 0x7f080058
com.example.uhf:dimen/m3_comp_switch_disabled_unselected_icon_opacity = 0x7f070198
com.example.uhf:color/mtrl_filled_stroke_color = 0x7f0602ec
com.example.uhf:color/m3_sys_color_light_primary = 0x7f0601fb
com.example.uhf:color/mtrl_filled_icon_tint = 0x7f0602eb
com.example.uhf:color/mtrl_filled_background_color = 0x7f0602ea
com.example.uhf:string/title_activity_fingerprint = 0x7f1203ae
com.example.uhf:id/material_timepicker_container = 0x7f0901ab
com.example.uhf:dimen/m3_card_elevated_disabled_z = 0x7f0700eb
com.example.uhf:color/mtrl_fab_ripple_color = 0x7f0602e9
com.example.uhf:macro/m3_comp_filter_chip_container_shape = 0x7f0d0057
com.example.uhf:color/m3_sys_color_dynamic_light_surface_container_lowest = 0x7f0601d7
com.example.uhf:color/mtrl_fab_bg_color_selector = 0x7f0602e7
com.example.uhf:color/mtrl_choice_chip_ripple_color = 0x7f0602e4
com.example.uhf:attr/paddingEnd = 0x7f04038a
com.example.uhf:string/btn_light_single = 0x7f1200c4
com.example.uhf:color/mtrl_choice_chip_background_color = 0x7f0602e3
com.example.uhf:color/mtrl_chip_text_color = 0x7f0602e2
com.example.uhf:color/mtrl_calendar_selected_range = 0x7f0602dc
com.example.uhf:color/mtrl_calendar_item_stroke_color = 0x7f0602db
com.example.uhf:attr/telltales_tailColor = 0x7f040473
com.example.uhf:attr/divider = 0x7f04018e
com.example.uhf:drawable/webtext = 0x7f08010d
com.example.uhf:color/mtrl_btn_text_btn_ripple_color = 0x7f0602d7
com.example.uhf:style/ThemeOverlay.Material3.Button.TonalButton = 0x7f1302bc
com.example.uhf:color/mtrl_btn_text_btn_bg_color_selector = 0x7f0602d6
com.example.uhf:style/Base.TextAppearance.Material3.Search = 0x7f130047
com.example.uhf:color/mtrl_btn_ripple_color = 0x7f0602d4
com.example.uhf:color/mtrl_btn_bg_color_selector = 0x7f0602d3
com.example.uhf:dimen/m3_alert_dialog_action_top_padding = 0x7f0700a3
com.example.uhf:style/Widget.Material3.NavigationRailView = 0x7f1303e6
com.example.uhf:color/m3_ref_palette_dynamic_tertiary90 = 0x7f06010e
com.example.uhf:color/material_dynamic_secondary99 = 0x7f060271
com.example.uhf:layout/design_text_input_start_icon = 0x7f0c002d
com.example.uhf:string/getQTParams_succ = 0x7f1201bd
com.example.uhf:array/allicon = 0x7f030003
com.example.uhf:color/material_timepicker_button_stroke = 0x7f0602cf
com.example.uhf:attr/behavior_fitToContents = 0x7f040072
com.example.uhf:color/material_slider_active_tick_marks_color = 0x7f0602c8
com.example.uhf:color/material_personalized_hint_foreground_inverse = 0x7f0602c5
com.example.uhf:color/material_personalized_hint_foreground = 0x7f0602c4
com.example.uhf:attr/onHide = 0x7f04037e
com.example.uhf:attr/listPreferredItemHeight = 0x7f0402e8
com.example.uhf:color/m3_sys_color_dark_on_error_container = 0x7f06017e
com.example.uhf:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__1 = 0x7f08001c
com.example.uhf:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f1302f0
com.example.uhf:color/material_personalized_color_text_secondary_and_tertiary_inverse = 0x7f0602c2
com.example.uhf:dimen/notification_content_margin_start = 0x7f07031d
com.example.uhf:drawable/material_ic_keyboard_arrow_next_black_24dp = 0x7f0800c3
com.example.uhf:layout/mtrl_layout_snackbar = 0x7f0c0063
com.example.uhf:macro/m3_comp_assist_chip_label_text_type = 0x7f0d0001
com.example.uhf:color/material_personalized_color_text_primary_inverse_disable_only = 0x7f0602c1
com.example.uhf:dimen/abc_text_size_small_material = 0x7f07004c
com.example.uhf:style/Widget.MaterialComponents.TimePicker.ImageButton.ShapeAppearance = 0x7f13048b
com.example.uhf:dimen/highlight_alpha_material_dark = 0x7f070097
com.example.uhf:dimen/m3_sys_state_pressed_state_layer_opacity = 0x7f070222
com.example.uhf:string/mtrl_picker_cancel = 0x7f120294
com.example.uhf:style/Base.Theme.Material3.Light.Dialog = 0x7f130065
com.example.uhf:attr/dialogTitle = 0x7f04018b
com.example.uhf:attr/materialIconButtonFilledTonalStyle = 0x7f040319
com.example.uhf:id/action_menu_divider = 0x7f090064
com.example.uhf:string/mtrl_picker_day_of_week_column_header = 0x7f120299
com.example.uhf:dimen/m3_back_progress_side_container_max_scale_x_distance_shrink = 0x7f0700b6
com.example.uhf:color/mtrl_chip_close_icon_tint = 0x7f0602e0
com.example.uhf:color/material_personalized_color_tertiary_container = 0x7f0602be
com.example.uhf:attr/materialSearchViewToolbarStyle = 0x7f040320
com.example.uhf:style/Widget.MaterialComponents.Button.TextButton = 0x7f13042c
com.example.uhf:style/Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f130295
com.example.uhf:color/design_dark_default_color_primary_variant = 0x7f060041
com.example.uhf:color/material_personalized_color_surface_variant = 0x7f0602bc
com.example.uhf:string/title_activity_a1443 = 0x7f1203a2
com.example.uhf:attr/extraMultilineHeightEnabled = 0x7f0401e5
com.example.uhf:color/material_personalized_color_surface_dim = 0x7f0602ba
com.example.uhf:style/Widget.AppCompat.CompoundButton.CheckBox = 0x7f130325
com.example.uhf:color/material_personalized_color_surface_container_lowest = 0x7f0602b9
com.example.uhf:style/TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f1301de
com.example.uhf:color/material_personalized_color_surface_container_low = 0x7f0602b8
com.example.uhf:color/material_personalized_color_surface_container_highest = 0x7f0602b7
com.example.uhf:attr/stackFromEnd = 0x7f040424
com.example.uhf:color/m3_sys_color_light_surface_dim = 0x7f060206
com.example.uhf:drawable/$mtrl_checkbox_button_icon_checked_unchecked__0 = 0x7f080011
com.example.uhf:color/material_personalized_color_surface_container = 0x7f0602b5
com.example.uhf:style/Widget.MaterialComponents.ActionMode = 0x7f130417
com.example.uhf:color/m3_sys_color_dynamic_light_on_surface_variant = 0x7f0601c8
com.example.uhf:style/Widget.Material3.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f1303d1
com.example.uhf:color/material_personalized_color_surface_bright = 0x7f0602b4
com.example.uhf:drawable/mtrl_switch_track = 0x7f0800eb
com.example.uhf:attr/subheaderColor = 0x7f04043c
com.example.uhf:color/material_dynamic_primary90 = 0x7f060262
com.example.uhf:color/material_personalized_color_surface = 0x7f0602b3
com.example.uhf:attr/dragScale = 0x7f040197
com.example.uhf:color/material_personalized_color_secondary_text = 0x7f0602b1
com.example.uhf:attr/ratingBarStyleIndicator = 0x7f0403c8
com.example.uhf:dimen/m3_comp_extended_fab_primary_focus_container_elevation = 0x7f070113
com.example.uhf:color/material_personalized_color_secondary_container = 0x7f0602b0
com.example.uhf:color/material_personalized_color_on_tertiary_container = 0x7f0602a7
com.example.uhf:color/material_timepicker_button_background = 0x7f0602ce
com.example.uhf:color/material_personalized_color_on_surface_variant = 0x7f0602a5
com.example.uhf:string/new_data_toast_message = 0x7f1202cd
com.example.uhf:color/material_personalized_color_on_primary_container = 0x7f0602a0
com.example.uhf:style/TextAppearance.M3.Sys.Typescale.HeadlineSmall = 0x7f130206
com.example.uhf:attr/thumbElevation = 0x7f0404ba
com.example.uhf:color/m3_sys_color_light_inverse_on_surface = 0x7f0601eb
com.example.uhf:id/mini = 0x7f0901b3
com.example.uhf:drawable/rb_bg = 0x7f080101
com.example.uhf:color/material_personalized_color_on_primary = 0x7f06029f
com.example.uhf:color/material_personalized_color_on_error = 0x7f06029d
com.example.uhf:string/led_msg_open_fail = 0x7f1201e6
com.example.uhf:color/material_personalized_color_on_surface = 0x7f0602a3
com.example.uhf:color/material_personalized_color_control_normal = 0x7f060299
com.example.uhf:style/Widget.Material3.SearchView.Toolbar = 0x7f1303f4
com.example.uhf:string/wifi_type_label = 0x7f1204b7
com.example.uhf:color/material_slider_inactive_track_color = 0x7f0602cc
com.example.uhf:string/title_ping_uploadresult = 0x7f1203e8
com.example.uhf:color/material_personalized_color_control_highlight = 0x7f060298
com.example.uhf:dimen/material_input_text_to_prefix_suffix_padding = 0x7f070245
com.example.uhf:color/red_qian = 0x7f06031f
com.example.uhf:color/m3_ref_palette_dynamic_neutral20 = 0x7f0600bf
com.example.uhf:color/material_on_surface_stroke = 0x7f060293
com.example.uhf:string/abc_capital_off = 0x7f120027
com.example.uhf:color/material_on_surface_emphasis_high_type = 0x7f060291
com.example.uhf:string/msg_sbc_results = 0x7f120276
com.example.uhf:color/material_on_surface_disabled = 0x7f060290
com.example.uhf:attr/colorControlActivated = 0x7f040101
com.example.uhf:attr/textAppearanceLabelSmall = 0x7f04048c
com.example.uhf:string/er_dsoft_get_ParamNum = 0x7f12015c
com.example.uhf:id/skipped = 0x7f090262
com.example.uhf:dimen/mtrl_calendar_header_divider_thickness = 0x7f070285
com.example.uhf:style/ThemeOverlay.Material3.FloatingActionButton.Surface = 0x7f1302cf
com.example.uhf:dimen/m3_divider_heavy_thickness = 0x7f0701b3
com.example.uhf:color/m3_sys_color_dynamic_dark_surface_container_highest = 0x7f0601b3
com.example.uhf:dimen/m3_comp_sheet_side_docked_standard_container_elevation = 0x7f070185
com.example.uhf:attr/cornerFamily = 0x7f040156
com.example.uhf:style/Base.V14.Theme.Material3.Light.SideSheetDialog = 0x7f130095
com.example.uhf:string/bluetooth_msg_connection_failed = 0x7f120099
com.example.uhf:dimen/m3_bottomappbar_horizontal_padding = 0x7f0700cf
com.example.uhf:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Surface = 0x7f1303ae
com.example.uhf:color/lightblue = 0x7f060078
com.example.uhf:color/material_on_primary_emphasis_high_type = 0x7f06028e
com.example.uhf:dimen/mtrl_bottomappbar_height = 0x7f07025d
com.example.uhf:style/Widget.MaterialComponents.Toolbar = 0x7f13048c
com.example.uhf:drawable/$mtrl_checkbox_button_checked_unchecked__0 = 0x7f08000d
com.example.uhf:color/grayslate = 0x7f06006b
com.example.uhf:dimen/m3_alert_dialog_icon_size = 0x7f0700a7
com.example.uhf:color/material_on_background_emphasis_high_type = 0x7f06028b
com.example.uhf:attr/thumbStrokeColor = 0x7f0404c1
com.example.uhf:animator/mtrl_extended_fab_state_list_animator = 0x7f02001d
com.example.uhf:color/material_on_background_disabled = 0x7f06028a
com.example.uhf:color/material_harmonized_color_on_error_container = 0x7f060289
com.example.uhf:id/cbBlock11 = 0x7f0900b5
com.example.uhf:id/design_menu_item_text = 0x7f090102
com.example.uhf:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar = 0x7f130135
com.example.uhf:color/material_harmonized_color_on_error = 0x7f060288
com.example.uhf:color/material_grey_900 = 0x7f060285
com.example.uhf:anim/btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011
com.example.uhf:color/m3_ref_palette_tertiary90 = 0x7f060167
com.example.uhf:color/material_grey_850 = 0x7f060284
com.example.uhf:attr/errorIconTint = 0x7f0401ca
com.example.uhf:string/b14443_msg_uid_err = 0x7f12006a
com.example.uhf:macro/m3_comp_date_picker_modal_container_shape = 0x7f0d000e
com.example.uhf:dimen/mtrl_alert_dialog_background_inset_end = 0x7f07024c
com.example.uhf:color/material_grey_800 = 0x7f060283
com.example.uhf:string/fingerprint_btn_identification = 0x7f12017a
com.example.uhf:layout/mtrl_alert_dialog_title = 0x7f0c0054
com.example.uhf:attr/shapeAppearanceLargeComponent = 0x7f0403f3
com.example.uhf:color/material_grey_600 = 0x7f060282
com.example.uhf:macro/m3_comp_navigation_drawer_inactive_pressed_icon_color = 0x7f0d008e
com.example.uhf:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f130039
com.example.uhf:color/material_dynamic_tertiary60 = 0x7f060279
com.example.uhf:id/gone = 0x7f09015d
com.example.uhf:string/msg_sure = 0x7f12027d
com.example.uhf:attr/preferenceFragmentListStyle = 0x7f0403b1
com.example.uhf:color/notification_icon_bg_color = 0x7f06030b
com.example.uhf:color/design_box_stroke_color = 0x7f060037
com.example.uhf:color/material_dynamic_tertiary30 = 0x7f060276
com.example.uhf:color/material_dynamic_tertiary20 = 0x7f060275
com.example.uhf:drawable/menu_bg_selected = 0x7f0800c8
com.example.uhf:string/republish_tweet = 0x7f120323
com.example.uhf:style/Widget.AppCompat.ActionBar.TabView = 0x7f130316
com.example.uhf:drawable/design_password_eye = 0x7f080096
com.example.uhf:dimen/m3_comp_filled_button_container_elevation = 0x7f070127
com.example.uhf:color/material_dynamic_secondary95 = 0x7f060270
com.example.uhf:attr/paddingRightSystemWindowInsets = 0x7f04038c
com.example.uhf:styleable/FragmentContainerView = 0x7f140040
com.example.uhf:color/material_dynamic_secondary90 = 0x7f06026f
com.example.uhf:id/toggle = 0x7f0902af
com.example.uhf:style/Widget.Material3.Button.TonalButton.Icon = 0x7f13038b
com.example.uhf:color/red2 = 0x7f06031b
com.example.uhf:attr/preserveIconSpacing = 0x7f0403ba
com.example.uhf:string/nfc_msg_write_exception = 0x7f1202d4
com.example.uhf:color/material_dynamic_secondary70 = 0x7f06026d
com.example.uhf:attr/onStateTransition = 0x7f040382
com.example.uhf:color/material_dynamic_secondary60 = 0x7f06026c
com.example.uhf:id/ignore = 0x7f09016f
com.example.uhf:attr/colorAccent = 0x7f0400fd
com.example.uhf:color/material_dynamic_secondary30 = 0x7f060269
com.example.uhf:dimen/network_list_description_size = 0x7f070314
com.example.uhf:string/load_full = 0x7f1201f4
com.example.uhf:dimen/m3_comp_date_picker_modal_date_today_container_outline_width = 0x7f070109
com.example.uhf:string/China_Standard_920_925MHz = 0x7f120003
com.example.uhf:color/material_dynamic_secondary10 = 0x7f060266
com.example.uhf:string/xml_parser_failed = 0x7f1204ba
com.example.uhf:dimen/abc_dialog_title_divider_material = 0x7f070026
com.example.uhf:dimen/abc_list_item_height_large_material = 0x7f070030
com.example.uhf:color/m3_ref_palette_dynamic_primary10 = 0x7f0600eb
com.example.uhf:color/material_dynamic_primary60 = 0x7f06025f
com.example.uhf:attr/chipSpacingHorizontal = 0x7f0400d3
com.example.uhf:color/material_dynamic_primary40 = 0x7f06025d
com.example.uhf:styleable/MaterialAlertDialog = 0x7f140053
com.example.uhf:attr/badgeHeight = 0x7f04005a
com.example.uhf:color/material_dynamic_primary20 = 0x7f06025b
com.example.uhf:color/material_dynamic_primary10 = 0x7f060259
com.example.uhf:color/material_dynamic_primary0 = 0x7f060258
com.example.uhf:dimen/mtrl_calendar_action_confirm_button_min_width = 0x7f070276
com.example.uhf:macro/m3_comp_date_picker_modal_date_today_label_text_color = 0x7f0d0013
com.example.uhf:animator/mtrl_extended_fab_show_motion_spec = 0x7f02001c
com.example.uhf:color/material_dynamic_neutral_variant80 = 0x7f060254
com.example.uhf:string/call_notification_answer_video_action = 0x7f1200e4
com.example.uhf:color/material_dynamic_neutral_variant20 = 0x7f06024e
com.example.uhf:attr/fastScrollEnabled = 0x7f0401ef
com.example.uhf:attr/textInputOutlinedDenseStyle = 0x7f0404a9
com.example.uhf:color/material_dynamic_neutral_variant0 = 0x7f06024b
com.example.uhf:color/m3_ref_palette_dynamic_primary90 = 0x7f0600f4
com.example.uhf:color/material_personalized_color_outline = 0x7f0602a8
com.example.uhf:color/material_dynamic_neutral99 = 0x7f06024a
com.example.uhf:color/material_dynamic_neutral95 = 0x7f060249
com.example.uhf:id/cradle = 0x7f0900f0
com.example.uhf:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout.Fullscreen = 0x7f130455
com.example.uhf:id/accessibility_custom_action_17 = 0x7f09003c
com.example.uhf:color/material_dynamic_neutral30 = 0x7f060242
com.example.uhf:color/m3_ref_palette_dynamic_neutral4 = 0x7f0600c3
com.example.uhf:dimen/highlight_alpha_material_light = 0x7f070098
com.example.uhf:string/btWrite = 0x7f1200c1
com.example.uhf:color/material_dynamic_neutral20 = 0x7f060241
com.example.uhf:style/TextAppearance.M3.Sys.Typescale.BodyLarge = 0x7f1301fe
com.example.uhf:dimen/mtrl_low_ripple_focused_alpha = 0x7f0702c4
com.example.uhf:color/material_dynamic_neutral100 = 0x7f060240
com.example.uhf:color/design_dark_default_color_on_surface = 0x7f06003e
com.example.uhf:color/material_dynamic_color_light_on_error = 0x7f06023c
com.example.uhf:color/material_dynamic_color_light_error_container = 0x7f06023b
com.example.uhf:string/desfire_title_create = 0x7f120126
com.example.uhf:string/material_motion_easing_accelerated = 0x7f120227
com.example.uhf:id/decor_content_parent = 0x7f0900fb
com.example.uhf:attr/percentWidth = 0x7f04039c
com.example.uhf:color/material_dynamic_color_light_error = 0x7f06023a
com.example.uhf:style/Base.Widget.AppCompat.Spinner = 0x7f1300fb
com.example.uhf:color/sliding_menu_item = 0x7f060333
com.example.uhf:string/action_set = 0x7f120045
com.example.uhf:color/material_dynamic_color_dark_on_error = 0x7f060238
com.example.uhf:color/material_divider_color = 0x7f060235
com.example.uhf:color/material_deep_teal_500 = 0x7f060234
com.example.uhf:drawable/abc_list_focused_holo = 0x7f08004f
com.example.uhf:color/m3_sys_color_dynamic_light_tertiary_container = 0x7f0601db
com.example.uhf:attr/contrast = 0x7f040152
com.example.uhf:color/material_cursor_color = 0x7f060232
com.example.uhf:string/btSetPrar = 0x7f1200bd
com.example.uhf:attr/colorSurfaceContainerHighest = 0x7f04012e
com.example.uhf:dimen/material_filled_edittext_font_1_3_padding_bottom = 0x7f07023c
com.example.uhf:color/material_blue_grey_950 = 0x7f060231
com.example.uhf:string/OAUTH_AccessToken_SXPIRED = 0x7f120012
com.example.uhf:attr/minTouchTargetSize = 0x7f040339
com.example.uhf:color/material_blue_grey_900 = 0x7f060230
com.example.uhf:color/m3_tonal_button_ripple_color_selector = 0x7f06022e
com.example.uhf:attr/buttonTint = 0x7f04009f
com.example.uhf:color/result_points = 0x7f060322
com.example.uhf:attr/fabCradleMargin = 0x7f0401ea
com.example.uhf:color/m3_timepicker_time_input_stroke_color = 0x7f06022d
com.example.uhf:color/m3_timepicker_secondary_text_button_ripple_color = 0x7f06022b
com.example.uhf:attr/layout_constraintTop_toBottomOf = 0x7f0402bd
com.example.uhf:id/fullscreen_header = 0x7f09015a
com.example.uhf:attr/switchPadding = 0x7f04044e
com.example.uhf:dimen/m3_comp_badge_large_size = 0x7f070104
com.example.uhf:attr/titleCentered = 0x7f0404d5
com.example.uhf:dimen/clock_face_margin_start = 0x7f070057
com.example.uhf:color/material_personalized_color_on_error_container = 0x7f06029e
com.example.uhf:string/uhf_msg_set_frehop_succ = 0x7f120464
com.example.uhf:color/m3_timepicker_button_text_color = 0x7f060226
com.example.uhf:string/lf_msg_data_not_null = 0x7f1201e8
com.example.uhf:dimen/mtrl_extended_fab_disabled_translation_z = 0x7f0702ad
com.example.uhf:id/overshoot = 0x7f0901f8
com.example.uhf:dimen/m3_btn_padding_right = 0x7f0700e0
com.example.uhf:color/m3_timepicker_button_background_color = 0x7f060224
com.example.uhf:color/bright_foreground_disabled_material_dark = 0x7f060026
com.example.uhf:dimen/m3_comp_outlined_card_disabled_outline_opacity = 0x7f070156
com.example.uhf:color/m3_textfield_stroke_color = 0x7f060223
com.example.uhf:attr/animateRelativeTo = 0x7f04003b
com.example.uhf:attr/tabMinWidth = 0x7f040463
com.example.uhf:color/m3_textfield_label_color = 0x7f060222
com.example.uhf:string/msg_psam_set = 0x7f12026a
com.example.uhf:style/Theme.Material3.Dark = 0x7f130252
com.example.uhf:color/m3_textfield_input_text_color = 0x7f060221
com.example.uhf:dimen/abc_star_small = 0x7f07003d
com.example.uhf:color/m3_textfield_filled_background_color = 0x7f06021f
com.example.uhf:color/m3_tabs_icon_color = 0x7f060216
com.example.uhf:color/m3_sys_color_tertiary_fixed = 0x7f060214
com.example.uhf:string/m1_title_multiple = 0x7f12020a
com.example.uhf:id/arc = 0x7f090078
com.example.uhf:color/m3_sys_color_secondary_fixed_dim = 0x7f060213
com.example.uhf:string/rfid_msg_confirm_afi = 0x7f120344
com.example.uhf:array/arrayBaud = 0x7f030009
com.example.uhf:attr/motionEffect_alpha = 0x7f04035e
com.example.uhf:color/m3_sys_color_on_secondary_fixed_variant = 0x7f06020d
com.example.uhf:style/Theme.Material3.Light.Dialog = 0x7f13026a
com.example.uhf:id/month_navigation_bar = 0x7f0901b5
com.example.uhf:attr/flow_firstVerticalBias = 0x7f040208
com.example.uhf:color/m3_sys_color_light_tertiary_container = 0x7f060209
com.example.uhf:macro/m3_comp_bottom_app_bar_container_color = 0x7f0d0005
com.example.uhf:macro/m3_comp_plain_tooltip_supporting_text_type = 0x7f0d00c6
com.example.uhf:color/m3_sys_color_light_surface_container_lowest = 0x7f060205
com.example.uhf:attr/carousel_infinite = 0x7f0400ae
com.example.uhf:color/m3_sys_color_light_surface_container_low = 0x7f060204
com.example.uhf:dimen/text_size_7 = 0x7f07034a
com.example.uhf:color/m3_sys_color_light_surface_container_high = 0x7f060202
com.example.uhf:style/Base.V23.Theme.AppCompat = 0x7f1300b4
com.example.uhf:animator/mtrl_card_state_list_anim = 0x7f020017
com.example.uhf:dimen/material_time_picker_minimum_screen_height = 0x7f070249
com.example.uhf:dimen/m3_btn_dialog_btn_spacing = 0x7f0700d1
com.example.uhf:color/m3_sys_color_light_surface_bright = 0x7f060200
com.example.uhf:styleable/CircularProgressIndicator = 0x7f140023
com.example.uhf:color/m3_sys_color_light_secondary_container = 0x7f0601fe
com.example.uhf:attr/layout_goneMarginBaseline = 0x7f0402ca
com.example.uhf:color/m3_sys_color_light_outline_variant = 0x7f0601fa
com.example.uhf:attr/image_width = 0x7f040256
com.example.uhf:dimen/text_size_22 = 0x7f07033f
com.example.uhf:attr/autoShowKeyboard = 0x7f040046
com.example.uhf:color/m3_sys_color_light_outline = 0x7f0601f9
com.example.uhf:color/m3_sys_color_light_on_tertiary = 0x7f0601f7
com.example.uhf:string/mtrl_checkbox_state_description_indeterminate = 0x7f12028a
com.example.uhf:layout/abc_screen_simple = 0x7f0c0015
com.example.uhf:dimen/m3_navigation_rail_item_padding_top = 0x7f0701d7
com.example.uhf:dimen/design_fab_size_mini = 0x7f070073
com.example.uhf:drawable/abc_list_pressed_holo_light = 0x7f080052
com.example.uhf:attr/textFillColor = 0x7f0404a4
com.example.uhf:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut = 0x7f130174
com.example.uhf:color/m3_sys_color_light_on_surface_variant = 0x7f0601f6
com.example.uhf:color/m3_sys_color_light_on_secondary_container = 0x7f0601f4
com.example.uhf:color/m3_sys_color_light_on_secondary = 0x7f0601f3
com.example.uhf:color/material_dynamic_neutral90 = 0x7f060248
com.example.uhf:attr/titlePositionInterpolator = 0x7f0404de
com.example.uhf:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f13003b
com.example.uhf:color/m3_sys_color_light_on_background = 0x7f0601ee
com.example.uhf:attr/values = 0x7f04050e
com.example.uhf:color/m3_ref_palette_error40 = 0x7f060116
com.example.uhf:color/m3_sys_color_light_inverse_surface = 0x7f0601ed
com.example.uhf:color/m3_sys_color_light_inverse_primary = 0x7f0601ec
com.example.uhf:color/m3_sys_color_dynamic_tertiary_fixed_dim = 0x7f0601e7
com.example.uhf:string/fingerprint_btn_save_stop = 0x7f12017e
com.example.uhf:color/m3_sys_color_dynamic_secondary_fixed_dim = 0x7f0601e5
com.example.uhf:dimen/m3_small_fab_size = 0x7f0701f2
com.example.uhf:style/Widget.AppCompat.Light.ListPopupWindow = 0x7f13033c
com.example.uhf:string/uhf_msg_r2000_tip = 0x7f120456
com.example.uhf:color/m3_sys_color_dynamic_primary_fixed = 0x7f0601e2
com.example.uhf:color/m3_sys_color_dynamic_on_tertiary_fixed_variant = 0x7f0601e1
com.example.uhf:color/m3_sys_color_dynamic_on_primary_fixed_variant = 0x7f0601dd
com.example.uhf:drawable/$mtrl_checkbox_button_unchecked_checked__1 = 0x7f08001f
com.example.uhf:attr/primaryActivityName = 0x7f0403bc
com.example.uhf:color/m3_sys_color_dynamic_light_tertiary = 0x7f0601da
com.example.uhf:dimen/network_list_small_padding = 0x7f070317
com.example.uhf:style/TextAppearance.AppCompat = 0x7f1301be
com.example.uhf:id/search_go_btn = 0x7f09024f
com.example.uhf:dimen/m3_comp_menu_container_elevation = 0x7f07013a
com.example.uhf:color/m3_sys_color_dynamic_light_surface_variant = 0x7f0601d9
com.example.uhf:color/m3_sys_color_dynamic_light_surface_container = 0x7f0601d3
com.example.uhf:attr/drawableLeftCompat = 0x7f04019c
com.example.uhf:style/TextAppearance.M3.Sys.Typescale.LabelMedium = 0x7f130208
com.example.uhf:color/m3_sys_color_dynamic_light_surface = 0x7f0601d1
com.example.uhf:string/mtrl_checkbox_button_icon_path_group_name = 0x7f120282
com.example.uhf:id/splinkParams = 0x7f090277
com.example.uhf:color/m3_sys_color_dynamic_light_primary_container = 0x7f0601ce
com.example.uhf:attr/extendedFloatingActionButtonTertiaryStyle = 0x7f0401e4
com.example.uhf:integer/preferences_header_pane_weight = 0x7f0a0044
com.example.uhf:drawable/abc_scrubber_track_mtrl_alpha = 0x7f080062
com.example.uhf:color/m3_sys_color_dynamic_light_outline = 0x7f0601cb
com.example.uhf:attr/panelMenuListWidth = 0x7f040393
com.example.uhf:color/m3_sys_color_dynamic_light_on_tertiary_container = 0x7f0601ca
com.example.uhf:dimen/m3_appbar_scrim_height_trigger_large = 0x7f0700ac
com.example.uhf:color/sbc_list_item = 0x7f06032a
com.example.uhf:color/m3_sys_color_dynamic_light_on_tertiary = 0x7f0601c9
com.example.uhf:style/Base.V14.Theme.MaterialComponents.Bridge = 0x7f130097
com.example.uhf:id/accessibility_custom_action_11 = 0x7f090036
com.example.uhf:macro/m3_comp_elevated_button_container_color = 0x7f0d0029
com.example.uhf:color/material_dynamic_tertiary99 = 0x7f06027e
com.example.uhf:string/action_rfid_upgrader = 0x7f120043
com.example.uhf:color/m3_sys_color_dynamic_light_on_primary_container = 0x7f0601c4
com.example.uhf:dimen/mtrl_fab_translation_z_pressed = 0x7f0702be
com.example.uhf:dimen/m3_comp_time_picker_period_selector_focus_state_layer_opacity = 0x7f0701a6
com.example.uhf:color/m3_sys_color_dynamic_light_on_background = 0x7f0601c0
com.example.uhf:dimen/m3_navigation_item_shape_inset_bottom = 0x7f0701c7
com.example.uhf:color/m3_sys_color_dynamic_light_inverse_surface = 0x7f0601bf
com.example.uhf:color/m3_sys_color_dynamic_light_inverse_primary = 0x7f0601be
com.example.uhf:macro/m3_comp_switch_disabled_unselected_handle_color = 0x7f0d011c
com.example.uhf:color/m3_sys_color_dynamic_dark_surface_variant = 0x7f0601b7
com.example.uhf:id/chains = 0x7f0900da
com.example.uhf:color/m3_ref_palette_black = 0x7f0600b9
com.example.uhf:color/m3_sys_color_dynamic_dark_surface_container_lowest = 0x7f0601b5
com.example.uhf:style/ThemeOverlay.Material3.BottomSheetDialog = 0x7f1302b4
com.example.uhf:attr/roundPercent = 0x7f0403d8
com.example.uhf:macro/m3_comp_time_picker_time_selector_selected_label_text_color = 0x7f0d0163
com.example.uhf:color/m3_sys_color_dynamic_dark_surface_container_low = 0x7f0601b4
com.example.uhf:style/Base.Widget.Material3.ActionBar.Solid = 0x7f130102
com.example.uhf:macro/m3_comp_navigation_drawer_label_text_type = 0x7f0d0091
com.example.uhf:attr/labelBehavior = 0x7f04028a
com.example.uhf:attr/goIcon = 0x7f04022c
com.example.uhf:style/RtlUnderlay.Widget.AppCompat.ActionButton = 0x7f13017e
com.example.uhf:styleable/PreferenceGroup = 0x7f14007c
com.example.uhf:color/m3_sys_color_dynamic_dark_surface_container = 0x7f0601b1
com.example.uhf:id/etLen_filter_lock = 0x7f090131
com.example.uhf:style/Widget.MaterialComponents.TabLayout = 0x7f130472
com.example.uhf:drawable/ic_launcher = 0x7f0800a5
com.example.uhf:color/m3_sys_color_dynamic_dark_surface = 0x7f0601af
com.example.uhf:id/btnGetFastInventory = 0x7f090094
com.example.uhf:drawable/$mtrl_checkbox_button_icon_checked_unchecked__1 = 0x7f080012
com.example.uhf:color/m3_sys_color_dynamic_dark_on_surface_variant = 0x7f0601a6
com.example.uhf:color/m3_sys_color_dynamic_dark_on_primary_container = 0x7f0601a2
com.example.uhf:color/m3_sys_color_dynamic_dark_on_primary = 0x7f0601a1
com.example.uhf:string/contents_phone = 0x7f1200fe
com.example.uhf:color/m3_ref_palette_secondary10 = 0x7f060151
com.example.uhf:color/m3_sys_color_dynamic_dark_on_error_container = 0x7f0601a0
com.example.uhf:string/scanning = 0x7f120361
com.example.uhf:anim/btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015
com.example.uhf:color/m3_sys_color_dynamic_dark_on_background = 0x7f06019e
com.example.uhf:dimen/mtrl_fab_elevation = 0x7f0702bb
com.example.uhf:string/uhf_msg_get_para_succ = 0x7f12044c
com.example.uhf:color/m3_ref_palette_tertiary60 = 0x7f060164
com.example.uhf:color/m3_sys_color_dynamic_dark_inverse_primary = 0x7f06019c
com.example.uhf:color/m3_sys_color_dynamic_dark_inverse_on_surface = 0x7f06019b
com.example.uhf:color/m3_navigation_bar_item_with_indicator_label_tint = 0x7f0600ac
com.example.uhf:id/btnGBLock = 0x7f090093
com.example.uhf:id/staticPostLayout = 0x7f090286
com.example.uhf:dimen/m3_comp_fab_primary_focus_state_layer_opacity = 0x7f07011c
com.example.uhf:style/Theme.AppCompat.Light.DialogWhenLarge = 0x7f130249
com.example.uhf:drawable/abc_btn_borderless_material = 0x7f08002b
com.example.uhf:color/m3_sys_color_dark_surface_container_highest = 0x7f060191
com.example.uhf:attr/positiveButtonText = 0x7f0403ac
com.example.uhf:id/mtrl_picker_header_toggle = 0x7f0901cc
com.example.uhf:color/m3_sys_color_dark_surface_container_high = 0x7f060190
com.example.uhf:macro/m3_comp_dialog_container_shape = 0x7f0d0023
com.example.uhf:id/reverseSawtooth = 0x7f090237
com.example.uhf:interpolator/m3_sys_motion_easing_emphasized = 0x7f0b0007
com.example.uhf:macro/m3_comp_search_bar_hover_state_layer_color = 0x7f0d00e7
com.example.uhf:color/m3_sys_color_dark_secondary_container = 0x7f06018c
com.example.uhf:string/hello_blank_fragment = 0x7f1201d0
com.example.uhf:dimen/design_navigation_icon_padding = 0x7f070078
com.example.uhf:attr/behavior_expandedOffset = 0x7f040071
com.example.uhf:attr/editTextColor = 0x7f0401ad
com.example.uhf:style/ThemeOverlay.Material3.Button.TextButton = 0x7f1302ba
com.example.uhf:color/m3_sys_color_dark_secondary = 0x7f06018b
com.example.uhf:dimen/design_navigation_item_icon_padding = 0x7f07007b
com.example.uhf:drawable/btn_radio_off_mtrl = 0x7f080080
com.example.uhf:string/sure = 0x7f120385
com.example.uhf:color/m3_sys_color_dark_primary = 0x7f060189
com.example.uhf:id/LvTags = 0x7f090016
com.example.uhf:attr/layout_optimizationLevel = 0x7f0402d4
com.example.uhf:style/Theme.AppCompat.DayNight.DialogWhenLarge = 0x7f13023d
com.example.uhf:color/m3_sys_color_dark_on_tertiary_container = 0x7f060186
com.example.uhf:dimen/mtrl_slider_track_side_padding = 0x7f0702f3
com.example.uhf:style/Platform.AppCompat = 0x7f130142
com.example.uhf:array/arrayOption_Write = 0x7f03001e
com.example.uhf:attr/useDrawerArrowDrawable = 0x7f04050b
com.example.uhf:dimen/abc_text_size_medium_material = 0x7f070049
com.example.uhf:color/m3_sys_color_dark_on_surface_variant = 0x7f060184
com.example.uhf:anim/design_snackbar_out = 0x7f01001b
com.example.uhf:color/material_personalized_color_text_primary_inverse = 0x7f0602c0
com.example.uhf:macro/m3_sys_color_dark_surface_tint = 0x7f0d0175
com.example.uhf:color/m3_sys_color_dark_on_surface = 0x7f060183
com.example.uhf:color/m3_sys_color_dark_on_secondary_container = 0x7f060182
com.example.uhf:dimen/m3_sys_motion_easing_standard_accelerate_control_x1 = 0x7f070213
com.example.uhf:dimen/m3_comp_top_app_bar_small_container_height = 0x7f0701b0
com.example.uhf:style/Widget.Design.TabLayout = 0x7f130365
com.example.uhf:attr/layout_constraintHeight_default = 0x7f0402ac
com.example.uhf:string/status_bar_notification_info_overflow = 0x7f12037b
com.example.uhf:color/m3_sys_color_dark_on_secondary = 0x7f060181
com.example.uhf:style/Widget.Material3.CircularProgressIndicator.Small = 0x7f1303a3
com.example.uhf:macro/m3_comp_outlined_button_outline_color = 0x7f0d00a6
com.example.uhf:color/m3_sys_color_dark_on_primary_container = 0x7f060180
com.example.uhf:color/m3_sys_color_dark_on_error = 0x7f06017d
com.example.uhf:styleable/ExtendedFloatingActionButton_Behavior_Layout = 0x7f140038
com.example.uhf:attr/drawableRightCompat = 0x7f04019d
com.example.uhf:color/m3_sys_color_dark_inverse_on_surface = 0x7f060179
com.example.uhf:dimen/m3_bottom_nav_item_padding_bottom = 0x7f0700c4
com.example.uhf:id/icon_group = 0x7f09016d
com.example.uhf:color/m3_sys_color_dark_error_container = 0x7f060178
com.example.uhf:id/cbFastID = 0x7f0900c5
com.example.uhf:id/accessibility_custom_action_3 = 0x7f09004a
com.example.uhf:color/m3_timepicker_display_ripple_color = 0x7f060229
com.example.uhf:string/ap_title_ip = 0x7f120050
com.example.uhf:id/touch_outside = 0x7f0902b2
com.example.uhf:style/Widget.AppCompat.ActivityChooserView = 0x7f13031b
com.example.uhf:drawable/abc_scrubber_primary_mtrl_alpha = 0x7f080061
com.example.uhf:color/m3_sys_color_dynamic_dark_background = 0x7f060198
com.example.uhf:dimen/mtrl_navigation_item_icon_size = 0x7f0702cd
com.example.uhf:attr/listPreferredItemHeightLarge = 0x7f0402e9
com.example.uhf:color/m3_sys_color_dark_background = 0x7f060176
com.example.uhf:attr/selectable = 0x7f0403e7
com.example.uhf:string/gps_btn_yes = 0x7f1201c1
com.example.uhf:color/m3_switch_track_tint = 0x7f060175
com.example.uhf:id/action_bar = 0x7f090057
com.example.uhf:dimen/m3_comp_navigation_bar_pressed_state_layer_opacity = 0x7f070142
com.example.uhf:attr/ttcIndex = 0x7f040507
com.example.uhf:color/m3_bottom_sheet_drag_handle_color = 0x7f06007f
com.example.uhf:color/m3_ref_palette_dynamic_neutral_variant70 = 0x7f0600e0
com.example.uhf:color/m3_slider_active_track_color_legacy = 0x7f06016e
com.example.uhf:color/m3_simple_item_ripple_color = 0x7f06016c
com.example.uhf:color/m3_ref_palette_white = 0x7f06016a
com.example.uhf:dimen/text_size_25 = 0x7f070342
com.example.uhf:attr/colorSecondaryFixed = 0x7f040127
com.example.uhf:string/title_about_sn = 0x7f1203a0
com.example.uhf:color/m3_text_button_background_color_selector = 0x7f06021c
com.example.uhf:string/title_id_power = 0x7f1203d9
com.example.uhf:string/gps_title_Time = 0x7f1201cd
com.example.uhf:layout/design_navigation_item_separator = 0x7f0c0028
com.example.uhf:color/m3_ref_palette_tertiary99 = 0x7f060169
com.example.uhf:id/btnLock = 0x7f09009b
com.example.uhf:dimen/abc_text_size_body_2_material = 0x7f070040
com.example.uhf:color/m3_sys_color_dynamic_light_surface_bright = 0x7f0601d2
com.example.uhf:color/m3_ref_palette_tertiary80 = 0x7f060166
com.example.uhf:color/m3_sys_color_light_on_primary_container = 0x7f0601f2
com.example.uhf:attr/dividerInsetStart = 0x7f040192
com.example.uhf:color/m3_ref_palette_tertiary40 = 0x7f060162
com.example.uhf:style/Theme.MaterialComponents.DialogWhenLarge = 0x7f13028c
com.example.uhf:attr/chipIconSize = 0x7f0400cd
com.example.uhf:string/desfire_title_file_size = 0x7f12012c
com.example.uhf:color/m3_ref_palette_tertiary30 = 0x7f060161
com.example.uhf:drawable/abc_textfield_search_activated_mtrl_alpha = 0x7f080074
com.example.uhf:style/ThemeOverlay.Material3.Dark = 0x7f1302bf
com.example.uhf:attr/horizontalOffset = 0x7f040241
com.example.uhf:color/purple = 0x7f060318
com.example.uhf:layout/abc_list_menu_item_layout = 0x7f0c0010
com.example.uhf:integer/material_motion_duration_short_2 = 0x7f0a002b
com.example.uhf:color/m3_ref_palette_tertiary20 = 0x7f060160
com.example.uhf:color/m3_ref_palette_tertiary70 = 0x7f060165
com.example.uhf:id/accessibility_custom_action_27 = 0x7f090047
com.example.uhf:color/error_color_material_dark = 0x7f060061
com.example.uhf:attr/titleTextColor = 0x7f0404e0
com.example.uhf:style/Widget.Design.TextInputEditText = 0x7f130366
com.example.uhf:dimen/abc_text_size_subhead_material = 0x7f07004d
com.example.uhf:id/btnGetSession = 0x7f090099
com.example.uhf:style/Base.TextAppearance.AppCompat.Display2 = 0x7f130020
com.example.uhf:attr/colorOnSecondaryFixedVariant = 0x7f040113
com.example.uhf:dimen/m3_comp_filter_chip_elevated_container_elevation = 0x7f070131
com.example.uhf:attr/collapsingToolbarLayoutStyle = 0x7f0400fb
com.example.uhf:color/m3_ref_palette_secondary99 = 0x7f06015c
com.example.uhf:id/mtrl_picker_fullscreen = 0x7f0901c8
com.example.uhf:color/m3_ref_palette_secondary70 = 0x7f060158
com.example.uhf:color/m3_ref_palette_secondary40 = 0x7f060155
com.example.uhf:string/tvTagUii_Kill = 0x7f12042c
com.example.uhf:color/abc_search_url_text = 0x7f06000d
com.example.uhf:color/m3_ref_palette_secondary30 = 0x7f060154
com.example.uhf:style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f13003e
com.example.uhf:animator/m3_card_state_list_anim = 0x7f02000d
com.example.uhf:macro/m3_comp_top_app_bar_small_on_scroll_container_color = 0x7f0d0173
com.example.uhf:macro/m3_comp_dialog_supporting_text_type = 0x7f0d0027
com.example.uhf:color/m3_ref_palette_secondary0 = 0x7f060150
com.example.uhf:color/m3_ref_palette_primary90 = 0x7f06014d
com.example.uhf:dimen/material_textinput_default_width = 0x7f070246
com.example.uhf:color/m3_ref_palette_primary100 = 0x7f060145
com.example.uhf:style/Base.V14.Theme.Material3.Light.BottomSheetDialog = 0x7f130093
com.example.uhf:style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f130306
com.example.uhf:macro/m3_comp_time_picker_time_selector_separator_color = 0x7f0d0165
com.example.uhf:string/title_wait_time = 0x7f1203f3
com.example.uhf:string/msg_encode_contents_failed = 0x7f12024c
com.example.uhf:dimen/mtrl_transition_shared_axis_slide_distance = 0x7f070310
com.example.uhf:dimen/m3_comp_filled_card_pressed_state_layer_opacity = 0x7f07012e
com.example.uhf:dimen/m3_comp_time_picker_time_selector_pressed_state_layer_opacity = 0x7f0701ac
com.example.uhf:string/psam_msg_fail = 0x7f12030c
com.example.uhf:color/m3_ref_palette_primary60 = 0x7f06014a
com.example.uhf:attr/materialButtonOutlinedStyle = 0x7f0402fd
com.example.uhf:color/m3_sys_color_dark_on_primary = 0x7f06017f
com.example.uhf:macro/m3_comp_radio_button_disabled_unselected_icon_color = 0x7f0d00d7
com.example.uhf:macro/m3_comp_navigation_drawer_inactive_hover_icon_color = 0x7f0d0089
com.example.uhf:color/m3_ref_palette_primary30 = 0x7f060147
com.example.uhf:color/m3_ref_palette_primary20 = 0x7f060146
com.example.uhf:layout/mtrl_calendar_month_navigation = 0x7f0c005f
com.example.uhf:anim/abc_slide_out_bottom = 0x7f010008
com.example.uhf:attr/dropDownListViewStyle = 0x7f0401a7
com.example.uhf:id/btnSetLinkParams = 0x7f0900a2
com.example.uhf:color/black1 = 0x7f060022
com.example.uhf:color/m3_ref_palette_neutral_variant95 = 0x7f060141
com.example.uhf:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f13041e
com.example.uhf:dimen/compat_button_inset_vertical_material = 0x7f070059
com.example.uhf:macro/m3_comp_slider_disabled_inactive_track_color = 0x7f0d010e
com.example.uhf:string/download_msg_down_file_size = 0x7f12014e
com.example.uhf:color/m3_ref_palette_neutral_variant90 = 0x7f060140
com.example.uhf:string/er_dsoft_get_img_succ = 0x7f120160
com.example.uhf:dimen/m3_comp_assist_chip_flat_container_elevation = 0x7f070101
com.example.uhf:color/mtrl_textinput_filled_box_default_background_color = 0x7f060307
com.example.uhf:style/Theme.AppCompat.DayNight.DarkActionBar = 0x7f130239
com.example.uhf:attr/customIntegerValue = 0x7f040171
com.example.uhf:color/m3_ref_palette_neutral_variant70 = 0x7f06013e
com.example.uhf:color/m3_ref_palette_neutral_variant50 = 0x7f06013c
com.example.uhf:style/Animation.Material3.SideSheetDialog = 0x7f130009
com.example.uhf:string/title_ip_or_domain = 0x7f1203db
com.example.uhf:color/m3_sys_color_light_background = 0x7f0601e8
com.example.uhf:styleable/Motion = 0x7f140069
com.example.uhf:color/m3_ref_palette_neutral_variant40 = 0x7f06013b
com.example.uhf:attr/floatingActionButtonPrimaryStyle = 0x7f0401fc
com.example.uhf:string/title_scan_between = 0x7f1203ec
com.example.uhf:array/colors = 0x7f03002e
com.example.uhf:color/m3_ref_palette_neutral_variant30 = 0x7f06013a
com.example.uhf:attr/motionDurationMedium3 = 0x7f04034c
com.example.uhf:color/m3_ref_palette_neutral_variant20 = 0x7f060139
com.example.uhf:attr/radioButtonStyle = 0x7f0403c5
com.example.uhf:styleable/GradientColor = 0x7f140041
com.example.uhf:attr/alwaysExpand = 0x7f040037
com.example.uhf:attr/titleMargin = 0x7f0404d8
com.example.uhf:color/m3_ref_palette_dynamic_neutral_variant99 = 0x7f0600e9
com.example.uhf:attr/chipMinTouchTargetSize = 0x7f0400d1
com.example.uhf:animator/mtrl_fab_transformation_sheet_collapse_spec = 0x7f020020
com.example.uhf:color/m3_ref_palette_neutral99 = 0x7f060135
com.example.uhf:color/m3_ref_palette_neutral80 = 0x7f06012d
com.example.uhf:dimen/m3_navigation_rail_item_active_indicator_width = 0x7f0701d3
com.example.uhf:color/material_dynamic_neutral80 = 0x7f060247
com.example.uhf:color/m3_ref_palette_neutral17 = 0x7f060122
com.example.uhf:attr/toolbarStyle = 0x7f0404e6
com.example.uhf:macro/m3_comp_icon_button_unselected_icon_color = 0x7f0d005a
com.example.uhf:anim/slide_left_in = 0x7f01002d
com.example.uhf:color/m3_ref_palette_neutral6 = 0x7f06012a
com.example.uhf:attr/materialCalendarHeaderDivider = 0x7f040305
com.example.uhf:color/red4 = 0x7f06031d
com.example.uhf:attr/startIconTint = 0x7f04042b
com.example.uhf:attr/animateMenuItems = 0x7f040039
com.example.uhf:color/m3_ref_palette_neutral50 = 0x7f060129
com.example.uhf:id/SHOW_PATH = 0x7f09001e
com.example.uhf:array/defaulticon = 0x7f030032
com.example.uhf:color/m3_ref_palette_neutral40 = 0x7f060128
com.example.uhf:string/v7_preference_on = 0x7f1204ae
com.example.uhf:color/m3_ref_palette_neutral4 = 0x7f060127
com.example.uhf:color/m3_ref_palette_neutral20 = 0x7f060123
com.example.uhf:color/m3_ref_palette_neutral100 = 0x7f060120
com.example.uhf:color/m3_ref_palette_error99 = 0x7f06011d
com.example.uhf:color/m3_ref_palette_error95 = 0x7f06011c
com.example.uhf:style/Base.Widget.Material3.TabLayout.OnSurface = 0x7f130114
com.example.uhf:id/centerInside = 0x7f0900d5
com.example.uhf:color/m3_ref_palette_error80 = 0x7f06011a
com.example.uhf:string/title_activity_uart = 0x7f1203c6
com.example.uhf:style/Theme.MaterialComponents.DayNight.NoActionBar.Bridge = 0x7f130283
com.example.uhf:color/m3_ref_palette_error70 = 0x7f060119
com.example.uhf:color/m3_ref_palette_error50 = 0x7f060117
com.example.uhf:attr/singleSelection = 0x7f04040f
com.example.uhf:dimen/m3_bottom_sheet_modal_elevation = 0x7f0700c9
com.example.uhf:string/button_sms = 0x7f1200e0
com.example.uhf:attr/contentPaddingLeft = 0x7f04014d
com.example.uhf:id/etPtr_filter_deact = 0x7f09013c
com.example.uhf:color/m3_ref_palette_error20 = 0x7f060114
com.example.uhf:attr/center_image = 0x7f0400b5
com.example.uhf:style/Platform.MaterialComponents.Dialog = 0x7f130145
com.example.uhf:attr/fontProviderPackage = 0x7f04021f
com.example.uhf:color/mtrl_chip_surface_color = 0x7f0602e1
com.example.uhf:color/red1 = 0x7f06031a
com.example.uhf:style/Base.V24.Theme.Material3.Light = 0x7f1300b8
com.example.uhf:color/design_dark_default_color_on_primary = 0x7f06003c
com.example.uhf:color/m3_ref_palette_dynamic_tertiary99 = 0x7f060110
com.example.uhf:dimen/m3_carousel_small_item_size_max = 0x7f0700f6
com.example.uhf:color/mtrl_navigation_item_text_color = 0x7f0602f4
com.example.uhf:attr/ifTagNotSet = 0x7f04024e
com.example.uhf:color/m3_ref_palette_neutral94 = 0x7f060131
com.example.uhf:id/spline = 0x7f090276
com.example.uhf:attr/fastScrollVerticalThumbDrawable = 0x7f0401f2
com.example.uhf:attr/indeterminateProgressStyle = 0x7f040258
com.example.uhf:color/m3_ref_palette_dynamic_tertiary70 = 0x7f06010c
com.example.uhf:color/m3_ref_palette_dynamic_tertiary50 = 0x7f06010a
com.example.uhf:dimen/material_clock_period_toggle_width = 0x7f070233
com.example.uhf:macro/m3_comp_navigation_bar_active_pressed_state_layer_color = 0x7f0d006a
com.example.uhf:attr/carousel_emptyViewsBehavior = 0x7f0400ab
com.example.uhf:color/m3_ref_palette_dynamic_tertiary30 = 0x7f060108
com.example.uhf:attr/textAppearanceHeadline6 = 0x7f040486
com.example.uhf:attr/behavior_skipCollapsed = 0x7f040079
com.example.uhf:array/arrayDesFirePwdMode = 0x7f03000d
com.example.uhf:color/m3_ref_palette_dynamic_tertiary100 = 0x7f060106
com.example.uhf:dimen/m3_sys_motion_easing_legacy_accelerate_control_y1 = 0x7f070205
com.example.uhf:attr/trackColorInactive = 0x7f0404f3
com.example.uhf:dimen/m3_sys_elevation_level2 = 0x7f0701f7
com.example.uhf:dimen/m3_carousel_debug_keyline_width = 0x7f0700f2
com.example.uhf:string/uhf_msg_read_pwm_fail = 0x7f12045c
com.example.uhf:string/abc_action_bar_home_description = 0x7f120021
com.example.uhf:string/up_msg_net_not_conn = 0x7f12049a
com.example.uhf:string/uhf_msg_read_tag_fail = 0x7f12045d
com.example.uhf:drawable/button_bg = 0x7f080084
com.example.uhf:color/m3_ref_palette_dynamic_secondary80 = 0x7f060100
com.example.uhf:attr/tabGravity = 0x7f040457
com.example.uhf:attr/passwordToggleEnabled = 0x7f040396
com.example.uhf:color/m3_ref_palette_dynamic_secondary70 = 0x7f0600ff
com.example.uhf:string/tvPtr_Data = 0x7f12041d
com.example.uhf:id/view_tree_saved_state_registry_owner = 0x7f0902d6
com.example.uhf:color/m3_ref_palette_dynamic_secondary50 = 0x7f0600fd
com.example.uhf:color/m3_ref_palette_dynamic_secondary40 = 0x7f0600fc
com.example.uhf:style/Theme.Material3.DynamicColors.Light.NoActionBar = 0x7f130267
com.example.uhf:color/m3_ref_palette_dynamic_secondary30 = 0x7f0600fb
com.example.uhf:dimen/mtrl_extended_fab_top_padding = 0x7f0702b7
com.example.uhf:color/m3_ref_palette_dynamic_secondary20 = 0x7f0600fa
com.example.uhf:color/m3_sys_color_dynamic_light_background = 0x7f0601ba
com.example.uhf:style/Widget.Material3.CollapsingToolbar.Medium = 0x7f1303a6
com.example.uhf:color/m3_ref_palette_dynamic_secondary100 = 0x7f0600f9
com.example.uhf:string/battery_tips_STATUS_CHARGING = 0x7f12007a
com.example.uhf:color/m3_sys_color_dynamic_dark_on_tertiary_container = 0x7f0601a8
com.example.uhf:color/m3_ref_palette_dynamic_secondary0 = 0x7f0600f7
com.example.uhf:attr/layout_goneMarginLeft = 0x7f0402cd
com.example.uhf:interpolator/btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0b0004
com.example.uhf:id/square = 0x7f09027c
com.example.uhf:string/desfire_msg_get_apps_fail = 0x7f12010e
com.example.uhf:attr/splitMinSmallestWidth = 0x7f04041a
com.example.uhf:dimen/m3_comp_radio_button_selected_focus_state_layer_opacity = 0x7f07016c
com.example.uhf:dimen/material_clock_period_toggle_height = 0x7f070230
com.example.uhf:color/m3_ref_palette_dynamic_primary100 = 0x7f0600ec
com.example.uhf:style/TextAppearance.Design.Counter.Overflow = 0x7f1301f5
com.example.uhf:macro/m3_comp_secondary_navigation_tab_with_icon_inactive_icon_color = 0x7f0d0103
com.example.uhf:attr/layout_constraintGuide_begin = 0x7f0402a8
com.example.uhf:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f130044
com.example.uhf:string/ap_title_pause = 0x7f120051
com.example.uhf:color/m3_ref_palette_dynamic_neutral_variant98 = 0x7f0600e8
com.example.uhf:style/Base.V14.Theme.MaterialComponents.Light.Bridge = 0x7f13009b
com.example.uhf:string/file_msg_cancel = 0x7f120172
com.example.uhf:attr/materialSearchBarStyle = 0x7f04031c
com.example.uhf:color/m3_ref_palette_dynamic_neutral_variant96 = 0x7f0600e7
com.example.uhf:attr/actionModeTheme = 0x7f04001f
com.example.uhf:dimen/mtrl_btn_max_width = 0x7f070269
com.example.uhf:id/title = 0x7f0902ac
com.example.uhf:attr/barrierAllowsGoneWidgets = 0x7f04006b
com.example.uhf:color/m3_ref_palette_dynamic_neutral_variant95 = 0x7f0600e6
com.example.uhf:color/m3_ref_palette_dynamic_neutral_variant92 = 0x7f0600e4
com.example.uhf:string/desfire_title_app_change_pwd = 0x7f12011a
com.example.uhf:color/m3_ref_palette_dynamic_neutral_variant87 = 0x7f0600e2
com.example.uhf:string/title_Download = 0x7f120391
com.example.uhf:dimen/m3_comp_fab_primary_small_icon_size = 0x7f070125
com.example.uhf:attr/quantizeMotionPhase = 0x7f0403c0
com.example.uhf:color/m3_dynamic_dark_default_color_primary_text = 0x7f060098
com.example.uhf:macro/m3_comp_search_view_header_input_text_type = 0x7f0d00f5
com.example.uhf:attr/customDimension = 0x7f04016f
com.example.uhf:style/ShapeAppearance.MaterialComponents = 0x7f1301a5
com.example.uhf:attr/behavior_saveFlags = 0x7f040077
com.example.uhf:color/m3_ref_palette_dynamic_neutral_variant4 = 0x7f0600db
com.example.uhf:color/m3_ref_palette_dynamic_neutral_variant30 = 0x7f0600da
com.example.uhf:color/m3_ref_palette_dynamic_neutral_variant24 = 0x7f0600d9
com.example.uhf:attr/itemPadding = 0x7f040271
com.example.uhf:color/m3_ref_palette_dynamic_neutral_variant22 = 0x7f0600d8
com.example.uhf:dimen/m3_comp_sheet_bottom_docked_drag_handle_height = 0x7f07017f
com.example.uhf:dimen/standard_padding = 0x7f070332
com.example.uhf:color/green1 = 0x7f06006e
com.example.uhf:color/m3_ref_palette_dynamic_neutral_variant20 = 0x7f0600d7
com.example.uhf:attr/dividerInsetEnd = 0x7f040191
com.example.uhf:color/m3_ref_palette_dynamic_neutral_variant17 = 0x7f0600d6
com.example.uhf:string/fingerprint_btn_set_PSW = 0x7f120180
com.example.uhf:string/button_disable = 0x7f1200cd
com.example.uhf:color/m3_navigation_item_icon_tint = 0x7f0600af
com.example.uhf:color/m3_ref_palette_dynamic_neutral_variant12 = 0x7f0600d5
com.example.uhf:color/m3_ref_palette_dynamic_neutral_variant10 = 0x7f0600d3
com.example.uhf:id/withinBounds = 0x7f0902dd
com.example.uhf:attr/targetId = 0x7f040472
com.example.uhf:attr/closeIcon = 0x7f0400e9
com.example.uhf:color/m3_ref_palette_dynamic_neutral99 = 0x7f0600d1
com.example.uhf:style/Widget.MaterialComponents.MaterialCalendar.DayTextView = 0x7f13044f
com.example.uhf:color/m3_ref_palette_dynamic_neutral98 = 0x7f0600d0
com.example.uhf:string/nfc_msg_not_support_ndef = 0x7f1202d1
com.example.uhf:layout/preference_material = 0x7f0c0082
com.example.uhf:string/Japan = 0x7f12000c
com.example.uhf:style/TextAppearance.Material3.SearchBar = 0x7f13021c
com.example.uhf:layout/notification_action = 0x7f0c0072
com.example.uhf:attr/labelStyle = 0x7f04028b
com.example.uhf:color/m3_sys_color_dynamic_on_primary_fixed = 0x7f0601dc
com.example.uhf:attr/itemTextAppearanceActive = 0x7f040280
com.example.uhf:color/m3_ref_palette_dynamic_neutral96 = 0x7f0600cf
com.example.uhf:style/Base.Widget.AppCompat.TextView = 0x7f1300fd
com.example.uhf:attr/contentInsetRight = 0x7f040147
com.example.uhf:color/m3_sys_color_tertiary_fixed_dim = 0x7f060215
com.example.uhf:color/m3_ref_palette_dynamic_neutral94 = 0x7f0600cd
com.example.uhf:color/m3_ref_palette_dynamic_neutral90 = 0x7f0600cb
com.example.uhf:color/m3_ref_palette_dynamic_neutral80 = 0x7f0600c9
com.example.uhf:integer/m3_sys_shape_corner_extra_small_corner_family = 0x7f0a0021
com.example.uhf:attr/contentPaddingEnd = 0x7f04014c
com.example.uhf:dimen/m3_navigation_rail_default_width = 0x7f0701ce
com.example.uhf:attr/flow_wrapMode = 0x7f040218
com.example.uhf:attr/reactiveGuide_applyToConstraintSet = 0x7f0403cc
com.example.uhf:array/arrayOption_Read = 0x7f03001d
com.example.uhf:attr/indicatorDirectionCircular = 0x7f04025a
com.example.uhf:color/m3_sys_color_dynamic_light_on_error_container = 0x7f0601c2
com.example.uhf:color/m3_ref_palette_dynamic_neutral22 = 0x7f0600c0
com.example.uhf:color/m3_sys_color_dynamic_light_surface_container_high = 0x7f0601d4
com.example.uhf:attr/percentY = 0x7f04039e
com.example.uhf:color/m3_ref_palette_dynamic_neutral0 = 0x7f0600ba
com.example.uhf:color/m3_ref_palette_dynamic_neutral_variant80 = 0x7f0600e1
com.example.uhf:dimen/m3_bottom_nav_item_active_indicator_height = 0x7f0700c1
com.example.uhf:dimen/mtrl_navigation_rail_default_width = 0x7f0702d2
com.example.uhf:color/m3_primary_text_disable_only = 0x7f0600b6
com.example.uhf:drawable/mtrl_navigation_bar_item_background = 0x7f0800de
com.example.uhf:attr/badgeWithTextWidth = 0x7f040069
com.example.uhf:color/m3_popupmenu_overlay_color = 0x7f0600b5
com.example.uhf:id/layout = 0x7f09017f
com.example.uhf:string/battery_msg_runing = 0x7f120073
com.example.uhf:color/m3_navigation_rail_ripple_color_selector = 0x7f0600b4
com.example.uhf:attr/enableCopying = 0x7f0401b5
com.example.uhf:color/m3_navigation_rail_item_with_indicator_label_tint = 0x7f0600b3
com.example.uhf:color/m3_navigation_rail_item_with_indicator_icon_tint = 0x7f0600b2
com.example.uhf:color/m3_sys_color_light_on_primary = 0x7f0601f1
com.example.uhf:color/m3_ref_palette_dynamic_secondary60 = 0x7f0600fe
com.example.uhf:integer/mtrl_switch_thumb_viewport_center_coordinate = 0x7f0a003a
com.example.uhf:style/Widget.Material3.MaterialCalendar.YearNavigationButton = 0x7f1303da
com.example.uhf:color/m3_navigation_item_text_color = 0x7f0600b1
com.example.uhf:attr/yearStyle = 0x7f04052b
com.example.uhf:dimen/m3_comp_filter_chip_flat_unselected_outline_width = 0x7f070133
com.example.uhf:attr/hideAnimationBehavior = 0x7f040236
com.example.uhf:color/m3_navigation_bar_item_with_indicator_icon_tint = 0x7f0600ab
com.example.uhf:style/ThemeOverlay.Material3.TextInputEditText = 0x7f1302e3
com.example.uhf:string/searchview_clear_text_content_description = 0x7f120365
com.example.uhf:color/m3_hint_foreground = 0x7f0600a9
com.example.uhf:attr/hideOnScroll = 0x7f04023a
com.example.uhf:color/full_transparent = 0x7f060065
com.example.uhf:color/m3_highlighted_text = 0x7f0600a8
com.example.uhf:layout/abc_activity_chooser_view_list_item = 0x7f0c0007
com.example.uhf:drawable/material_ic_keyboard_arrow_previous_black_24dp = 0x7f0800c4
com.example.uhf:string/desfire_title_dialog_tips_msg = 0x7f120128
com.example.uhf:string/setAgreement_succ = 0x7f12036c
com.example.uhf:string/uhf_msg_no_Bluetooth = 0x7f120453
com.example.uhf:color/m3_fab_ripple_color_selector = 0x7f0600a6
com.example.uhf:attr/colorSurfaceBright = 0x7f04012b
com.example.uhf:color/m3_fab_efab_foreground_color_selector = 0x7f0600a5
com.example.uhf:string/rfid_msg_confirm_dsfid = 0x7f120345
com.example.uhf:dimen/m3_menu_elevation = 0x7f0701c1
com.example.uhf:dimen/m3_btn_text_btn_icon_padding_left = 0x7f0700e3
com.example.uhf:dimen/m3_comp_outlined_text_field_focus_outline_width = 0x7f07015d
com.example.uhf:attr/trackDecorationTintMode = 0x7f0404f7
com.example.uhf:style/TextAppearance.Material3.LabelLarge = 0x7f130218
com.example.uhf:dimen/m3_comp_filled_card_icon_size = 0x7f07012d
com.example.uhf:color/m3_fab_efab_background_color_selector = 0x7f0600a4
com.example.uhf:color/m3_elevated_chip_background_color = 0x7f0600a3
com.example.uhf:macro/m3_comp_navigation_drawer_modal_container_color = 0x7f0d0092
com.example.uhf:attr/hideOnContentScroll = 0x7f040239
com.example.uhf:color/m3_dynamic_highlighted_text = 0x7f06009f
com.example.uhf:drawable/triangle = 0x7f08010a
com.example.uhf:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout = 0x7f130454
com.example.uhf:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f1300a3
com.example.uhf:attr/windowActionBar = 0x7f040520
com.example.uhf:attr/showPaths = 0x7f040401
com.example.uhf:integer/m3_sys_shape_corner_small_corner_family = 0x7f0a0025
com.example.uhf:drawable/card_press = 0x7f08008f
com.example.uhf:color/switch_thumb_disabled_material_light = 0x7f06033a
com.example.uhf:string/m1_title_block = 0x7f120207
com.example.uhf:string/reset_fail = 0x7f120324
com.example.uhf:integer/num_cols = 0x7f0a0042
com.example.uhf:color/m3_default_color_secondary_text = 0x7f060097
com.example.uhf:color/m3_default_color_primary_text = 0x7f060096
com.example.uhf:attr/drawableTopCompat = 0x7f0401a2
com.example.uhf:color/m3_dark_hint_foreground = 0x7f060094
com.example.uhf:string/btRead = 0x7f1200b6
com.example.uhf:color/m3_dark_default_color_primary_text = 0x7f060091
com.example.uhf:attr/colorControlNormal = 0x7f040103
com.example.uhf:dimen/m3_searchview_divider_size = 0x7f0701e7
com.example.uhf:style/Widget.MaterialComponents.ActionBar.PrimarySurface = 0x7f130414
com.example.uhf:color/m3_chip_stroke_color = 0x7f06008f
com.example.uhf:color/m3_chip_ripple_color = 0x7f06008e
com.example.uhf:color/m3_chip_assist_text_color = 0x7f06008c
com.example.uhf:layout/preference_category_material = 0x7f0c007b
com.example.uhf:style/TextAppearance.AppCompat.Light.SearchResult.Title = 0x7f1301cc
com.example.uhf:attr/materialCalendarDay = 0x7f040300
com.example.uhf:attr/actionModeStyle = 0x7f04001e
com.example.uhf:string/rfid_mgs_kill_fail = 0x7f12033c
com.example.uhf:macro/m3_comp_secondary_navigation_tab_active_label_text_color = 0x7f0d00fb
com.example.uhf:color/m3_checkbox_button_icon_tint = 0x7f06008a
com.example.uhf:id/mtrl_card_checked_layer_id = 0x7f0901c4
com.example.uhf:attr/customPixelDimension = 0x7f040173
com.example.uhf:attr/titleMarginEnd = 0x7f0404da
com.example.uhf:attr/drawableBottomCompat = 0x7f04019a
com.example.uhf:color/m3_calendar_item_stroke_color = 0x7f060086
com.example.uhf:attr/color = 0x7f0400fc
com.example.uhf:drawable/mtrl_dropdown_arrow = 0x7f0800d5
com.example.uhf:color/m3_button_outline_color_selector = 0x7f060082
com.example.uhf:id/accessibility_custom_action_5 = 0x7f09004e
com.example.uhf:dimen/text_size_10 = 0x7f070333
com.example.uhf:dimen/mtrl_card_spacing = 0x7f0702a5
com.example.uhf:id/META = 0x7f090017
com.example.uhf:color/material_dynamic_primary70 = 0x7f060260
com.example.uhf:color/mtrl_textinput_disabled_color = 0x7f060306
com.example.uhf:color/m3_ref_palette_neutral_variant80 = 0x7f06013f
com.example.uhf:style/Base.Theme.AppCompat.Dialog = 0x7f130051
com.example.uhf:style/Widget.MaterialComponents.Button.Icon = 0x7f130429
com.example.uhf:dimen/text_size_21 = 0x7f07033e
com.example.uhf:color/m3_appbar_overlay_color = 0x7f06007c
com.example.uhf:macro/m3_comp_search_bar_trailing_icon_color = 0x7f0d00f0
com.example.uhf:attr/colorSurfaceContainer = 0x7f04012c
com.example.uhf:color/material_dynamic_primary80 = 0x7f060261
com.example.uhf:color/listitem_black = 0x7f06007a
com.example.uhf:dimen/mtrl_card_checked_icon_size = 0x7f0702a1
com.example.uhf:attr/emojiCompatEnabled = 0x7f0401b4
com.example.uhf:color/lightblue2 = 0x7f060079
com.example.uhf:color/m3_ref_palette_neutral22 = 0x7f060124
com.example.uhf:color/lfile_colorPrimary = 0x7f060077
com.example.uhf:dimen/m3_sys_state_focus_state_layer_opacity = 0x7f070220
com.example.uhf:color/lemonyellow = 0x7f060076
com.example.uhf:drawable/$m3_avd_hide_password__2 = 0x7f080009
com.example.uhf:color/highlighted_text_material_light = 0x7f060075
com.example.uhf:attr/itemStrokeColor = 0x7f04027d
com.example.uhf:style/Base.Widget.AppCompat.PopupMenu.Overflow = 0x7f1300f0
com.example.uhf:string/tvChinnelSpace = 0x7f1203fd
com.example.uhf:macro/m3_comp_radio_button_unselected_hover_icon_color = 0x7f0d00e1
com.example.uhf:id/etServerUrl = 0x7f090142
com.example.uhf:attr/trackTintMode = 0x7f0404fd
com.example.uhf:color/m3_sys_color_dark_surface_variant = 0x7f060195
com.example.uhf:color/green = 0x7f06006d
com.example.uhf:id/TvPhase = 0x7f090028
com.example.uhf:color/graywhite = 0x7f06006c
com.example.uhf:color/design_default_color_on_primary = 0x7f060049
com.example.uhf:attr/enforceMaterialTheme = 0x7f0401c0
com.example.uhf:string/bluetooth_msg_not_adapter = 0x7f12009a
com.example.uhf:color/button_material_dark = 0x7f06002c
com.example.uhf:dimen/design_bottom_navigation_shadow_height = 0x7f07006b
com.example.uhf:array/arrayThreshold = 0x7f030027
com.example.uhf:id/ghost_view_holder = 0x7f09015c
com.example.uhf:string/m3_sys_motion_easing_linear = 0x7f12021c
com.example.uhf:dimen/mtrl_textinput_box_corner_radius_small = 0x7f070302
com.example.uhf:attr/iconStartPadding = 0x7f04024a
com.example.uhf:drawable/mtrl_checkbox_button = 0x7f0800ca
com.example.uhf:string/battery_tips_HEALTH_OVERHEAT = 0x7f120076
com.example.uhf:attr/persistent = 0x7f0403a0
com.example.uhf:attr/keyPositionType = 0x7f040286
com.example.uhf:color/foreground_material_light = 0x7f060064
com.example.uhf:attr/badgeShapeAppearanceOverlay = 0x7f04005d
com.example.uhf:color/dim_foreground_disabled_material_dark = 0x7f06005c
com.example.uhf:dimen/design_appbar_elevation = 0x7f070060
com.example.uhf:attr/visibilityMode = 0x7f040516
com.example.uhf:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f13047f
com.example.uhf:attr/homeLayout = 0x7f040240
com.example.uhf:attr/linearProgressIndicatorStyle = 0x7f0402df
com.example.uhf:attr/elevationOverlayEnabled = 0x7f0401b3
com.example.uhf:color/design_snackbar_background_color = 0x7f06005b
com.example.uhf:style/Base.V21.Theme.AppCompat.Light.Dialog = 0x7f1300a9
com.example.uhf:color/design_default_color_primary_dark = 0x7f06004d
com.example.uhf:dimen/m3_comp_extended_fab_primary_focus_state_layer_opacity = 0x7f070114
com.example.uhf:color/design_fab_stroke_end_outer_color = 0x7f060057
com.example.uhf:color/m3_card_foreground_color = 0x7f060087
com.example.uhf:dimen/mtrl_extended_fab_start_padding_icon = 0x7f0702b6
com.example.uhf:color/design_fab_shadow_start_color = 0x7f060055
com.example.uhf:string/cancle = 0x7f1200ed
com.example.uhf:dimen/design_fab_size_normal = 0x7f070074
com.example.uhf:attr/layout_collapseParallaxMultiplier = 0x7f040298
com.example.uhf:layout/abc_action_mode_close_item_material = 0x7f0c0005
com.example.uhf:color/design_fab_shadow_mid_color = 0x7f060054
com.example.uhf:id/fade = 0x7f09014a
com.example.uhf:string/rfid_mgs_error_lessthan12 = 0x7f120334
com.example.uhf:color/design_fab_shadow_end_color = 0x7f060053
com.example.uhf:layout/item_text1 = 0x7f0c003d
com.example.uhf:id/edge = 0x7f090119
com.example.uhf:color/design_error = 0x7f060052
com.example.uhf:string/user_area_number = 0x7f1204ac
com.example.uhf:dimen/design_fab_translation_z_pressed = 0x7f070076
com.example.uhf:style/TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f1301d2
com.example.uhf:id/tag_unhandled_key_listeners = 0x7f090299
com.example.uhf:attr/tooltipText = 0x7f0404eb
com.example.uhf:color/design_default_color_secondary_variant = 0x7f060050
com.example.uhf:drawable/test_level_drawable = 0x7f080106
com.example.uhf:dimen/m3_comp_navigation_drawer_modal_container_elevation = 0x7f070147
com.example.uhf:attr/defaultState = 0x7f04017e
com.example.uhf:macro/m3_comp_navigation_bar_active_focus_icon_color = 0x7f0d005f
com.example.uhf:macro/m3_comp_filled_card_container_color = 0x7f0d0046
com.example.uhf:id/etMDL = 0x7f090137
com.example.uhf:attr/motionEasingEmphasizedDecelerateInterpolator = 0x7f040356
com.example.uhf:string/msg_reboot_count = 0x7f12026d
com.example.uhf:attr/layoutManager = 0x7f040293
com.example.uhf:color/design_default_color_secondary = 0x7f06004f
com.example.uhf:color/design_dark_default_color_error = 0x7f060039
com.example.uhf:string/abc_menu_enter_shortcut_label = 0x7f12002c
com.example.uhf:macro/m3_comp_secondary_navigation_tab_container_color = 0x7f0d00fc
com.example.uhf:id/content = 0x7f0900e9
com.example.uhf:style/Base.Theme.Material3.Dark.SideSheetDialog = 0x7f130062
com.example.uhf:dimen/abc_action_bar_subtitle_bottom_margin_material = 0x7f07000b
com.example.uhf:string/abc_menu_meta_shortcut_label = 0x7f12002e
com.example.uhf:attr/updatesContinuously = 0x7f040509
com.example.uhf:id/graph = 0x7f09015e
com.example.uhf:attr/transitionShapeAppearance = 0x7f040503
com.example.uhf:color/design_default_color_on_surface = 0x7f06004b
com.example.uhf:attr/flow_lastHorizontalStyle = 0x7f04020f
com.example.uhf:color/m3_sys_color_dynamic_dark_on_secondary_container = 0x7f0601a4
com.example.uhf:color/design_default_color_on_secondary = 0x7f06004a
com.example.uhf:color/design_default_color_on_background = 0x7f060047
com.example.uhf:attr/layout_constraintRight_creator = 0x7f0402b6
com.example.uhf:dimen/m3_comp_top_app_bar_small_on_scroll_container_elevation = 0x7f0701b1
com.example.uhf:attr/textAppearanceSmallPopupMenu = 0x7f040496
com.example.uhf:attr/thumbIconTint = 0x7f0404be
com.example.uhf:attr/textColorSearchUrl = 0x7f0404a2
com.example.uhf:attr/helperText = 0x7f040232
com.example.uhf:color/design_dark_default_color_surface = 0x7f060044
com.example.uhf:color/design_dark_default_color_secondary_variant = 0x7f060043
com.example.uhf:string/exposed_dropdown_menu_content_description = 0x7f12016a
com.example.uhf:dimen/mtrl_slider_track_height = 0x7f0702f2
com.example.uhf:dimen/m3_sys_motion_easing_legacy_control_x1 = 0x7f070207
com.example.uhf:drawable/button_bg2 = 0x7f080085
com.example.uhf:macro/m3_comp_checkbox_selected_disabled_icon_color = 0x7f0d0008
com.example.uhf:color/material_personalized_color_surface_inverse = 0x7f0602bb
com.example.uhf:anim/design_bottom_sheet_slide_out = 0x7f010019
com.example.uhf:id/tvMS = 0x7f0902c4
com.example.uhf:color/design_dark_default_color_background = 0x7f060038
com.example.uhf:id/CTRL = 0x7f090008
com.example.uhf:attr/deltaPolarRadius = 0x7f040181
com.example.uhf:style/Base.Widget.AppCompat.ImageButton = 0x7f1300e1
com.example.uhf:color/deep_gray = 0x7f060035
com.example.uhf:color/cardview_shadow_end_color = 0x7f060032
com.example.uhf:string/mtrl_switch_thumb_path_checked = 0x7f1202b5
com.example.uhf:color/sbc_snippet_text = 0x7f06032c
com.example.uhf:color/call_notification_answer_color = 0x7f06002e
com.example.uhf:color/green3 = 0x7f060070
com.example.uhf:attr/motionEffect_translationX = 0x7f040363
com.example.uhf:color/bright_foreground_material_light = 0x7f06002b
com.example.uhf:color/bright_foreground_inverse_material_dark = 0x7f060028
com.example.uhf:attr/curveFit = 0x7f04016b
com.example.uhf:attr/motionEffect_start = 0x7f040361
com.example.uhf:dimen/mtrl_calendar_header_selection_line_height = 0x7f070288
com.example.uhf:string/uhf_msg_tag_must_not_null = 0x7f120475
com.example.uhf:attr/boxBackgroundMode = 0x7f040086
com.example.uhf:color/background_material_light = 0x7f060020
com.example.uhf:macro/m3_comp_switch_selected_track_color = 0x7f0d012e
com.example.uhf:color/m3_dynamic_default_color_secondary_text = 0x7f06009e
com.example.uhf:macro/m3_comp_navigation_bar_active_pressed_label_text_color = 0x7f0d0069
com.example.uhf:color/background_floating_material_dark = 0x7f06001d
com.example.uhf:style/Widget.Material3.BottomNavigationView = 0x7f130375
com.example.uhf:dimen/abc_action_bar_subtitle_top_margin_material = 0x7f07000c
com.example.uhf:color/m3_ref_palette_dynamic_neutral_variant100 = 0x7f0600d4
com.example.uhf:string/mtrl_picker_invalid_range = 0x7f12029e
com.example.uhf:string/strUpgrade = 0x7f120381
com.example.uhf:id/text_input_start_icon = 0x7f0902a4
com.example.uhf:attr/materialAlertDialogTitleIconStyle = 0x7f0402fa
com.example.uhf:dimen/m3_comp_time_picker_time_selector_focus_state_layer_opacity = 0x7f0701aa
com.example.uhf:style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f130046
com.example.uhf:string/history_email_title = 0x7f1201d5
com.example.uhf:attr/barLength = 0x7f04006a
com.example.uhf:color/m3_sys_color_dark_inverse_primary = 0x7f06017a
com.example.uhf:color/abc_tint_edittext = 0x7f060015
com.example.uhf:attr/ensureMinTouchTargetSize = 0x7f0401c2
com.example.uhf:attr/splitMinWidth = 0x7f04041b
com.example.uhf:color/abc_tint_btn_checkable = 0x7f060013
com.example.uhf:dimen/m3_comp_checkbox_selected_disabled_container_opacity = 0x7f070108
com.example.uhf:layout/preference_information_material = 0x7f0c0080
com.example.uhf:array/arrayTagType = 0x7f030026
com.example.uhf:color/abc_secondary_text_material_light = 0x7f060012
com.example.uhf:string/uhf_msg_set_filter_fail = 0x7f120460
com.example.uhf:color/abc_primary_text_disable_only_material_dark = 0x7f060009
com.example.uhf:attr/drawableEndCompat = 0x7f04019b
com.example.uhf:color/abc_decor_view_status_guard = 0x7f060005
com.example.uhf:dimen/m3_large_fab_max_image_size = 0x7f0701be
com.example.uhf:attr/textAppearanceLineHeightEnabled = 0x7f04048e
com.example.uhf:attr/windowActionModeOverlay = 0x7f040522
com.example.uhf:style/Base.TextAppearance.AppCompat.Body1 = 0x7f13001b
com.example.uhf:dimen/m3_comp_elevated_card_container_elevation = 0x7f07010f
com.example.uhf:layout/abc_action_menu_layout = 0x7f0c0003
com.example.uhf:attr/windowActionBarOverlay = 0x7f040521
com.example.uhf:macro/m3_comp_navigation_bar_inactive_label_text_color = 0x7f0d0073
com.example.uhf:dimen/design_tab_text_size = 0x7f07008d
com.example.uhf:attr/itemHorizontalPadding = 0x7f04026a
com.example.uhf:string/tvPtr_Select = 0x7f12041e
com.example.uhf:color/m3_ref_palette_dynamic_neutral_variant94 = 0x7f0600e5
com.example.uhf:macro/m3_comp_filled_icon_button_toggle_selected_icon_color = 0x7f0d0049
com.example.uhf:id/material_timepicker_cancel_button = 0x7f0901aa
com.example.uhf:string/update_msg_diag_title = 0x7f1204a2
com.example.uhf:attr/motionDurationLong4 = 0x7f040349
com.example.uhf:attr/widgetLayout = 0x7f04051f
com.example.uhf:attr/dayInvalidStyle = 0x7f040176
com.example.uhf:drawable/mtrl_checkbox_button_icon_unchecked_checked = 0x7f0800d1
com.example.uhf:style/Base.V14.Theme.Material3.Light.Dialog = 0x7f130094
com.example.uhf:string/mtrl_checkbox_state_description_unchecked = 0x7f12028b
com.example.uhf:string/rfid_title_continuous_read_fail_count = 0x7f120359
com.example.uhf:attr/keyboardIcon = 0x7f040287
com.example.uhf:id/cbAutoUpload = 0x7f0900b2
com.example.uhf:attr/thumbIconSize = 0x7f0404bd
com.example.uhf:color/m3_ref_palette_neutral60 = 0x7f06012b
com.example.uhf:string/tvProtocol = 0x7f12041a
com.example.uhf:attr/waveOffset = 0x7f04051a
com.example.uhf:style/Base.V7.Theme.AppCompat = 0x7f1300bf
com.example.uhf:macro/m3_comp_switch_unselected_pressed_track_color = 0x7f0d013e
com.example.uhf:color/m3_ref_palette_error30 = 0x7f060115
com.example.uhf:style/TextAppearance.M3.Sys.Typescale.DisplayLarge = 0x7f130201
com.example.uhf:string/battery_tips_STATUS_FULL = 0x7f12007c
com.example.uhf:attr/appBarLayoutStyle = 0x7f04003d
com.example.uhf:attr/waveDecay = 0x7f040519
com.example.uhf:id/cache_measures = 0x7f0900ad
com.example.uhf:attr/viewTransitionOnPositiveCross = 0x7f040515
com.example.uhf:layout/fragment_uhf_radar_location = 0x7f0c0035
com.example.uhf:style/Base.Theme.MaterialComponents.CompactMenu = 0x7f13006b
com.example.uhf:attr/actionTextColorAlpha = 0x7f040024
com.example.uhf:id/action_set = 0x7f090069
com.example.uhf:attr/viewTransitionOnCross = 0x7f040513
com.example.uhf:string/gps_title_tip = 0x7f1201ce
com.example.uhf:attr/tint = 0x7f0404d1
com.example.uhf:attr/verticalOffsetWithText = 0x7f040510
com.example.uhf:integer/m3_chip_anim_duration = 0x7f0a000e
com.example.uhf:attr/useSimpleSummaryProvider = 0x7f04050d
com.example.uhf:id/open_search_view_toolbar = 0x7f0901f4
com.example.uhf:attr/bottomSheetDialogTheme = 0x7f040082
com.example.uhf:attr/triggerSlack = 0x7f040506
com.example.uhf:attr/actionBarStyle = 0x7f040007
com.example.uhf:color/switch_thumb_material_light = 0x7f06033c
com.example.uhf:macro/m3_comp_filled_tonal_icon_button_toggle_selected_icon_color = 0x7f0d0055
com.example.uhf:color/material_personalized_color_surface_container_high = 0x7f0602b6
com.example.uhf:macro/m3_comp_navigation_bar_inactive_hover_state_layer_color = 0x7f0d0071
com.example.uhf:dimen/m3_comp_suggestion_chip_container_height = 0x7f07018f
com.example.uhf:dimen/m3_appbar_scrim_height_trigger = 0x7f0700ab
com.example.uhf:attr/cornerFamilyTopRight = 0x7f04015a
com.example.uhf:attr/dialogTheme = 0x7f04018a
com.example.uhf:attr/simpleItemSelectedColor = 0x7f040409
com.example.uhf:attr/attributeName = 0x7f040042
com.example.uhf:attr/itemIconSize = 0x7f04026d
com.example.uhf:attr/transformPivotTarget = 0x7f0404fe
com.example.uhf:anim/slide_right_out = 0x7f010030
com.example.uhf:color/mtrl_card_view_ripple = 0x7f0602de
com.example.uhf:attr/trackStopIndicatorSize = 0x7f0404fa
com.example.uhf:style/Animation.AppCompat.DropDownUp = 0x7f130005
com.example.uhf:style/ThemeOverlay.MaterialComponents.Dialog = 0x7f1302f9
com.example.uhf:string/m3_sys_motion_easing_standard = 0x7f12021d
com.example.uhf:attr/trackDecoration = 0x7f0404f5
com.example.uhf:attr/trackColorActive = 0x7f0404f2
com.example.uhf:attr/fontProviderQuery = 0x7f040220
com.example.uhf:attr/chipIcon = 0x7f0400cb
com.example.uhf:attr/toolbarId = 0x7f0404e4
com.example.uhf:attr/textAppearanceLargePopupMenu = 0x7f04048d
com.example.uhf:attr/tabRippleColor = 0x7f04046a
com.example.uhf:color/m3_sys_color_dynamic_dark_primary = 0x7f0601ab
com.example.uhf:dimen/m3_sys_motion_easing_legacy_accelerate_control_y2 = 0x7f070206
com.example.uhf:attr/chipIconVisible = 0x7f0400cf
com.example.uhf:macro/m3_comp_filled_text_field_input_text_type = 0x7f0d0050
com.example.uhf:string/button_web_search = 0x7f1200e1
com.example.uhf:attr/floatingActionButtonLargeSecondaryStyle = 0x7f0401f8
com.example.uhf:dimen/m3_comp_outlined_card_outline_width = 0x7f070158
com.example.uhf:style/Base.MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f130019
com.example.uhf:attr/textAppearanceButton = 0x7f04047c
com.example.uhf:attr/toolbarNavigationButtonStyle = 0x7f0404e5
com.example.uhf:string/fingerprint_msg_set_Baudrate_succ = 0x7f120197
com.example.uhf:drawable/m3_tabs_background = 0x7f0800ba
com.example.uhf:attr/toggleCheckedStateOnClick = 0x7f0404e3
com.example.uhf:integer/abc_config_activityDefaultDur = 0x7f0a0000
com.example.uhf:attr/titleEnabled = 0x7f0404d7
com.example.uhf:layout/mtrl_auto_complete_simple_item = 0x7f0c0058
com.example.uhf:dimen/m3_sys_motion_easing_legacy_accelerate_control_x2 = 0x7f070204
com.example.uhf:attr/hintEnabled = 0x7f04023c
com.example.uhf:layout/abc_alert_dialog_material = 0x7f0c0009
com.example.uhf:attr/tickRadiusInactive = 0x7f0404cf
com.example.uhf:id/navigation_header_container = 0x7f0901d9
com.example.uhf:style/TextAppearance.AppCompat.Subhead.Inverse = 0x7f1301d7
com.example.uhf:color/abc_tint_spinner = 0x7f060017
com.example.uhf:attr/tickMarkTintMode = 0x7f0404cd
com.example.uhf:id/accessibility_custom_action_10 = 0x7f090035
com.example.uhf:drawable/$mtrl_checkbox_button_icon_unchecked_checked__2 = 0x7f08001a
com.example.uhf:dimen/m3_comp_time_input_time_input_field_focus_outline_width = 0x7f0701a4
com.example.uhf:attr/buttonIconTint = 0x7f04009a
com.example.uhf:bool/abc_action_bar_embed_tabs = 0x7f050000
com.example.uhf:attr/wavePeriod = 0x7f04051b
com.example.uhf:attr/colorOnError = 0x7f040109
com.example.uhf:attr/thumbTrackGapSize = 0x7f0404c6
com.example.uhf:string/title_97_infrared = 0x7f12038f
com.example.uhf:dimen/m3_appbar_size_compact = 0x7f0700ae
com.example.uhf:layout/fragment_block_write = 0x7f0c0031
com.example.uhf:array/fileEndingVideo = 0x7f030039
com.example.uhf:attr/thumbTintMode = 0x7f0404c5
com.example.uhf:dimen/tooltip_vertical_padding = 0x7f070352
com.example.uhf:color/m3_ref_palette_dynamic_neutral50 = 0x7f0600c5
com.example.uhf:attr/touchRegionId = 0x7f0404ef
com.example.uhf:macro/m3_comp_time_picker_period_selector_selected_focus_state_layer_color = 0x7f0d0156
com.example.uhf:attr/thumbTint = 0x7f0404c4
com.example.uhf:dimen/mtrl_btn_text_btn_padding_right = 0x7f070273
com.example.uhf:style/Base.TextAppearance.AppCompat.Display4 = 0x7f130022
com.example.uhf:attr/buttonCompat = 0x7f040096
com.example.uhf:styleable/CustomAttribute = 0x7f140032
com.example.uhf:string/abc_menu_shift_shortcut_label = 0x7f12002f
com.example.uhf:attr/dialogPreferredPadding = 0x7f040189
com.example.uhf:string/abc_prepend_shortcut_label = 0x7f120032
com.example.uhf:attr/subheaderInsetStart = 0x7f04043e
com.example.uhf:attr/thumbIcon = 0x7f0404bc
com.example.uhf:dimen/abc_action_bar_default_height_material = 0x7f070002
com.example.uhf:attr/extendedFloatingActionButtonSecondaryStyle = 0x7f0401e1
com.example.uhf:color/design_bottom_navigation_shadow_color = 0x7f060036
com.example.uhf:attr/round = 0x7f0403d7
com.example.uhf:attr/thumbHeight = 0x7f0404bb
com.example.uhf:attr/textAppearanceBodyLarge = 0x7f040479
com.example.uhf:attr/theme = 0x7f0404b7
com.example.uhf:dimen/m3_navigation_menu_headline_horizontal_padding = 0x7f0701cd
com.example.uhf:color/dim_foreground_disabled_material_light = 0x7f06005d
com.example.uhf:attr/springStiffness = 0x7f040421
com.example.uhf:id/FUNCTION = 0x7f090015
com.example.uhf:macro/m3_comp_time_picker_time_selector_selected_hover_state_layer_color = 0x7f0d0162
com.example.uhf:attr/colorErrorContainer = 0x7f040105
com.example.uhf:color/primary_text_disabled_material_dark = 0x7f060316
com.example.uhf:attr/circularflow_viewCenter = 0x7f0400e1
com.example.uhf:color/m3_ref_palette_secondary20 = 0x7f060153
com.example.uhf:attr/itemPaddingBottom = 0x7f040272
com.example.uhf:attr/textOutlineColor = 0x7f0404ae
com.example.uhf:color/white1 = 0x7f060347
com.example.uhf:string/network_not_connected = 0x7f1202cc
com.example.uhf:attr/textInputStyle = 0x7f0404ac
com.example.uhf:string/title_Check = 0x7f120390
com.example.uhf:attr/layout_constraintVertical_weight = 0x7f0402c1
com.example.uhf:attr/textInputLayoutFocusedRectEnabled = 0x7f0404a8
com.example.uhf:string/m3_sys_motion_easing_emphasized_path_data = 0x7f120218
com.example.uhf:id/m3_side_sheet = 0x7f090196
com.example.uhf:attr/perpendicularPath_percent = 0x7f04039f
com.example.uhf:attr/textInputFilledStyle = 0x7f0404a7
com.example.uhf:attr/motionDurationMedium2 = 0x7f04034b
com.example.uhf:attr/progressBarStyle = 0x7f0403be
com.example.uhf:color/design_dark_default_color_secondary = 0x7f060042
com.example.uhf:attr/backgroundStacked = 0x7f040056
com.example.uhf:macro/m3_comp_primary_navigation_tab_with_label_text_active_label_text_color = 0x7f0d00d1
com.example.uhf:attr/textInputFilledExposedDropdownMenuStyle = 0x7f0404a6
com.example.uhf:attr/textInputFilledDenseStyle = 0x7f0404a5
com.example.uhf:attr/startIconContentDescription = 0x7f040427
com.example.uhf:style/TextAppearance.Design.Hint = 0x7f1301f8
com.example.uhf:attr/textColorAlertDialogListItem = 0x7f0404a1
com.example.uhf:macro/m3_comp_extended_fab_secondary_icon_color = 0x7f0d0031
com.example.uhf:dimen/mtrl_navigation_rail_compact_width = 0x7f0702d1
com.example.uhf:color/blue2 = 0x7f060025
com.example.uhf:macro/m3_comp_time_picker_time_selector_selected_container_color = 0x7f0d0160
com.example.uhf:macro/m3_comp_search_view_header_trailing_icon_color = 0x7f0d00f9
com.example.uhf:color/design_default_color_background = 0x7f060045
com.example.uhf:attr/ratingBarStyle = 0x7f0403c7
com.example.uhf:attr/textAppearanceTitleLarge = 0x7f040499
com.example.uhf:string/fingerprint_msg_set_PSW_fail = 0x7f120198
com.example.uhf:attr/subtitle = 0x7f040441
com.example.uhf:animator/m3_extended_fab_hide_motion_spec = 0x7f020012
com.example.uhf:color/m3_slider_active_track_color = 0x7f06016d
com.example.uhf:anim/m3_bottom_sheet_slide_out = 0x7f010023
com.example.uhf:attr/textAppearanceSearchResultTitle = 0x7f040495
com.example.uhf:attr/state_indeterminate = 0x7f040432
com.example.uhf:attr/textAppearanceListItemSecondary = 0x7f040490
com.example.uhf:string/download_msg_down_fail = 0x7f12014c
com.example.uhf:dimen/m3_comp_primary_navigation_tab_inactive_hover_state_layer_opacity = 0x7f070164
com.example.uhf:attr/flow_horizontalBias = 0x7f04020b
com.example.uhf:styleable/AppBarLayout_Layout = 0x7f14000e
com.example.uhf:attr/popupMenuStyle = 0x7f0403a9
com.example.uhf:attr/textAppearanceHeadlineMedium = 0x7f040488
com.example.uhf:attr/textAppearanceHeadlineLarge = 0x7f040487
com.example.uhf:string/led_msg_close_fail = 0x7f1201e5
com.example.uhf:dimen/m3_comp_filled_card_container_elevation = 0x7f070129
com.example.uhf:attr/textAppearanceHeadline3 = 0x7f040483
com.example.uhf:macro/m3_comp_navigation_rail_inactive_pressed_state_layer_color = 0x7f0d009e
com.example.uhf:attr/textAppearanceDisplayMedium = 0x7f04047f
com.example.uhf:drawable/notification_template_icon_low_bg = 0x7f0800f9
com.example.uhf:attr/collapsingToolbarLayoutMediumStyle = 0x7f0400fa
com.example.uhf:dimen/m3_searchview_height = 0x7f0701e9
com.example.uhf:string/file_btn_confirm = 0x7f120171
com.example.uhf:style/Widget.Material3.Chip.Input.Icon.Elevated = 0x7f130398
com.example.uhf:color/m3_ref_palette_primary50 = 0x7f060149
com.example.uhf:attr/textAppearanceBodySmall = 0x7f04047b
com.example.uhf:attr/textAppearanceBodyMedium = 0x7f04047a
com.example.uhf:dimen/network_list_address_size = 0x7f070311
com.example.uhf:dimen/mtrl_slider_thumb_radius = 0x7f0702ef
com.example.uhf:id/btOK = 0x7f09008a
com.example.uhf:attr/title = 0x7f0404d4
com.example.uhf:attr/colorOnSecondaryContainer = 0x7f040111
com.example.uhf:color/m3_sys_color_dynamic_dark_surface_container_high = 0x7f0601b2
com.example.uhf:attr/textAppearanceBody2 = 0x7f040478
com.example.uhf:attr/textAppearanceBody1 = 0x7f040477
com.example.uhf:attr/telltales_velocityMode = 0x7f040475
com.example.uhf:style/Base.Theme.MaterialComponents.Light = 0x7f130072
com.example.uhf:menu/main = 0x7f0e0000
com.example.uhf:id/btnGetFrequency = 0x7f090095
com.example.uhf:string/msg_barcode_set = 0x7f12023e
com.example.uhf:anim/btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017
com.example.uhf:attr/tabSecondaryStyle = 0x7f04046b
com.example.uhf:string/up_one_level = 0x7f12049f
com.example.uhf:attr/contentScrim = 0x7f040151
com.example.uhf:macro/m3_comp_navigation_drawer_inactive_icon_color = 0x7f0d008c
com.example.uhf:macro/m3_comp_primary_navigation_tab_with_label_text_inactive_label_text_color = 0x7f0d00d2
com.example.uhf:attr/cornerSizeBottomRight = 0x7f04015e
com.example.uhf:color/design_dark_default_color_primary_dark = 0x7f060040
com.example.uhf:style/Platform.V25.AppCompat = 0x7f13014d
com.example.uhf:attr/actionDropDownStyle = 0x7f04000e
com.example.uhf:color/material_harmonized_color_error_container = 0x7f060287
com.example.uhf:attr/reactiveGuide_animateChange = 0x7f0403ca
com.example.uhf:attr/tabPaddingTop = 0x7f040469
com.example.uhf:string/title_connecting = 0x7f1203d0
com.example.uhf:dimen/mtrl_snackbar_action_text_color_alpha = 0x7f0702f5
com.example.uhf:attr/motionDebug = 0x7f040341
com.example.uhf:anim/btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010
com.example.uhf:string/title_about_display = 0x7f12039a
com.example.uhf:style/Widget.Design.AppBarLayout = 0x7f13035d
com.example.uhf:attr/paddingStartSystemWindowInsets = 0x7f04038e
com.example.uhf:id/dragRight = 0x7f090111
com.example.uhf:style/Base.V14.ThemeOverlay.Material3.BottomSheetDialog = 0x7f13009f
com.example.uhf:attr/tabPaddingStart = 0x7f040468
com.example.uhf:style/Base.Widget.Material3.MaterialCalendar.NavigationButton = 0x7f130111
com.example.uhf:macro/m3_comp_outlined_text_field_outline_color = 0x7f0d00c3
com.example.uhf:style/ThemeOverlay.AppCompat.Dark = 0x7f1302a2
com.example.uhf:attr/layout_constraintDimensionRatio = 0x7f0402a5
com.example.uhf:attr/tabPadding = 0x7f040465
com.example.uhf:array/arrayBank2 = 0x7f030007
com.example.uhf:color/design_dark_default_color_on_secondary = 0x7f06003d
com.example.uhf:macro/m3_comp_filled_autocomplete_text_field_input_text_type = 0x7f0d0042
com.example.uhf:attr/tabMaxWidth = 0x7f040462
com.example.uhf:attr/trackDecorationTint = 0x7f0404f6
com.example.uhf:attr/tabInlineLabel = 0x7f040461
com.example.uhf:string/lf_title_data = 0x7f1201ee
com.example.uhf:id/open_search_view_divider = 0x7f0901ec
com.example.uhf:attr/tabIndicatorAnimationMode = 0x7f04045c
com.example.uhf:attr/seekBarStyle = 0x7f0403e6
com.example.uhf:id/startToEnd = 0x7f090283
com.example.uhf:attr/tabPaddingBottom = 0x7f040466
com.example.uhf:string/preferences_bulk_mode_title = 0x7f1202f1
com.example.uhf:string/history_send = 0x7f1201d8
com.example.uhf:attr/path_percent = 0x7f04039a
com.example.uhf:style/ThemeOverlay.Material3 = 0x7f1302aa
com.example.uhf:color/half_transparent = 0x7f060071
com.example.uhf:attr/actionViewClass = 0x7f040025
com.example.uhf:macro/m3_comp_date_picker_modal_date_today_container_outline_color = 0x7f0d0012
com.example.uhf:attr/textEndPadding = 0x7f0404a3
com.example.uhf:attr/materialSearchViewStyle = 0x7f04031e
com.example.uhf:attr/altSrc = 0x7f040036
com.example.uhf:attr/colorOnSurfaceVariant = 0x7f040116
com.example.uhf:attr/materialCalendarMonth = 0x7f04030a
com.example.uhf:dimen/m3_comp_switch_selected_pressed_state_layer_opacity = 0x7f07019b
com.example.uhf:dimen/mtrl_navigation_item_shape_vertical_margin = 0x7f0702cf
com.example.uhf:style/Platform.V21.AppCompat.Light = 0x7f13014c
com.example.uhf:dimen/m3_btn_dialog_btn_min_width = 0x7f0700d0
com.example.uhf:dimen/abc_switch_padding = 0x7f07003e
com.example.uhf:string/location_fail = 0x7f1201f8
com.example.uhf:string/title_QTTag = 0x7f120393
com.example.uhf:attr/summaryOff = 0x7f04044b
com.example.uhf:attr/tabBackground = 0x7f040455
com.example.uhf:attr/textPanY = 0x7f0404b1
com.example.uhf:string/call_notification_ongoing_text = 0x7f1200e8
com.example.uhf:dimen/m3_toolbar_text_size_title = 0x7f070225
com.example.uhf:color/material_blue_grey_800 = 0x7f06022f
com.example.uhf:dimen/m3_sys_motion_easing_standard_accelerate_control_y2 = 0x7f070216
com.example.uhf:attr/switchTextOff = 0x7f040453
com.example.uhf:string/desfire_title_link_encry = 0x7f120130
com.example.uhf:color/red3 = 0x7f06031c
com.example.uhf:string/rfid_mgs_killpwdtip = 0x7f12033e
com.example.uhf:attr/textureEffect = 0x7f0404b4
com.example.uhf:attr/switchPreferenceCompatStyle = 0x7f04044f
com.example.uhf:macro/m3_comp_outlined_text_field_error_outline_color = 0x7f0d00b6
com.example.uhf:style/TextAppearance.AppCompat.Large = 0x7f1301c9
com.example.uhf:id/selected = 0x7f090258
com.example.uhf:attr/colorOnTertiaryFixed = 0x7f040119
com.example.uhf:styleable/ViewPager2 = 0x7f1400aa
com.example.uhf:dimen/m3_comp_navigation_drawer_pressed_state_layer_opacity = 0x7f070148
com.example.uhf:attr/strokeWidth = 0x7f04043a
com.example.uhf:attr/labelVisibilityMode = 0x7f04028c
com.example.uhf:attr/wavePhase = 0x7f04051c
com.example.uhf:attr/thumbIconTintMode = 0x7f0404bf
com.example.uhf:string/desfire_title_sel_app_fail = 0x7f12013b
com.example.uhf:string/OAUTH_AccessToken_ACCESS = 0x7f120010
com.example.uhf:dimen/m3_comp_fab_primary_icon_size = 0x7f07011f
com.example.uhf:attr/summary = 0x7f04044a
com.example.uhf:attr/selectableItemBackgroundBorderless = 0x7f0403e9
com.example.uhf:dimen/m3_comp_fab_primary_large_container_height = 0x7f070120
com.example.uhf:attr/switchMinWidth = 0x7f04044d
com.example.uhf:styleable/LinearProgressIndicator = 0x7f140050
com.example.uhf:attr/suffixTextColor = 0x7f040448
com.example.uhf:dimen/m3_comp_filled_text_field_disabled_active_indicator_opacity = 0x7f07012f
com.example.uhf:attr/textStartPadding = 0x7f0404b2
com.example.uhf:attr/suffixText = 0x7f040446
com.example.uhf:dimen/abc_action_bar_content_inset_with_nav = 0x7f070001
com.example.uhf:color/material_dynamic_neutral60 = 0x7f060245
com.example.uhf:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f13041b
com.example.uhf:attr/subtitleTextStyle = 0x7f040445
com.example.uhf:string/fingerprint_title_name = 0x7f1201ac
com.example.uhf:color/m3_ref_palette_error60 = 0x7f060118
com.example.uhf:id/RgInventory = 0x7f09001b
com.example.uhf:array/c4000icon = 0x7f03002c
com.example.uhf:string/preferences_try_bsplus = 0x7f120308
com.example.uhf:style/TextAppearance.M3.Sys.Typescale.DisplaySmall = 0x7f130203
com.example.uhf:attr/subheaderTextAppearance = 0x7f04043f
com.example.uhf:id/transition_current_scene = 0x7f0902b6
com.example.uhf:color/material_personalized__highlighted_text = 0x7f060294
com.example.uhf:style/Platform.ThemeOverlay.AppCompat.Light = 0x7f13014a
com.example.uhf:attr/windowMinWidthMinor = 0x7f040528
com.example.uhf:color/tooltip_background_dark = 0x7f060340
com.example.uhf:attr/subheaderInsetEnd = 0x7f04043d
com.example.uhf:color/m3_sys_color_dynamic_light_error = 0x7f0601bb
com.example.uhf:attr/subMenuArrow = 0x7f04043b
com.example.uhf:dimen/material_clock_size = 0x7f070234
com.example.uhf:attr/activityAction = 0x7f040027
com.example.uhf:attr/extendedFloatingActionButtonPrimaryStyle = 0x7f0401e0
com.example.uhf:attr/showText = 0x7f040403
com.example.uhf:attr/buttonBarNegativeButtonStyle = 0x7f040092
com.example.uhf:attr/tabPaddingEnd = 0x7f040467
com.example.uhf:dimen/network_list_state_width = 0x7f070319
com.example.uhf:macro/m3_comp_badge_color = 0x7f0d0002
com.example.uhf:dimen/abc_config_prefDialogWidth = 0x7f070017
com.example.uhf:color/material_dynamic_secondary20 = 0x7f060268
com.example.uhf:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f130477
com.example.uhf:dimen/material_helper_text_font_1_3_padding_top = 0x7f070244
com.example.uhf:layout/mtrl_alert_dialog_actions = 0x7f0c0053
com.example.uhf:dimen/m3_searchbar_padding_start = 0x7f0701e4
com.example.uhf:color/abc_background_cache_hint_selector_material_dark = 0x7f060000
com.example.uhf:style/Widget.Material3.BottomAppBar = 0x7f130371
com.example.uhf:style/Theme.Material3.Light.Dialog.Alert = 0x7f13026b
com.example.uhf:dimen/notification_top_pad = 0x7f070327
com.example.uhf:anim/btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c
com.example.uhf:attr/startIconMinSize = 0x7f040429
com.example.uhf:string/uhf_title_write = 0x7f12048e
com.example.uhf:string/preferences_play_beep_title = 0x7f120300
com.example.uhf:integer/m3_btn_anim_delay_ms = 0x7f0a000a
com.example.uhf:attr/springStopThreshold = 0x7f040422
com.example.uhf:id/btnFactoryReset = 0x7f090092
com.example.uhf:attr/boxStrokeErrorColor = 0x7f04008d
com.example.uhf:array/arrayLF = 0x7f030015
com.example.uhf:dimen/m3_navigation_item_active_indicator_label_padding = 0x7f0701c4
com.example.uhf:attr/commitIcon = 0x7f040139
com.example.uhf:macro/m3_comp_top_app_bar_medium_headline_color = 0x7f0d016d
com.example.uhf:attr/springBoundary = 0x7f04041e
com.example.uhf:color/material_deep_teal_200 = 0x7f060233
com.example.uhf:id/navigation_bar_item_icon_view = 0x7f0901d5
com.example.uhf:dimen/mtrl_chip_pressed_translation_z = 0x7f0702a6
com.example.uhf:attr/forceDefaultNavigationOnClickListener = 0x7f040226
com.example.uhf:color/m3_sys_color_dynamic_light_surface_container_low = 0x7f0601d6
com.example.uhf:dimen/m3_comp_suggestion_chip_flat_outline_width = 0x7f070192
com.example.uhf:attr/titleMarginTop = 0x7f0404dc
com.example.uhf:macro/m3_comp_switch_disabled_selected_handle_color = 0x7f0d0119
com.example.uhf:attr/minWidth = 0x7f04033a
com.example.uhf:attr/snackbarStyle = 0x7f040413
com.example.uhf:attr/actionBarTabTextStyle = 0x7f04000a
com.example.uhf:drawable/abc_tab_indicator_mtrl_alpha = 0x7f08006d
com.example.uhf:attr/textBackgroundZoom = 0x7f0404a0
com.example.uhf:dimen/m3_searchbar_height = 0x7f0701e0
com.example.uhf:attr/sliderStyle = 0x7f040411
com.example.uhf:attr/sizePercent = 0x7f040410
com.example.uhf:id/screen = 0x7f090244
com.example.uhf:attr/percentHeight = 0x7f04039b
com.example.uhf:dimen/notification_action_text_size = 0x7f07031b
com.example.uhf:color/m3_ref_palette_dynamic_neutral_variant6 = 0x7f0600de
com.example.uhf:color/m3_sys_color_dynamic_light_surface_dim = 0x7f0601d8
com.example.uhf:attr/textOutlineThickness = 0x7f0404af
com.example.uhf:dimen/m3_btn_icon_only_icon_padding = 0x7f0700da
com.example.uhf:attr/simpleItemLayout = 0x7f040408
com.example.uhf:dimen/abc_control_corner_material = 0x7f070018
com.example.uhf:dimen/m3_comp_navigation_drawer_standard_container_elevation = 0x7f070149
com.example.uhf:drawable/ic_arrow_down_24dp = 0x7f08009b
com.example.uhf:attr/layout_constraintLeft_toRightOf = 0x7f0402b5
com.example.uhf:color/m3_ref_palette_dynamic_primary50 = 0x7f0600f0
com.example.uhf:attr/layout_constraintBaseline_toTopOf = 0x7f04029e
com.example.uhf:attr/showMarker = 0x7f0403ff
com.example.uhf:attr/simpleItems = 0x7f04040b
com.example.uhf:style/Widget.MaterialComponents.Light.ActionBar.Solid = 0x7f130446
com.example.uhf:string/summary_collapsed_preference_list = 0x7f120384
com.example.uhf:attr/rotationCenterId = 0x7f0403d6
com.example.uhf:attr/carousel_alignment = 0x7f0400a9
com.example.uhf:attr/shapeAppearanceSmallComponent = 0x7f0403f6
com.example.uhf:color/preference_fallback_accent_color = 0x7f06030f
com.example.uhf:attr/drawableStartCompat = 0x7f04019f
com.example.uhf:attr/tabUnboundedRipple = 0x7f040471
com.example.uhf:attr/showDelay = 0x7f0403fd
com.example.uhf:array/arrayPSAMBaud = 0x7f03001f
com.example.uhf:attr/minHeight = 0x7f040336
com.example.uhf:attr/sideSheetModalStyle = 0x7f040407
com.example.uhf:layout/preference_dialog_edittext = 0x7f0c007c
com.example.uhf:layout/abc_popup_menu_item_layout = 0x7f0c0013
com.example.uhf:color/m3_sys_color_light_error = 0x7f0601e9
com.example.uhf:string/uhf_msg_read_frequency_fail = 0x7f120458
com.example.uhf:attr/materialDisplayDividerStyle = 0x7f040315
com.example.uhf:interpolator/m3_sys_motion_easing_linear = 0x7f0b000a
com.example.uhf:attr/shapeAppearanceMediumComponent = 0x7f0403f4
com.example.uhf:attr/textPanX = 0x7f0404b0
com.example.uhf:attr/actionBarItemBackground = 0x7f040003
com.example.uhf:attr/polarRelativeTo = 0x7f0403a7
com.example.uhf:color/m3_ref_palette_tertiary50 = 0x7f060163
com.example.uhf:attr/shapeAppearanceCornerLarge = 0x7f0403f0
com.example.uhf:attr/shapeAppearance = 0x7f0403ed
com.example.uhf:string/title_about_cpu = 0x7f120397
com.example.uhf:string/b14443_msg_init_err = 0x7f120063
com.example.uhf:attr/seekBarPreferenceStyle = 0x7f0403e5
com.example.uhf:string/title_del = 0x7f1203d2
com.example.uhf:dimen/design_navigation_separator_vertical_padding = 0x7f07007f
com.example.uhf:attr/sideSheetDialogTheme = 0x7f040406
com.example.uhf:styleable/MaterialAutoCompleteTextView = 0x7f140055
com.example.uhf:attr/applyMotionScene = 0x7f04003e
com.example.uhf:attr/seekBarIncrement = 0x7f0403e4
com.example.uhf:color/m3_ref_palette_dynamic_tertiary60 = 0x7f06010b
com.example.uhf:attr/secondaryActivityAction = 0x7f0403e2
com.example.uhf:attr/preferenceInformationStyle = 0x7f0403b3
com.example.uhf:attr/searchPrefixText = 0x7f0403e0
com.example.uhf:string/btReadUii = 0x7f1200b7
com.example.uhf:color/m3_sys_color_dark_outline = 0x7f060187
com.example.uhf:attr/scrimVisibleHeightTrigger = 0x7f0403dd
com.example.uhf:color/androidx_core_secondary_text_default_material_light = 0x7f06001c
com.example.uhf:attr/shouldRemoveExpandedCorners = 0x7f0403fa
com.example.uhf:string/preferences_name = 0x7f1202ff
com.example.uhf:string/fingerprint_btn_set_Threshold = 0x7f120182
com.example.uhf:macro/m3_comp_outlined_text_field_error_supporting_text_color = 0x7f0d00b7
com.example.uhf:attr/scaleFromTextSize = 0x7f0403da
com.example.uhf:style/Preference.SeekBarPreference = 0x7f130160
com.example.uhf:id/etPtr_light_filter = 0x7f090140
com.example.uhf:dimen/m3_comp_extended_fab_primary_container_height = 0x7f070112
com.example.uhf:string/desfire_title_value_balance = 0x7f12013e
com.example.uhf:attr/preferenceCategoryStyle = 0x7f0403ad
com.example.uhf:dimen/mtrl_shape_corner_size_large_component = 0x7f0702e7
com.example.uhf:string/btUii = 0x7f1200c0
com.example.uhf:attr/removeEmbeddedFabElevation = 0x7f0403d3
com.example.uhf:color/m3_ref_palette_neutral10 = 0x7f06011f
com.example.uhf:drawable/packed = 0x7f0800fc
com.example.uhf:attr/textAppearanceHeadline2 = 0x7f040482
com.example.uhf:string/update_title_cancel = 0x7f1204a7
com.example.uhf:string/menu_share = 0x7f12023b
com.example.uhf:style/Widget.Material3.SideSheet.Modal.Detached = 0x7f1303f8
com.example.uhf:dimen/mtrl_calendar_header_toggle_margin_bottom = 0x7f07028a
com.example.uhf:style/Widget.MaterialComponents.Button.UnelevatedButton = 0x7f130432
com.example.uhf:attr/textAppearancePopupMenuHeader = 0x7f040493
com.example.uhf:id/androidx_window_activity_scope = 0x7f090073
com.example.uhf:attr/reactiveGuide_applyToAllConstraintSets = 0x7f0403cb
com.example.uhf:string/msg_init_fail = 0x7f120253
com.example.uhf:attr/triggerReceiver = 0x7f040505
com.example.uhf:attr/materialCalendarFullscreenTheme = 0x7f040302
com.example.uhf:integer/m3_card_anim_delay_ms = 0x7f0a000c
com.example.uhf:id/SHOW_ALL = 0x7f09001d
com.example.uhf:attr/queryBackground = 0x7f0403c2
com.example.uhf:color/material_dynamic_neutral50 = 0x7f060244
com.example.uhf:attr/quantizeMotionSteps = 0x7f0403c1
com.example.uhf:attr/headerLayout = 0x7f040230
com.example.uhf:dimen/mtrl_btn_pressed_z = 0x7f07026e
com.example.uhf:style/Base.V21.Theme.AppCompat.Dialog = 0x7f1300a7
com.example.uhf:string/network_msg_emergency_only = 0x7f1202bf
com.example.uhf:integer/mtrl_view_visible = 0x7f0a0041
com.example.uhf:integer/mtrl_card_anim_delay_ms = 0x7f0a0033
com.example.uhf:dimen/m3_comp_filled_card_focus_state_layer_opacity = 0x7f07012b
com.example.uhf:attr/expandedTitleTextColor = 0x7f0401dd
com.example.uhf:styleable/MotionScene = 0x7f14006e
com.example.uhf:attr/expandedTitleMarginStart = 0x7f0401da
com.example.uhf:dimen/m3_comp_outlined_text_field_disabled_supporting_text_opacity = 0x7f07015c
com.example.uhf:attr/pressedTranslationZ = 0x7f0403bb
com.example.uhf:color/material_dynamic_neutral_variant90 = 0x7f060255
com.example.uhf:style/Base.ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f13008c
com.example.uhf:attr/toolbarSurfaceStyle = 0x7f0404e7
com.example.uhf:id/BtClear = 0x7f090003
com.example.uhf:string/title_activity_printer = 0x7f1203c0
com.example.uhf:macro/m3_comp_time_picker_period_selector_selected_container_color = 0x7f0d0155
com.example.uhf:attr/passwordToggleContentDescription = 0x7f040394
com.example.uhf:string/mtrl_switch_track_decoration_path = 0x7f1202ba
com.example.uhf:string/factory_reset = 0x7f12016d
com.example.uhf:dimen/m3_back_progress_main_container_min_edge_gap = 0x7f0700b4
com.example.uhf:dimen/m3_comp_fab_primary_pressed_state_layer_opacity = 0x7f070123
com.example.uhf:dimen/m3_comp_navigation_bar_active_indicator_height = 0x7f07013b
com.example.uhf:dimen/m3_slider_thumb_elevation = 0x7f0701f0
com.example.uhf:attr/preferenceScreenStyle = 0x7f0403b4
com.example.uhf:attr/shapeAppearanceCornerMedium = 0x7f0403f1
com.example.uhf:attr/flow_padding = 0x7f040213
com.example.uhf:attr/preferenceFragmentStyle = 0x7f0403b2
com.example.uhf:color/design_fab_stroke_top_outer_color = 0x7f060059
com.example.uhf:string/fingerprint_btn_Vfy_PSW = 0x7f120175
com.example.uhf:dimen/m3_comp_fab_primary_hover_state_layer_opacity = 0x7f07011e
com.example.uhf:attr/reverseLayout = 0x7f0403d4
com.example.uhf:dimen/abc_dialog_min_width_major = 0x7f070022
com.example.uhf:dimen/design_navigation_elevation = 0x7f070077
com.example.uhf:drawable/check_text_color = 0x7f080090
com.example.uhf:dimen/text_size_31 = 0x7f070348
com.example.uhf:color/m3_ref_palette_neutral12 = 0x7f060121
com.example.uhf:attr/popupWindowStyle = 0x7f0403ab
com.example.uhf:id/continuousVelocity = 0x7f0900ec
com.example.uhf:attr/hideMotionSpec = 0x7f040237
com.example.uhf:string/button_share_by_email = 0x7f1200db
com.example.uhf:attr/popupTheme = 0x7f0403aa
com.example.uhf:string/ml_title_read_id = 0x7f12023c
com.example.uhf:dimen/tooltip_y_offset_touch = 0x7f070354
com.example.uhf:drawable/seekbar_bg = 0x7f080104
com.example.uhf:attr/flow_horizontalStyle = 0x7f04020d
com.example.uhf:attr/numericModifiers = 0x7f04037b
com.example.uhf:attr/placeholder_emptyVisibility = 0x7f0403a6
com.example.uhf:layout/material_radial_view_group = 0x7f0c004b
com.example.uhf:attr/itemPaddingTop = 0x7f040273
com.example.uhf:id/EtAccessPwd_Write = 0x7f09000b
com.example.uhf:color/material_dynamic_neutral0 = 0x7f06023e
com.example.uhf:id/btnSetProtocol = 0x7f0900a5
com.example.uhf:color/mtrl_navigation_bar_colored_ripple_color = 0x7f0602ef
com.example.uhf:dimen/text_size_26 = 0x7f070343
com.example.uhf:macro/m3_comp_navigation_bar_inactive_focus_icon_color = 0x7f0d006c
com.example.uhf:dimen/material_textinput_min_width = 0x7f070248
com.example.uhf:attr/state_with_icon = 0x7f040435
com.example.uhf:color/m3_sys_color_primary_fixed_dim = 0x7f060211
com.example.uhf:attr/shortcutMatchRequired = 0x7f0403f8
com.example.uhf:macro/m3_comp_outlined_card_outline_color = 0x7f0d00ae
com.example.uhf:animator/mtrl_fab_hide_motion_spec = 0x7f02001e
com.example.uhf:attr/lastBaselineToBottomHeight = 0x7f04028e
com.example.uhf:attr/startIconTintMode = 0x7f04042c
com.example.uhf:macro/m3_comp_filled_text_field_container_shape = 0x7f0d004c
com.example.uhf:attr/exampleDrawable = 0x7f0401d1
com.example.uhf:attr/textAppearanceDisplayLarge = 0x7f04047e
com.example.uhf:attr/helperTextTextAppearance = 0x7f040234
com.example.uhf:attr/clockIcon = 0x7f0400e7
com.example.uhf:attr/navigationViewStyle = 0x7f040375
com.example.uhf:dimen/m3_comp_radio_button_unselected_pressed_state_layer_opacity = 0x7f070171
com.example.uhf:attr/paddingStart = 0x7f04038d
com.example.uhf:dimen/m3_comp_fab_primary_container_elevation = 0x7f07011a
com.example.uhf:attr/paddingBottomNoButtons = 0x7f040388
com.example.uhf:attr/overlay = 0x7f040387
com.example.uhf:dimen/mtrl_calendar_day_vertical_padding = 0x7f07027f
com.example.uhf:attr/counterOverflowTextColor = 0x7f040164
com.example.uhf:layout/select_dialog_multichoice_material = 0x7f0c008b
com.example.uhf:dimen/m3_comp_filled_card_dragged_state_layer_opacity = 0x7f07012a
com.example.uhf:attr/onTouchUp = 0x7f040383
com.example.uhf:attr/onPositiveCross = 0x7f040380
com.example.uhf:attr/onNegativeCross = 0x7f04037f
com.example.uhf:attr/carousel_backwardTransition = 0x7f0400aa
com.example.uhf:id/textinput_placeholder = 0x7f0902a8
com.example.uhf:attr/onCross = 0x7f04037d
com.example.uhf:dimen/abc_text_size_display_1_material = 0x7f070043
com.example.uhf:color/primary_text_default_material_dark = 0x7f060314
com.example.uhf:attr/offsetAlignmentMode = 0x7f04037c
com.example.uhf:attr/viewTransitionOnNegativeCross = 0x7f040514
com.example.uhf:attr/behavior_draggable = 0x7f040070
com.example.uhf:attr/nestedScrollable = 0x7f040379
com.example.uhf:attr/chipStrokeColor = 0x7f0400d7
com.example.uhf:attr/motionEffect_end = 0x7f04035f
com.example.uhf:integer/mtrl_switch_thumb_post_morphing_duration = 0x7f0a0037
com.example.uhf:attr/negativeButtonText = 0x7f040376
com.example.uhf:attr/navigationRailStyle = 0x7f040374
com.example.uhf:attr/materialIconButtonOutlinedStyle = 0x7f04031a
com.example.uhf:attr/navigationIcon = 0x7f040371
com.example.uhf:id/embed = 0x7f09011d
com.example.uhf:macro/m3_comp_menu_list_item_selected_container_color = 0x7f0d005e
com.example.uhf:color/m3_ref_palette_primary10 = 0x7f060144
com.example.uhf:attr/showSeekBarValue = 0x7f040402
com.example.uhf:anim/abc_slide_out_top = 0x7f010009
com.example.uhf:layout/material_clockface_textview = 0x7f0c0049
com.example.uhf:style/MaterialAlertDialog.Material3.Title.Text.CenterStacked = 0x7f130132
com.example.uhf:color/m3_ref_palette_dynamic_neutral_variant60 = 0x7f0600df
com.example.uhf:attr/flow_verticalBias = 0x7f040215
com.example.uhf:style/Theme.Material3.Dark.Dialog.MinWidth = 0x7f130256
com.example.uhf:attr/backgroundTint = 0x7f040057
com.example.uhf:color/accent_material_light = 0x7f06001a
com.example.uhf:color/m3_dynamic_primary_text_disable_only = 0x7f0600a1
com.example.uhf:attr/motionPathRotate = 0x7f040368
com.example.uhf:color/material_dynamic_neutral_variant99 = 0x7f060257
com.example.uhf:attr/materialDividerStyle = 0x7f040317
com.example.uhf:attr/motionInterpolator = 0x7f040366
com.example.uhf:string/download_msg_close = 0x7f120149
com.example.uhf:attr/motionEffect_viewTransition = 0x7f040365
com.example.uhf:attr/colorPrimaryFixedDim = 0x7f040121
com.example.uhf:dimen/disabled_alpha_material_light = 0x7f070091
com.example.uhf:attr/clockFaceBackgroundColor = 0x7f0400e5
com.example.uhf:macro/m3_comp_navigation_drawer_active_hover_state_layer_color = 0x7f0d007d
com.example.uhf:attr/motionEffect_move = 0x7f040360
com.example.uhf:attr/layout_goneMarginEnd = 0x7f0402cc
com.example.uhf:attr/expandActivityOverflowButtonDrawable = 0x7f0401d3
com.example.uhf:id/NO_DEBUG = 0x7f090018
com.example.uhf:attr/contentInsetEnd = 0x7f040144
com.example.uhf:attr/motionEasingLinearInterpolator = 0x7f040359
com.example.uhf:animator/m3_extended_fab_change_size_expand_motion_spec = 0x7f020011
com.example.uhf:drawable/abc_ic_clear_material = 0x7f080040
com.example.uhf:attr/collapsedTitleTextAppearance = 0x7f0400f5
com.example.uhf:attr/coordinatorLayoutStyle = 0x7f040154
com.example.uhf:attr/scrimAnimationDuration = 0x7f0403db
com.example.uhf:attr/motionEasingLinear = 0x7f040358
com.example.uhf:color/material_dynamic_color_dark_error = 0x7f060236
com.example.uhf:id/snap = 0x7f090266
com.example.uhf:attr/switchStyle = 0x7f040451
com.example.uhf:string/Weibo_Share_Repeat = 0x7f12001f
com.example.uhf:attr/motionDurationLong1 = 0x7f040346
com.example.uhf:attr/motionEasingStandard = 0x7f04035a
com.example.uhf:dimen/mtrl_extended_fab_icon_text_spacing = 0x7f0702b2
com.example.uhf:dimen/notification_small_icon_size_as_large = 0x7f070325
com.example.uhf:attr/motionEasingEmphasizedInterpolator = 0x7f040357
com.example.uhf:string/fingerprint_btn_get_Threshold = 0x7f120179
com.example.uhf:id/rbEPC_filter_deact = 0x7f090213
com.example.uhf:color/m3_ref_palette_dynamic_primary95 = 0x7f0600f5
com.example.uhf:attr/tabIndicatorHeight = 0x7f040460
com.example.uhf:style/Platform.ThemeOverlay.AppCompat = 0x7f130148
com.example.uhf:attr/iconPadding = 0x7f040247
com.example.uhf:attr/motionEasingEmphasizedAccelerateInterpolator = 0x7f040355
com.example.uhf:attr/iconSize = 0x7f040248
com.example.uhf:attr/motionEasingEmphasized = 0x7f040354
com.example.uhf:attr/motionDurationShort3 = 0x7f040350
com.example.uhf:attr/preferenceCategoryTitleTextAppearance = 0x7f0403ae
com.example.uhf:styleable/KeyFramesVelocity = 0x7f140049
com.example.uhf:dimen/mtrl_textinput_box_label_cutout_padding = 0x7f070303
com.example.uhf:color/m3_timepicker_display_background_color = 0x7f060228
com.example.uhf:attr/motionDurationMedium4 = 0x7f04034d
com.example.uhf:attr/motionDurationLong3 = 0x7f040348
com.example.uhf:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f1302b0
com.example.uhf:attr/motionTarget = 0x7f04036b
com.example.uhf:string/download_msg_down_succ = 0x7f120150
com.example.uhf:dimen/m3_back_progress_bottom_container_max_scale_y_distance = 0x7f0700b2
com.example.uhf:animator/m3_btn_state_list_anim = 0x7f02000b
com.example.uhf:color/material_personalized_color_primary_inverse = 0x7f0602ac
com.example.uhf:dimen/abc_cascading_menus_min_smallest_width = 0x7f070016
com.example.uhf:attr/fastScrollHorizontalTrackDrawable = 0x7f0401f1
com.example.uhf:attr/textInputOutlinedStyle = 0x7f0404ab
com.example.uhf:attr/textAppearanceListItemSmall = 0x7f040491
com.example.uhf:attr/motionDurationExtraLong4 = 0x7f040345
com.example.uhf:dimen/m3_comp_assist_chip_elevated_container_elevation = 0x7f070100
com.example.uhf:attr/motionDurationExtraLong2 = 0x7f040343
com.example.uhf:color/blue = 0x7f060023
com.example.uhf:string/title_ping_packet_size = 0x7f1203e3
com.example.uhf:layout/material_textinput_timepicker = 0x7f0c004c
com.example.uhf:string/desfire_title_pwd = 0x7f120134
com.example.uhf:array/defaultname = 0x7f030033
com.example.uhf:color/m3_ref_palette_dynamic_primary99 = 0x7f0600f6
com.example.uhf:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f130480
com.example.uhf:attr/mock_showDiagonals = 0x7f04033f
com.example.uhf:color/m3_sys_color_dynamic_on_secondary_fixed = 0x7f0601de
com.example.uhf:string/msg_init_succ = 0x7f120254
com.example.uhf:attr/mock_label = 0x7f04033c
com.example.uhf:id/btnSetMemoryBank = 0x7f0900a3
com.example.uhf:layout/material_clock_display_divider = 0x7f0c0046
com.example.uhf:attr/navigationIconTint = 0x7f040372
com.example.uhf:id/material_timepicker_ok_button = 0x7f0901ad
com.example.uhf:attr/minHideDelay = 0x7f040337
com.example.uhf:style/Widget.MaterialComponents.Toolbar.Surface = 0x7f13048f
com.example.uhf:array/arrayProtocol = 0x7f030023
com.example.uhf:color/m3_sys_color_light_surface = 0x7f0601ff
com.example.uhf:string/abc_menu_alt_shortcut_label = 0x7f120029
com.example.uhf:attr/itemShapeInsetTop = 0x7f04027b
com.example.uhf:dimen/mtrl_textinput_outline_box_expanded_padding = 0x7f070308
com.example.uhf:string/desfire_ms_unknown_file = 0x7f12010a
com.example.uhf:string/battery_tips_NOT_CHARGING = 0x7f120079
com.example.uhf:attr/paddingLeftSystemWindowInsets = 0x7f04038b
com.example.uhf:color/material_personalized__highlighted_text_inverse = 0x7f060295
com.example.uhf:dimen/m3_comp_primary_navigation_tab_inactive_pressed_state_layer_opacity = 0x7f070165
com.example.uhf:attr/menuGravity = 0x7f040333
com.example.uhf:attr/menuAlignmentMode = 0x7f040332
com.example.uhf:id/floating = 0x7f090156
com.example.uhf:attr/statusBarScrim = 0x7f040438
com.example.uhf:attr/measureWithLargestChild = 0x7f040330
com.example.uhf:attr/collapsingToolbarLayoutLargeStyle = 0x7f0400f8
com.example.uhf:attr/maxWidth = 0x7f04032f
com.example.uhf:string/battery_tips_HEALTH_OVER_VOLTAGE = 0x7f120077
com.example.uhf:dimen/mtrl_calendar_year_horizontal_padding = 0x7f07029d
com.example.uhf:attr/textAppearanceListItem = 0x7f04048f
com.example.uhf:dimen/m3_comp_filled_button_with_icon_icon_size = 0x7f070128
com.example.uhf:attr/motionEffect_strict = 0x7f040362
com.example.uhf:dimen/abc_action_bar_overflow_padding_end_material = 0x7f070007
com.example.uhf:attr/maxAcceleration = 0x7f040326
com.example.uhf:attr/errorAccessibilityLabel = 0x7f0401c5
com.example.uhf:color/mtrl_card_view_foreground = 0x7f0602dd
com.example.uhf:attr/fontProviderSystemFontFamily = 0x7f040221
com.example.uhf:attr/materialTimePickerTheme = 0x7f040324
com.example.uhf:string/tvKillPwd = 0x7f12040d
com.example.uhf:attr/materialSwitchStyle = 0x7f040321
com.example.uhf:dimen/m3_comp_switch_disabled_selected_handle_opacity = 0x7f070194
com.example.uhf:color/material_dynamic_tertiary0 = 0x7f060272
com.example.uhf:attr/materialSearchViewToolbarHeight = 0x7f04031f
com.example.uhf:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth = 0x7f13027f
com.example.uhf:attr/layout_editor_absoluteY = 0x7f0402c9
com.example.uhf:attr/listPreferredItemPaddingStart = 0x7f0402ee
com.example.uhf:dimen/m3_comp_navigation_bar_hover_state_layer_opacity = 0x7f070140
com.example.uhf:styleable/FontFamilyFont = 0x7f14003d
com.example.uhf:attr/materialIconButtonStyle = 0x7f04031b
com.example.uhf:attr/layout_constraintVertical_bias = 0x7f0402bf
com.example.uhf:macro/m3_comp_radio_button_selected_hover_state_layer_color = 0x7f0d00db
com.example.uhf:drawable/abc_textfield_search_material = 0x7f080076
com.example.uhf:attr/tickMarkTint = 0x7f0404cc
com.example.uhf:attr/materialDividerHeavyStyle = 0x7f040316
com.example.uhf:dimen/m3_nav_badge_with_text_vertical_offset = 0x7f0701c2
com.example.uhf:string/abc_menu_sym_shortcut_label = 0x7f120031
com.example.uhf:style/Widget.Material3.TextInputLayout.OutlinedBox = 0x7f13040b
com.example.uhf:id/decode = 0x7f0900f8
com.example.uhf:color/material_dynamic_neutral_variant50 = 0x7f060251
com.example.uhf:style/Widget.AppCompat.Spinner.DropDown.ActionBar = 0x7f130355
com.example.uhf:attr/thumbColor = 0x7f0404b9
com.example.uhf:attr/materialCardViewOutlinedStyle = 0x7f040311
com.example.uhf:dimen/hint_alpha_material_dark = 0x7f070099
com.example.uhf:dimen/mtrl_bottomappbar_fabOffsetEndMode = 0x7f070258
com.example.uhf:attr/materialCalendarTheme = 0x7f04030d
com.example.uhf:attr/colorContainer = 0x7f040100
com.example.uhf:attr/materialCalendarMonthNavigationButton = 0x7f04030b
com.example.uhf:attr/materialCalendarHeaderToggleButton = 0x7f040309
com.example.uhf:attr/materialCalendarHeaderTitle = 0x7f040308
com.example.uhf:drawable/ic_m3_chip_check = 0x7f0800a8
com.example.uhf:attr/materialCalendarHeaderSelection = 0x7f040307
com.example.uhf:style/Widget.MaterialComponents.MaterialCalendar.DayOfWeekLabel = 0x7f13044e
com.example.uhf:attr/shapeAppearanceCornerExtraLarge = 0x7f0403ee
com.example.uhf:macro/m3_comp_switch_selected_hover_icon_color = 0x7f0d0126
com.example.uhf:macro/m3_comp_outlined_text_field_label_text_color = 0x7f0d00c2
com.example.uhf:id/packed = 0x7f0901f9
com.example.uhf:id/dragDown = 0x7f09010e
com.example.uhf:attr/materialAlertDialogTitleTextStyle = 0x7f0402fc
com.example.uhf:attr/extendStrategy = 0x7f0401df
com.example.uhf:style/MyActionBarTabTextStyle = 0x7f13013f
com.example.uhf:dimen/mtrl_calendar_dialog_background_inset = 0x7f070282
com.example.uhf:attr/textAppearanceOverline = 0x7f040492
com.example.uhf:macro/m3_comp_time_picker_time_selector_unselected_focus_state_layer_color = 0x7f0d0168
com.example.uhf:string/msg_share_explanation = 0x7f12027a
com.example.uhf:string/close = 0x7f1200f9
com.example.uhf:attr/listLayout = 0x7f0402e5
com.example.uhf:attr/logoScaleType = 0x7f0402f2
com.example.uhf:dimen/material_filled_edittext_font_2_0_padding_bottom = 0x7f07023e
com.example.uhf:array/arrayDesFirePwdNum = 0x7f03000e
com.example.uhf:dimen/m3_comp_search_view_docked_header_container_height = 0x7f070179
com.example.uhf:attr/materialButtonToggleGroupStyle = 0x7f0402ff
com.example.uhf:drawable/notification_bg_normal = 0x7f0800f4
com.example.uhf:attr/indicatorSize = 0x7f04025d
com.example.uhf:anim/m3_bottom_sheet_slide_in = 0x7f010022
com.example.uhf:attr/region_widthMoreThan = 0x7f0403d2
com.example.uhf:attr/logoDescription = 0x7f0402f1
com.example.uhf:string/rfid_msg_upgrade_succ = 0x7f120353
com.example.uhf:id/preferences_header = 0x7f090208
com.example.uhf:attr/logoAdjustViewBounds = 0x7f0402f0
com.example.uhf:array/action1 = 0x7f030000
com.example.uhf:attr/textAppearanceTitleSmall = 0x7f04049b
com.example.uhf:string/uhf_title_perm = 0x7f120487
com.example.uhf:string/btAdd_Select = 0x7f1200a7
com.example.uhf:color/material_personalized_color_secondary = 0x7f0602af
com.example.uhf:attr/tabContentStart = 0x7f040456
com.example.uhf:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f1302f2
com.example.uhf:attr/buttonPanelSideLayout = 0x7f04009c
com.example.uhf:string/b14443_title_auto = 0x7f12006b
com.example.uhf:attr/collapsedSize = 0x7f0400f3
com.example.uhf:id/south = 0x7f090268
com.example.uhf:array/defaultcls = 0x7f030031
com.example.uhf:attr/listPreferredItemPaddingRight = 0x7f0402ed
com.example.uhf:layout/m3_alert_dialog_actions = 0x7f0c0040
com.example.uhf:attr/listPreferredItemPaddingLeft = 0x7f0402ec
com.example.uhf:array/arrayUart = 0x7f030028
com.example.uhf:attr/state_collapsible = 0x7f04042f
com.example.uhf:string/yid_msg_scan_fail = 0x7f1204bd
com.example.uhf:attr/carousel_nextState = 0x7f0400af
com.example.uhf:attr/listPreferredItemPaddingEnd = 0x7f0402eb
com.example.uhf:style/Theme.AppCompat.DayNight.NoActionBar = 0x7f13023e
com.example.uhf:dimen/material_clock_display_width = 0x7f070229
com.example.uhf:color/material_personalized_color_outline_variant = 0x7f0602a9
com.example.uhf:dimen/m3_sys_motion_easing_standard_control_y2 = 0x7f07021a
com.example.uhf:attr/indeterminateAnimationType = 0x7f040257
com.example.uhf:attr/materialTimePickerStyle = 0x7f040323
com.example.uhf:macro/m3_comp_search_view_docked_container_shape = 0x7f0d00f3
com.example.uhf:attr/subtitleTextColor = 0x7f040444
com.example.uhf:attr/liftOnScrollColor = 0x7f0402da
com.example.uhf:dimen/mtrl_navigation_rail_text_size = 0x7f0702d8
com.example.uhf:dimen/mtrl_card_checked_icon_margin = 0x7f0702a0
com.example.uhf:dimen/design_bottom_navigation_elevation = 0x7f070064
com.example.uhf:attr/queryPatterns = 0x7f0403c4
com.example.uhf:attr/navigationMode = 0x7f040373
com.example.uhf:color/material_dynamic_primary30 = 0x7f06025c
com.example.uhf:attr/listPopupWindowStyle = 0x7f0402e7
com.example.uhf:style/ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f1302fb
com.example.uhf:dimen/mtrl_navigation_bar_item_default_icon_size = 0x7f0702c8
com.example.uhf:color/cardview_shadow_start_color = 0x7f060033
com.example.uhf:color/m3_ref_palette_neutral98 = 0x7f060134
com.example.uhf:style/Widget.Material3.Toolbar = 0x7f13040f
com.example.uhf:string/fingerprint_tab_identification = 0x7f1201a3
com.example.uhf:animator/mtrl_extended_fab_hide_motion_spec = 0x7f02001b
com.example.uhf:attr/limitBoundsTo = 0x7f0402dc
com.example.uhf:attr/errorIconDrawable = 0x7f0401c9
com.example.uhf:attr/shouldDisableView = 0x7f0403f9
com.example.uhf:drawable/abc_seekbar_tick_mark_material = 0x7f080064
com.example.uhf:string/b14443_msg_reset_err = 0x7f120066
com.example.uhf:id/action_Deactivate = 0x7f090056
com.example.uhf:attr/srcCompat = 0x7f040423
com.example.uhf:id/autoCompleteToEnd = 0x7f09007d
com.example.uhf:attr/customColorValue = 0x7f04016e
com.example.uhf:color/m3_sys_color_light_on_error_container = 0x7f0601f0
com.example.uhf:dimen/design_bottom_navigation_icon_size = 0x7f070066
com.example.uhf:color/m3_sys_color_dynamic_light_on_primary = 0x7f0601c3
com.example.uhf:style/Widget.AppCompat.Light.ActionButton = 0x7f130335
com.example.uhf:attr/lineSpacing = 0x7f0402de
com.example.uhf:dimen/compat_button_padding_horizontal_material = 0x7f07005a
com.example.uhf:integer/m3_sys_motion_duration_extra_long3 = 0x7f0a0011
com.example.uhf:attr/layout_marginBaseline = 0x7f0402d3
com.example.uhf:style/AlertDialog.AppCompat = 0x7f130002
com.example.uhf:string/tvAccess_Lock = 0x7f1203f6
com.example.uhf:attr/trackHeight = 0x7f0404f8
com.example.uhf:macro/m3_comp_filled_text_field_supporting_text_type = 0x7f0d0051
com.example.uhf:dimen/notification_subtext_size = 0x7f070326
com.example.uhf:attr/materialButtonStyle = 0x7f0402fe
com.example.uhf:attr/floatingActionButtonTertiaryStyle = 0x7f040205
com.example.uhf:string/fingerprint_dailog_no = 0x7f120184
com.example.uhf:style/Widget.AppCompat.Toolbar = 0x7f130359
com.example.uhf:attr/layout_editor_absoluteX = 0x7f0402c8
com.example.uhf:id/tag_window_insets_animation_callback = 0x7f09029a
com.example.uhf:drawable/close = 0x7f080092
com.example.uhf:style/Theme.Material3.Dark.BottomSheetDialog = 0x7f130253
com.example.uhf:attr/layout_constraintWidth_min = 0x7f0402c5
com.example.uhf:color/m3_sys_color_dynamic_dark_outline_variant = 0x7f0601aa
com.example.uhf:attr/enableEdgeToEdge = 0x7f0401b6
com.example.uhf:color/m3_sys_color_on_tertiary_fixed_variant = 0x7f06020f
com.example.uhf:layout/preference_widget_checkbox = 0x7f0c0084
com.example.uhf:attr/behavior_significantVelocityThreshold = 0x7f040078
com.example.uhf:array/desfire_array_hex = 0x7f030035
com.example.uhf:style/Theme.MaterialComponents.Dialog.MinWidth.Bridge = 0x7f13028b
com.example.uhf:layout/abc_action_bar_up_container = 0x7f0c0001
com.example.uhf:attr/layout_constraintVertical_chainStyle = 0x7f0402c0
com.example.uhf:drawable/rectangle_bg2 = 0x7f080103
com.example.uhf:attr/alertDialogStyle = 0x7f04002e
com.example.uhf:attr/trackTint = 0x7f0404fc
com.example.uhf:anim/linear_indeterminate_line2_head_interpolator = 0x7f010020
com.example.uhf:id/layout4 = 0x7f090181
com.example.uhf:style/Preference.SwitchPreference = 0x7f130162
com.example.uhf:id/search_close_btn = 0x7f09024d
com.example.uhf:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x2 = 0x7f070200
com.example.uhf:string/preferences_auto_focus_title = 0x7f1202ef
com.example.uhf:style/Widget.AppCompat.AutoCompleteTextView = 0x7f13031c
com.example.uhf:dimen/m3_comp_primary_navigation_tab_active_pressed_state_layer_opacity = 0x7f070162
com.example.uhf:id/accessibility_custom_action_4 = 0x7f09004d
com.example.uhf:attr/layout_constraintTag = 0x7f0402bb
com.example.uhf:drawable/paste = 0x7f0800fd
com.example.uhf:string/uhf_msg_tab_write = 0x7f120474
com.example.uhf:attr/colorSecondary = 0x7f040125
com.example.uhf:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f13030b
com.example.uhf:attr/fabCradleRoundedCornerRadius = 0x7f0401eb
com.example.uhf:styleable/AppCompatEmojiHelper = 0x7f14000f
com.example.uhf:attr/layout_constraintStart_toEndOf = 0x7f0402b9
com.example.uhf:attr/layout_constraintRight_toRightOf = 0x7f0402b8
com.example.uhf:id/rbEPC_filter_perm = 0x7f090215
com.example.uhf:attr/layout_constraintRight_toLeftOf = 0x7f0402b7
com.example.uhf:array/arrayPacketSize = 0x7f030020
com.example.uhf:attr/layout_constraintLeft_toLeftOf = 0x7f0402b4
com.example.uhf:attr/layout_constraintLeft_creator = 0x7f0402b3
com.example.uhf:attr/layout_constraintHorizontal_weight = 0x7f0402b2
com.example.uhf:dimen/design_snackbar_background_corner_radius = 0x7f070082
com.example.uhf:attr/layout_constraintHeight_percent = 0x7f0402af
com.example.uhf:string/rfid_mgs_error_nolockcode = 0x7f120336
com.example.uhf:array/c4000cls = 0x7f03002b
com.example.uhf:attr/layout_constraintHeight_min = 0x7f0402ae
com.example.uhf:attr/marginHorizontal = 0x7f0402f3
com.example.uhf:attr/listChoiceIndicatorMultipleAnimated = 0x7f0402e1
com.example.uhf:attr/layout_constraintHeight_max = 0x7f0402ad
com.example.uhf:string/title_activity_psam = 0x7f1203c1
com.example.uhf:color/button_material_light = 0x7f06002d
com.example.uhf:string/network_msg_wifi_conning = 0x7f1202c8
com.example.uhf:attr/ifTagSet = 0x7f04024f
com.example.uhf:attr/thumbTextPadding = 0x7f0404c3
com.example.uhf:dimen/m3_card_elevated_dragged_z = 0x7f0700ec
com.example.uhf:string/battery_title_rb_def = 0x7f120085
com.example.uhf:id/startVertical = 0x7f090284
com.example.uhf:string/setAgreement_fail = 0x7f12036b
com.example.uhf:macro/m3_comp_switch_unselected_pressed_icon_color = 0x7f0d013c
com.example.uhf:dimen/notification_right_side_padding_top = 0x7f070323
com.example.uhf:attr/floatingActionButtonSmallSecondaryStyle = 0x7f0401ff
com.example.uhf:dimen/design_snackbar_elevation = 0x7f070083
com.example.uhf:style/TextAppearance.M3.Sys.Typescale.TitleSmall = 0x7f13020c
com.example.uhf:attr/ratingBarStyleSmall = 0x7f0403c9
com.example.uhf:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x2 = 0x7f0701fc
com.example.uhf:attr/hintTextColor = 0x7f04023e
com.example.uhf:attr/maxNumber = 0x7f04032d
com.example.uhf:string/title_activity_test = 0x7f1203c4
com.example.uhf:color/green2 = 0x7f06006f
com.example.uhf:dimen/mtrl_tooltip_cornerSize = 0x7f07030c
com.example.uhf:attr/textAppearanceTitleMedium = 0x7f04049a
com.example.uhf:macro/m3_comp_radio_button_selected_pressed_icon_color = 0x7f0d00dd
com.example.uhf:attr/layout_constraintCircleAngle = 0x7f0402a3
com.example.uhf:id/accessibility_custom_action_28 = 0x7f090048
com.example.uhf:attr/layout_constraintBottom_toTopOf = 0x7f0402a1
com.example.uhf:macro/m3_comp_radio_button_selected_icon_color = 0x7f0d00dc
com.example.uhf:color/m3_ref_palette_neutral92 = 0x7f060130
com.example.uhf:color/m3_efab_ripple_color_selector = 0x7f0600a2
com.example.uhf:attr/passwordToggleDrawable = 0x7f040395
com.example.uhf:attr/materialSearchViewPrefixStyle = 0x7f04031d
com.example.uhf:string/freHopType = 0x7f1201bb
com.example.uhf:attr/lStar = 0x7f040289
com.example.uhf:attr/tabTextColor = 0x7f040470
com.example.uhf:attr/layout_constraintBottom_creator = 0x7f04029f
com.example.uhf:id/transition_pause_alpha = 0x7f0902b9
com.example.uhf:dimen/m3_chip_elevated_elevation = 0x7f0700fc
com.example.uhf:style/ShapeAppearance.M3.Sys.Shape.Corner.Small = 0x7f130198
com.example.uhf:attr/layout_collapseMode = 0x7f040297
com.example.uhf:attr/region_heightLessThan = 0x7f0403cf
com.example.uhf:string/desfire_title_app_properties = 0x7f12011f
com.example.uhf:attr/layoutDescription = 0x7f040291
com.example.uhf:style/Base.V21.Theme.AppCompat = 0x7f1300a6
com.example.uhf:style/Widget.Material3.SideSheet.Detached = 0x7f1303f6
com.example.uhf:color/material_dynamic_neutral_variant30 = 0x7f06024f
com.example.uhf:attr/layout = 0x7f040290
com.example.uhf:id/startHorizontal = 0x7f090282
com.example.uhf:color/m3_ref_palette_tertiary10 = 0x7f06015e
com.example.uhf:attr/layout_constraintTop_creator = 0x7f0402bc
com.example.uhf:attr/keylines = 0x7f040288
com.example.uhf:attr/actionProviderClass = 0x7f040023
com.example.uhf:attr/itemVerticalPadding = 0x7f040284
com.example.uhf:attr/layoutDuringTransition = 0x7f040292
com.example.uhf:styleable/ButtonBarLayout = 0x7f14001b
com.example.uhf:color/m3_timepicker_button_ripple_color = 0x7f060225
com.example.uhf:string/tvStartFreI = 0x7f120422
com.example.uhf:id/SHIFT = 0x7f09001c
com.example.uhf:attr/chipCornerRadius = 0x7f0400c8
com.example.uhf:dimen/m3_bottomappbar_height = 0x7f0700ce
com.example.uhf:attr/itemTextColor = 0x7f040283
com.example.uhf:attr/buttonTintMode = 0x7f0400a0
com.example.uhf:style/Base.Widget.AppCompat.Light.ActionBar.TabView = 0x7f1300e7
com.example.uhf:attr/enforceTextAppearance = 0x7f0401c1
com.example.uhf:dimen/m3_comp_suggestion_chip_flat_container_elevation = 0x7f070191
com.example.uhf:attr/expandedTitleMarginEnd = 0x7f0401d9
com.example.uhf:attr/transitionFlags = 0x7f040501
com.example.uhf:attr/layout_goneMarginBottom = 0x7f0402cb
com.example.uhf:string/gps_msg_Locate_succ = 0x7f1201c3
com.example.uhf:attr/flow_verticalAlign = 0x7f040214
com.example.uhf:color/material_personalized_color_primary_text_inverse = 0x7f0602ae
com.example.uhf:attr/chipSurfaceColor = 0x7f0400da
com.example.uhf:attr/itemShapeInsetEnd = 0x7f040279
com.example.uhf:attr/itemShapeInsetBottom = 0x7f040278
com.example.uhf:id/message = 0x7f0901b1
com.example.uhf:color/material_dynamic_primary99 = 0x7f060264
com.example.uhf:attr/itemShapeFillColor = 0x7f040277
com.example.uhf:string/kettest_msg_broadcast_tip = 0x7f1201e0
com.example.uhf:string/abc_searchview_description_voice = 0x7f120038
com.example.uhf:dimen/m3_card_hovered_z = 0x7f0700f0
com.example.uhf:attr/colorTertiaryFixedDim = 0x7f040138
com.example.uhf:attr/gapBetweenBars = 0x7f04022a
com.example.uhf:attr/boxCornerRadiusTopStart = 0x7f04008b
com.example.uhf:style/Widget.MaterialComponents.PopupMenu.Overflow = 0x7f13046b
com.example.uhf:style/ShapeAppearance.M3.Comp.SearchBar.Avatar.Shape = 0x7f13018a
com.example.uhf:attr/contentInsetStart = 0x7f040148
com.example.uhf:macro/m3_comp_navigation_bar_inactive_hover_icon_color = 0x7f0d006f
com.example.uhf:color/m3_sys_color_dynamic_dark_primary_container = 0x7f0601ac
com.example.uhf:dimen/m3_comp_scrim_container_opacity = 0x7f070172
com.example.uhf:string/desfire_title_app_max_key = 0x7f12011d
com.example.uhf:layout/ime_secondary_split_test_activity = 0x7f0c003c
com.example.uhf:attr/motionEasingDecelerated = 0x7f040353
com.example.uhf:attr/colorOnSecondary = 0x7f040110
com.example.uhf:dimen/m3_comp_search_bar_container_height = 0x7f070175
com.example.uhf:color/m3_sys_color_light_on_surface = 0x7f0601f5
com.example.uhf:attr/floatingActionButtonLargePrimaryStyle = 0x7f0401f7
com.example.uhf:attr/indicatorTrackGapSize = 0x7f04025e
com.example.uhf:color/m3_calendar_item_disabled_text = 0x7f060085
com.example.uhf:attr/errorTextColor = 0x7f0401ce
com.example.uhf:color/m3_sys_color_light_tertiary = 0x7f060208
com.example.uhf:attr/colorOutline = 0x7f04011b
com.example.uhf:string/msg_no_data = 0x7f120268
com.example.uhf:color/material_dynamic_primary95 = 0x7f060263
com.example.uhf:color/m3_ref_palette_secondary90 = 0x7f06015a
com.example.uhf:id/tag_state_description = 0x7f090296
com.example.uhf:color/m3_sys_color_dark_surface_dim = 0x7f060194
com.example.uhf:string/mtrl_picker_invalid_format_use = 0x7f12029d
com.example.uhf:attr/materialAlertDialogTheme = 0x7f0402f9
com.example.uhf:string/OAUTH_ERROR = 0x7f120013
com.example.uhf:array/arrayBase = 0x7f030008
com.example.uhf:attr/fontProviderCerts = 0x7f04021c
com.example.uhf:color/m3_ref_palette_dynamic_neutral100 = 0x7f0600bc
com.example.uhf:attr/materialCalendarYearNavigationButton = 0x7f04030e
com.example.uhf:bool/workmanager_test_configuration = 0x7f050007
com.example.uhf:array/config = 0x7f03002f
com.example.uhf:attr/motionProgress = 0x7f040369
com.example.uhf:attr/indicatorColor = 0x7f040259
com.example.uhf:style/PreferenceThemeOverlay.v14 = 0x7f13016d
com.example.uhf:array/i760cls = 0x7f03003b
com.example.uhf:attr/buttonStyleSmall = 0x7f04009e
com.example.uhf:animator/mtrl_btn_unelevated_state_list_anim = 0x7f020016
com.example.uhf:dimen/m3_comp_text_button_focus_state_layer_opacity = 0x7f0701a1
com.example.uhf:attr/dynamicColorThemeOverlay = 0x7f0401ab
com.example.uhf:drawable/ic_mtrl_chip_checked_black = 0x7f0800ac
com.example.uhf:color/abc_secondary_text_material_dark = 0x7f060011
com.example.uhf:string/mtrl_picker_navigate_to_current_year_description = 0x7f12029f
com.example.uhf:id/normal = 0x7f0901e0
com.example.uhf:attr/tintMode = 0x7f0404d2
com.example.uhf:attr/buttonBarNeutralButtonStyle = 0x7f040093
com.example.uhf:attr/imagePanX = 0x7f040251
com.example.uhf:macro/m3_comp_navigation_bar_active_focus_state_layer_color = 0x7f0d0061
com.example.uhf:attr/defaultQueryHint = 0x7f04017c
com.example.uhf:id/container = 0x7f0900e8
com.example.uhf:attr/drawerArrowStyle = 0x7f0401a3
com.example.uhf:id/fill_horizontal = 0x7f09014c
com.example.uhf:style/Base.Widget.Design.TabLayout = 0x7f130101
com.example.uhf:attr/customNavigationLayout = 0x7f040172
com.example.uhf:attr/iconSpaceReserved = 0x7f040249
com.example.uhf:bool/config_materialPreferenceIconSpaceReserved = 0x7f050002
com.example.uhf:attr/textBackground = 0x7f04049c
com.example.uhf:styleable/Chip = 0x7f140021
com.example.uhf:color/m3_navigation_item_background_color = 0x7f0600ae
com.example.uhf:attr/floatingActionButtonLargeStyle = 0x7f0401f9
com.example.uhf:styleable/SearchView = 0x7f140088
com.example.uhf:attr/chipIconEnabled = 0x7f0400cc
com.example.uhf:animator/m3_extended_fab_show_motion_spec = 0x7f020013
com.example.uhf:attr/windowFixedWidthMinor = 0x7f040526
com.example.uhf:attr/hoveredFocusedTranslationZ = 0x7f040243
com.example.uhf:id/rbUser = 0x7f090224
com.example.uhf:attr/badgeTextAppearance = 0x7f040060
com.example.uhf:attr/finishSecondaryWithPrimary = 0x7f0401f5
com.example.uhf:attr/chipStandaloneStyle = 0x7f0400d5
com.example.uhf:attr/layout_goneMarginTop = 0x7f0402d0
com.example.uhf:attr/checkedIconGravity = 0x7f0400c0
com.example.uhf:drawable/$avd_hide_password__2 = 0x7f080002
com.example.uhf:id/topPanel = 0x7f0902b1
com.example.uhf:attr/constraintSetEnd = 0x7f04013d
com.example.uhf:dimen/m3_comp_secondary_navigation_tab_active_indicator_height = 0x7f07017b
com.example.uhf:attr/chipGroupStyle = 0x7f0400ca
com.example.uhf:dimen/design_fab_border_width = 0x7f070070
com.example.uhf:string/lf_title_write = 0x7f1201f1
com.example.uhf:styleable/AppBarLayoutStates = 0x7f14000d
com.example.uhf:attr/hintTextAppearance = 0x7f04023d
com.example.uhf:attr/prefixTextColor = 0x7f0403b9
com.example.uhf:color/m3_ref_palette_primary99 = 0x7f06014f
com.example.uhf:color/m3_sys_color_dynamic_dark_inverse_surface = 0x7f06019d
com.example.uhf:dimen/design_tab_text_size_2line = 0x7f07008e
com.example.uhf:dimen/mtrl_calendar_header_toggle_margin_top = 0x7f07028b
com.example.uhf:id/neverCompleteToEnd = 0x7f0901db
com.example.uhf:color/material_personalized_color_tertiary = 0x7f0602bd
com.example.uhf:attr/counterTextAppearance = 0x7f040165
com.example.uhf:attr/carousel_forwardTransition = 0x7f0400ad
com.example.uhf:style/Base.V7.Widget.AppCompat.EditText = 0x7f1300c5
com.example.uhf:color/m3_assist_chip_stroke_color = 0x7f06007e
com.example.uhf:string/uhf_msg_filter_data_not_null = 0x7f120448
com.example.uhf:integer/m3_sys_motion_duration_medium2 = 0x7f0a0018
com.example.uhf:attr/fragment = 0x7f040228
com.example.uhf:style/TextAppearance.MaterialComponents.Headline3 = 0x7f13022a
com.example.uhf:drawable/avd_show_password = 0x7f08007b
com.example.uhf:attr/mock_labelBackgroundColor = 0x7f04033d
com.example.uhf:layout/notification_action_tombstone = 0x7f0c0073
com.example.uhf:attr/preferenceFragmentCompatStyle = 0x7f0403b0
com.example.uhf:attr/arrowHeadLength = 0x7f040040
com.example.uhf:attr/allowStacking = 0x7f040033
com.example.uhf:color/mtrl_btn_text_color_disabled = 0x7f0602d8
com.example.uhf:array/arrayInventoried = 0x7f030013
com.example.uhf:styleable/Tooltip = 0x7f1400a4
com.example.uhf:attr/fontWeight = 0x7f040224
com.example.uhf:attr/expandedTitleMarginTop = 0x7f0401db
com.example.uhf:attr/dividerThickness = 0x7f040194
com.example.uhf:attr/upDuration = 0x7f040508
com.example.uhf:attr/touchAnchorId = 0x7f0404ed
com.example.uhf:attr/itemBackground = 0x7f040268
com.example.uhf:anim/fragment_fast_out_extra_slow_in = 0x7f01001c
com.example.uhf:attr/fontStyle = 0x7f040222
com.example.uhf:attr/checkedState = 0x7f0400c5
com.example.uhf:layout/m3_alert_dialog = 0x7f0c003f
com.example.uhf:string/bd_title_Latitude = 0x7f12008c
com.example.uhf:attr/textureHeight = 0x7f0404b5
com.example.uhf:dimen/mtrl_extended_fab_min_width = 0x7f0702b4
com.example.uhf:attr/fontFamily = 0x7f04021a
com.example.uhf:string/msg_file_format_err = 0x7f12024d
com.example.uhf:attr/flow_verticalStyle = 0x7f040217
com.example.uhf:string/mtrl_picker_date_header_unselected = 0x7f120298
com.example.uhf:attr/exampleColor = 0x7f0401cf
com.example.uhf:string/searchview_navigation_content_description = 0x7f120366
com.example.uhf:attr/iconifiedByDefault = 0x7f04024d
com.example.uhf:attr/layout_wrapBehaviorInParent = 0x7f0402d8
com.example.uhf:interpolator/mtrl_fast_out_linear_in = 0x7f0b000e
com.example.uhf:attr/hideNavigationIcon = 0x7f040238
com.example.uhf:attr/colorSurfaceDim = 0x7f040131
com.example.uhf:integer/app_bar_elevation_anim_duration = 0x7f0a0002
com.example.uhf:dimen/m3_comp_filter_chip_container_height = 0x7f070130
com.example.uhf:attr/imageRotate = 0x7f040253
com.example.uhf:attr/autoSizeTextType = 0x7f04004b
com.example.uhf:id/tvTime = 0x7f0902c8
com.example.uhf:style/TextAppearance.Material3.BodyMedium = 0x7f130210
com.example.uhf:color/mtrl_textinput_default_box_stroke_color = 0x7f060305
com.example.uhf:drawable/tooltip_frame_dark = 0x7f080108
com.example.uhf:attr/floatingActionButtonSurfaceStyle = 0x7f040204
com.example.uhf:dimen/m3_sys_motion_easing_legacy_control_y2 = 0x7f07020a
com.example.uhf:attr/tabIndicatorGravity = 0x7f04045f
com.example.uhf:attr/chipStartPadding = 0x7f0400d6
com.example.uhf:attr/listDividerAlertDialog = 0x7f0402e3
com.example.uhf:attr/errorEnabled = 0x7f0401c8
com.example.uhf:id/mtrl_child_content_container = 0x7f0901c5
com.example.uhf:string/submit_report = 0x7f120383
com.example.uhf:drawable/btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f08007d
com.example.uhf:attr/titleMarginStart = 0x7f0404db
com.example.uhf:layout/design_text_input_end_icon = 0x7f0c002c
com.example.uhf:style/Widget.Material3.Search.Toolbar.Button.Navigation = 0x7f1303ef
com.example.uhf:attr/borderRoundPercent = 0x7f04007c
com.example.uhf:attr/fastScrollHorizontalThumbDrawable = 0x7f0401f0
com.example.uhf:attr/flow_lastVerticalStyle = 0x7f040211
com.example.uhf:dimen/mtrl_calendar_title_baseline_to_top = 0x7f070299
com.example.uhf:anim/design_snackbar_in = 0x7f01001a
com.example.uhf:string/tvAccessPwd = 0x7f1203f5
com.example.uhf:style/ThemeOverlay.Material3.Light.Dialog.Alert.Framework = 0x7f1302d4
com.example.uhf:attr/telltales_tailScale = 0x7f040474
com.example.uhf:attr/cornerSize = 0x7f04015c
com.example.uhf:attr/drawableTintMode = 0x7f0401a1
com.example.uhf:attr/materialAlertDialogButtonSpacerVisibility = 0x7f0402f8
com.example.uhf:attr/adjustable = 0x7f04002b
com.example.uhf:string/desfire_title_dialog_tips = 0x7f120127
com.example.uhf:macro/m3_comp_sheet_side_docked_modal_container_color = 0x7f0d0108
com.example.uhf:attr/behavior_autoShrink = 0x7f04006f
com.example.uhf:attr/listChoiceBackgroundIndicator = 0x7f0402e0
com.example.uhf:attr/thumbStrokeWidth = 0x7f0404c2
com.example.uhf:style/Base.Widget.AppCompat.Light.ActionBar.TabBar = 0x7f1300e4
com.example.uhf:color/material_dynamic_tertiary100 = 0x7f060274
com.example.uhf:dimen/m3_comp_bottom_app_bar_container_height = 0x7f070107
com.example.uhf:anim/btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016
com.example.uhf:style/Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f130332
com.example.uhf:string/tvInventoryName = 0x7f12040b
com.example.uhf:attr/framePosition = 0x7f040229
com.example.uhf:id/enterAlways = 0x7f090120
com.example.uhf:style/Widget.Material3.BottomSheet = 0x7f130377
com.example.uhf:attr/moveWhenScrollAtTop = 0x7f04036e
com.example.uhf:id/direct = 0x7f090106
com.example.uhf:attr/contentInsetStartWithNavigation = 0x7f040149
com.example.uhf:attr/allowDividerAfterLastItem = 0x7f040031
com.example.uhf:attr/exampleString = 0x7f0401d2
com.example.uhf:color/material_personalized_color_primary = 0x7f0602aa
com.example.uhf:style/Theme.AppCompat.DayNight = 0x7f130238
com.example.uhf:string/ul_title_uid = 0x7f120494
com.example.uhf:attr/errorTextAppearance = 0x7f0401cd
com.example.uhf:style/TextAppearance.Material3.HeadlineLarge = 0x7f130215
com.example.uhf:attr/errorShown = 0x7f0401cc
com.example.uhf:string/history_empty = 0x7f1201d6
com.example.uhf:id/btSet = 0x7f09008d
com.example.uhf:attr/touchAnchorSide = 0x7f0404ee
com.example.uhf:attr/layout_constraintHeight = 0x7f0402ab
com.example.uhf:layout/fragment_main = 0x7f0c0033
com.example.uhf:attr/layout_constraintBaseline_toBaselineOf = 0x7f04029c
com.example.uhf:attr/errorIconTintMode = 0x7f0401cb
com.example.uhf:attr/layout_goneMarginRight = 0x7f0402ce
com.example.uhf:attr/lastItemDecorated = 0x7f04028f
com.example.uhf:string/download_msg_cancel_down = 0x7f120147
com.example.uhf:attr/circleRadius = 0x7f0400db
com.example.uhf:color/mtrl_chip_background_color = 0x7f0602df
com.example.uhf:style/Widget.MaterialComponents.TimePicker.ImageButton = 0x7f13048a
com.example.uhf:string/error_icon_content_description = 0x7f120166
com.example.uhf:dimen/text_size_19 = 0x7f07033c
com.example.uhf:attr/actionBarPopupTheme = 0x7f040004
com.example.uhf:attr/entryValues = 0x7f0401c4
com.example.uhf:string/rfid_mgs_lockpwdtip = 0x7f120341
com.example.uhf:attr/entries = 0x7f0401c3
com.example.uhf:style/ThemeOverlay.Material3.MaterialCalendar.Fullscreen = 0x7f1302d8
com.example.uhf:string/title_succ_count = 0x7f1203f1
com.example.uhf:style/Widget.Material3.MaterialTimePicker = 0x7f1303dd
com.example.uhf:attr/endIconScaleType = 0x7f0401bd
com.example.uhf:color/material_timepicker_modebutton_tint = 0x7f0602d2
com.example.uhf:string/msg_reboot_set = 0x7f12026e
com.example.uhf:string/United_States_Standard = 0x7f12001b
com.example.uhf:macro/m3_comp_outlined_card_container_shape = 0x7f0d00a9
com.example.uhf:macro/m3_comp_date_picker_modal_year_selection_year_unselected_label_text_color = 0x7f0d0021
com.example.uhf:attr/endIconDrawable = 0x7f0401ba
com.example.uhf:attr/endIconContentDescription = 0x7f0401b9
com.example.uhf:string/desfire_msg_pwd_not_hex = 0x7f120114
com.example.uhf:attr/singleLineTitle = 0x7f04040e
com.example.uhf:attr/staggered = 0x7f040425
com.example.uhf:attr/flow_verticalGap = 0x7f040216
com.example.uhf:attr/elevationOverlayAccentColor = 0x7f0401b1
com.example.uhf:attr/tabIndicatorAnimationDuration = 0x7f04045b
com.example.uhf:string/action_rfid_ver = 0x7f120044
com.example.uhf:attr/passwordToggleTint = 0x7f040397
com.example.uhf:dimen/notification_media_narrow_margin = 0x7f070321
com.example.uhf:string/title_activity_iso14443b = 0x7f1203b3
com.example.uhf:dimen/m3_bottom_nav_item_active_indicator_width = 0x7f0700c3
com.example.uhf:attr/layout_constraintWidth_max = 0x7f0402c4
com.example.uhf:string/network_msg_power_off = 0x7f1202c3
com.example.uhf:attr/itemShapeInsetStart = 0x7f04027a
com.example.uhf:attr/editTextPreferenceStyle = 0x7f0401ae
com.example.uhf:style/ShapeAppearance.MaterialComponents.Badge = 0x7f1301a6
com.example.uhf:attr/materialCardViewElevatedStyle = 0x7f04030f
com.example.uhf:color/m3_sys_color_light_error_container = 0x7f0601ea
com.example.uhf:layout/mtrl_picker_header_toggle = 0x7f0c006d
com.example.uhf:style/BasePreferenceThemeOverlay = 0x7f130124
com.example.uhf:attr/floatingActionButtonStyle = 0x7f040203
com.example.uhf:integer/m3_sys_motion_duration_long1 = 0x7f0a0013
com.example.uhf:id/cbPhase = 0x7f0900c9
com.example.uhf:attr/motionEasingStandardInterpolator = 0x7f04035d
com.example.uhf:dimen/mtrl_progress_circular_track_thickness_extra_small = 0x7f0702e2
com.example.uhf:dimen/material_emphasis_high_type = 0x7f07023a
com.example.uhf:style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f1300d5
com.example.uhf:attr/editTextBackground = 0x7f0401ac
com.example.uhf:styleable/ShapeAppearance = 0x7f14008a
com.example.uhf:dimen/abc_list_item_height_material = 0x7f070031
com.example.uhf:macro/m3_comp_search_view_divider_color = 0x7f0d00f2
com.example.uhf:style/Widget.MaterialComponents.ShapeableImageView = 0x7f13046d
com.example.uhf:color/m3_sys_color_dark_surface = 0x7f06018d
com.example.uhf:attr/duration = 0x7f0401aa
com.example.uhf:color/m3_ref_palette_neutral_variant99 = 0x7f060142
com.example.uhf:string/ul_title_DSFID = 0x7f120490
com.example.uhf:id/clockwise = 0x7f0900e2
com.example.uhf:id/SpinnerBank_Write = 0x7f090024
com.example.uhf:anim/m3_side_sheet_exit_to_left = 0x7f010028
com.example.uhf:dimen/mtrl_btn_padding_top = 0x7f07026d
com.example.uhf:animator/mtrl_chip_state_list_anim = 0x7f020018
com.example.uhf:style/Base.TextAppearance.AppCompat.Subhead.Inverse = 0x7f130032
com.example.uhf:string/battery_tips_HEALTH_DEAD = 0x7f120074
com.example.uhf:attr/popupMenuBackground = 0x7f0403a8
com.example.uhf:style/Widget.Material3.Chip.Filter.Elevated = 0x7f130394
com.example.uhf:style/Base.Widget.AppCompat.ActionButton.CloseMode = 0x7f1300cd
com.example.uhf:attr/dropdownPreferenceStyle = 0x7f0401a9
com.example.uhf:dimen/mtrl_extended_fab_end_padding_icon = 0x7f0702b0
com.example.uhf:id/onInterceptTouchReturnSwipe = 0x7f0901e7
com.example.uhf:attr/drawerLayoutCornerSize = 0x7f0401a4
com.example.uhf:attr/contentDescription = 0x7f040143
com.example.uhf:integer/m3_sys_motion_duration_short2 = 0x7f0a001c
com.example.uhf:attr/haloRadius = 0x7f04022f
com.example.uhf:id/btnBrowser = 0x7f090090
com.example.uhf:anim/linear_indeterminate_line1_tail_interpolator = 0x7f01001f
com.example.uhf:attr/colorSurfaceInverse = 0x7f040132
com.example.uhf:color/m3_sys_color_light_primary_container = 0x7f0601fc
com.example.uhf:color/m3_ref_palette_secondary60 = 0x7f060157
com.example.uhf:attr/constraint_referenced_tags = 0x7f040140
com.example.uhf:attr/colorPrimarySurface = 0x7f040123
com.example.uhf:style/Base.Theme.Uhfuartdemo = 0x7f13007c
com.example.uhf:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y2 = 0x7f070202
com.example.uhf:string/uhf_msg_write_must_len4x = 0x7f12047a
com.example.uhf:string/history_clear_one_history_text = 0x7f1201d3
com.example.uhf:id/submit_area = 0x7f09028a
com.example.uhf:color/m3_sys_color_light_secondary = 0x7f0601fd
com.example.uhf:string/uhf_msg_set_pwm_succ = 0x7f12046c
com.example.uhf:style/ThemeOverlay.AppCompat.Dialog = 0x7f1302a6
com.example.uhf:attr/textAppearanceDisplaySmall = 0x7f040480
com.example.uhf:attr/drawableSize = 0x7f04019e
com.example.uhf:attr/motion_triggerOnCollision = 0x7f04036d
com.example.uhf:color/m3_sys_color_dynamic_light_inverse_on_surface = 0x7f0601bd
com.example.uhf:id/unlabeled = 0x7f0902ce
com.example.uhf:attr/dividerVertical = 0x7f040195
com.example.uhf:attr/checkedChip = 0x7f0400bd
com.example.uhf:attr/shapeAppearanceOverlay = 0x7f0403f5
com.example.uhf:string/fingerprint_msg_import_succ = 0x7f120190
com.example.uhf:bool/mtrl_btn_textappearance_all_caps = 0x7f050006
com.example.uhf:style/Base.Theme.Material3.Light.BottomSheetDialog = 0x7f130064
com.example.uhf:attr/imageZoom = 0x7f040254
com.example.uhf:attr/state_dragged = 0x7f040430
com.example.uhf:id/cbFilter = 0x7f0900c6
com.example.uhf:id/BtInventory = 0x7f090004
com.example.uhf:attr/borderlessButtonStyle = 0x7f04007e
com.example.uhf:string/title_analog_call_touch_on = 0x7f1203cd
com.example.uhf:id/action_container = 0x7f09005e
com.example.uhf:attr/suggestionRowLayout = 0x7f040449
com.example.uhf:style/ShapeAppearance.M3.Comp.DatePicker.Modal.Date.Container.Shape = 0x7f130183
com.example.uhf:raw/serror = 0x7f110001
com.example.uhf:anim/abc_slide_in_top = 0x7f010007
com.example.uhf:attr/displayOptions = 0x7f04018d
com.example.uhf:attr/helperTextEnabled = 0x7f040233
com.example.uhf:macro/m3_comp_outlined_text_field_focus_outline_color = 0x7f0d00bb
com.example.uhf:color/material_on_primary_disabled = 0x7f06028d
com.example.uhf:string/rbRead = 0x7f120321
com.example.uhf:attr/dialogLayout = 0x7f040186
com.example.uhf:dimen/mtrl_high_ripple_pressed_alpha = 0x7f0702c2
com.example.uhf:interpolator/btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0b0005
com.example.uhf:attr/colorOnTertiaryContainer = 0x7f040118
com.example.uhf:attr/bottomInsetScrimEnabled = 0x7f040080
com.example.uhf:attr/deltaPolarAngle = 0x7f040180
com.example.uhf:id/seekbar_value = 0x7f090256
com.example.uhf:attr/drawableTint = 0x7f0401a0
com.example.uhf:string/uhf_title_access = 0x7f120480
com.example.uhf:attr/textAppearanceSubtitle1 = 0x7f040497
com.example.uhf:attr/counterMaxLength = 0x7f040162
com.example.uhf:dimen/abc_action_bar_elevation_material = 0x7f070005
com.example.uhf:attr/actionButtonStyle = 0x7f04000d
com.example.uhf:color/material_grey_100 = 0x7f06027f
com.example.uhf:style/TextAppearance.AppCompat.Small.Inverse = 0x7f1301d5
com.example.uhf:macro/m3_comp_search_bar_input_text_type = 0x7f0d00ea
com.example.uhf:style/Base.TextAppearance.AppCompat.Display1 = 0x7f13001f
com.example.uhf:animator/mtrl_extended_fab_change_size_expand_motion_spec = 0x7f02001a
com.example.uhf:color/m3_ref_palette_neutral_variant60 = 0x7f06013d
com.example.uhf:attr/spanCount = 0x7f040415
com.example.uhf:attr/deriveConstraintsFrom = 0x7f040183
com.example.uhf:string/desfire_title_oper_pwd = 0x7f120132
com.example.uhf:color/orange = 0x7f06030c
com.example.uhf:attr/itemActiveIndicatorStyle = 0x7f040267
com.example.uhf:id/percent = 0x7f090203
com.example.uhf:attr/badgeStyle = 0x7f04005e
com.example.uhf:attr/daySelectedStyle = 0x7f040177
com.example.uhf:attr/colorOnTertiaryFixedVariant = 0x7f04011a
com.example.uhf:attr/expanded = 0x7f0401d4
com.example.uhf:attr/customFloatValue = 0x7f040170
com.example.uhf:attr/customColorDrawableValue = 0x7f04016d
com.example.uhf:attr/customBoolean = 0x7f04016c
com.example.uhf:attr/showDividers = 0x7f0403fe
com.example.uhf:color/accent_material_dark = 0x7f060019
com.example.uhf:attr/tabIndicatorColor = 0x7f04045d
com.example.uhf:attr/windowFixedHeightMinor = 0x7f040524
com.example.uhf:string/tvLockCode = 0x7f120412
com.example.uhf:dimen/mtrl_card_dragged_z = 0x7f0702a3
com.example.uhf:color/m3_ref_palette_secondary100 = 0x7f060152
com.example.uhf:macro/m3_comp_outlined_text_field_input_text_type = 0x7f0d00c1
com.example.uhf:dimen/m3_comp_secondary_navigation_tab_hover_state_layer_opacity = 0x7f07017d
com.example.uhf:drawable/$mtrl_switch_thumb_checked_unchecked__0 = 0x7f080022
com.example.uhf:dimen/m3_navigation_drawer_layout_corner_size = 0x7f0701c3
com.example.uhf:dimen/abc_text_size_display_4_material = 0x7f070046
com.example.uhf:string/default_font_gray_scale = 0x7f120103
com.example.uhf:attr/thumbRadius = 0x7f0404c0
com.example.uhf:style/Widget.Material3.Snackbar = 0x7f1303fd
com.example.uhf:attr/secondaryActivityName = 0x7f0403e3
com.example.uhf:string/material_timepicker_minute = 0x7f120232
com.example.uhf:attr/backgroundOverlayColorAlpha = 0x7f040054
com.example.uhf:color/mtrl_tabs_icon_color_selector_colored = 0x7f060301
com.example.uhf:attr/materialTimePickerTitleStyle = 0x7f040325
com.example.uhf:attr/alphabeticModifiers = 0x7f040035
com.example.uhf:attr/counterOverflowTextAppearance = 0x7f040163
com.example.uhf:style/Widget.Material3.ExtendedFloatingActionButton.Surface = 0x7f1303b2
com.example.uhf:id/motion_base = 0x7f0901ba
com.example.uhf:attr/cornerSizeTopRight = 0x7f040160
com.example.uhf:id/textinput_error = 0x7f0902a6
com.example.uhf:dimen/m3_comp_switch_track_width = 0x7f07019d
com.example.uhf:attr/actionModeSelectAllDrawable = 0x7f04001b
com.example.uhf:attr/elevation = 0x7f0401b0
com.example.uhf:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow = 0x7f130175
com.example.uhf:string/uhf_msg_upgrade_succ = 0x7f120477
com.example.uhf:color/m3_sys_color_dynamic_tertiary_fixed = 0x7f0601e6
com.example.uhf:style/Base.ThemeOverlay.AppCompat.Light = 0x7f130083
com.example.uhf:macro/m3_comp_time_picker_time_selector_separator_type = 0x7f0d0166
com.example.uhf:id/dragAnticlockwise = 0x7f09010c
com.example.uhf:drawable/$mtrl_switch_thumb_pressed_checked__0 = 0x7f080024
com.example.uhf:attr/checkMarkCompat = 0x7f0400b8
com.example.uhf:color/m3_sys_color_dynamic_dark_secondary = 0x7f0601ad
com.example.uhf:style/Platform.V25.AppCompat.Light = 0x7f13014e
com.example.uhf:attr/firstBaselineToTopHeight = 0x7f0401f6
com.example.uhf:dimen/tooltip_y_offset_non_touch = 0x7f070353
com.example.uhf:style/Base.Widget.AppCompat.Button = 0x7f1300d2
com.example.uhf:id/center_vertical = 0x7f0900d7
com.example.uhf:id/tv_count = 0x7f0902ca
com.example.uhf:attr/colorError = 0x7f040104
com.example.uhf:style/Platform.V21.AppCompat = 0x7f13014b
com.example.uhf:attr/motionDurationShort1 = 0x7f04034e
com.example.uhf:attr/dialogMessage = 0x7f040187
com.example.uhf:attr/tickColorInactive = 0x7f0404ca
com.example.uhf:style/Base.ThemeOverlay.AppCompat.Dialog = 0x7f130081
com.example.uhf:style/Widget.MaterialComponents.ActionBar.Solid = 0x7f130415
com.example.uhf:attr/cornerFamilyTopLeft = 0x7f040159
com.example.uhf:style/Base.ThemeOverlay.AppCompat = 0x7f13007d
com.example.uhf:style/Widget.AppCompat.Light.ActionBar = 0x7f13032c
com.example.uhf:attr/cornerFamilyBottomRight = 0x7f040158
com.example.uhf:color/material_dynamic_tertiary40 = 0x7f060277
com.example.uhf:attr/cornerFamilyBottomLeft = 0x7f040157
com.example.uhf:string/mtrl_exceed_max_badge_number_content_description = 0x7f12028d
com.example.uhf:macro/m3_comp_time_picker_period_selector_outline_color = 0x7f0d0154
com.example.uhf:color/m3_sys_color_dynamic_light_secondary = 0x7f0601cf
com.example.uhf:attr/rangeFillColor = 0x7f0403c6
com.example.uhf:attr/contentPaddingTop = 0x7f040150
com.example.uhf:color/m3_sys_color_dynamic_light_on_secondary_container = 0x7f0601c6
com.example.uhf:attr/paddingTopNoTitle = 0x7f04038f
com.example.uhf:attr/contentPaddingRight = 0x7f04014e
com.example.uhf:dimen/m3_comp_progress_indicator_active_indicator_track_space = 0x7f070167
com.example.uhf:attr/contentPadding = 0x7f04014a
com.example.uhf:macro/m3_comp_time_picker_period_selector_unselected_hover_state_layer_color = 0x7f0d015b
com.example.uhf:attr/contentInsetLeft = 0x7f040146
com.example.uhf:style/Widget.MaterialComponents.CircularProgressIndicator.Small = 0x7f13043e
com.example.uhf:style/Theme.MaterialComponents.DayNight.DarkActionBar.Bridge = 0x7f130278
com.example.uhf:string/desfire_title_add_file = 0x7f120118
com.example.uhf:anim/abc_fade_out = 0x7f010001
com.example.uhf:style/Preference.Information.Material = 0x7f13015c
com.example.uhf:attr/floatingActionButtonLargeSurfaceStyle = 0x7f0401fa
com.example.uhf:id/noState = 0x7f0901de
com.example.uhf:attr/contentInsetEndWithActions = 0x7f040145
com.example.uhf:styleable/NavigationBarActiveIndicator = 0x7f140071
com.example.uhf:string/btSetFre = 0x7f1200bb
com.example.uhf:attr/itemTextAppearanceInactive = 0x7f040282
com.example.uhf:style/Base.Widget.Material3.CompoundButton.CheckBox = 0x7f130108
com.example.uhf:attr/constraints = 0x7f040141
com.example.uhf:style/RtlOverlay.DialogWindowTitle.AppCompat = 0x7f13016f
com.example.uhf:dimen/mtrl_calendar_day_horizontal_padding = 0x7f07027d
com.example.uhf:style/Base.V22.Theme.AppCompat.Light = 0x7f1300b3
com.example.uhf:id/easeOut = 0x7f090117
com.example.uhf:attr/constraint_referenced_ids = 0x7f04013f
com.example.uhf:attr/actionModeCloseContentDescription = 0x7f040014
com.example.uhf:attr/constraintSetStart = 0x7f04013e
com.example.uhf:attr/constraintSet = 0x7f04013c
com.example.uhf:style/Widget.MaterialComponents.TimePicker.Clock = 0x7f130484
com.example.uhf:string/rfid_mgs_error_config = 0x7f120332
com.example.uhf:dimen/text_size_9 = 0x7f07034c
com.example.uhf:drawable/image = 0x7f0800b0
com.example.uhf:color/cardview_dark_background = 0x7f060030
com.example.uhf:id/end = 0x7f09011e
com.example.uhf:style/TextAppearance.AppCompat.Caption = 0x7f1301c2
com.example.uhf:attr/colorSwitchThumbNormal = 0x7f040134
com.example.uhf:style/ShapeAppearance.M3.Comp.SearchBar.Container.Shape = 0x7f13018b
com.example.uhf:attr/colorSurfaceContainerLowest = 0x7f040130
com.example.uhf:attr/actionModePopupWindowStyle = 0x7f04001a
com.example.uhf:dimen/m3_comp_navigation_drawer_icon_size = 0x7f070146
com.example.uhf:attr/maxImageSize = 0x7f04032b
com.example.uhf:string/share_name = 0x7f120372
com.example.uhf:dimen/m3_sys_elevation_level5 = 0x7f0701fa
com.example.uhf:color/mtrl_popupmenu_overlay_color = 0x7f0602f9
com.example.uhf:attr/colorSurface = 0x7f04012a
com.example.uhf:styleable/Transition = 0x7f1400a6
com.example.uhf:id/search_src_text = 0x7f090252
com.example.uhf:attr/titleMargins = 0x7f0404dd
com.example.uhf:macro/m3_comp_navigation_rail_active_icon_color = 0x7f0d0095
com.example.uhf:string/title_activity_iso15693 = 0x7f1203b4
com.example.uhf:attr/colorSecondaryFixedDim = 0x7f040128
com.example.uhf:attr/endIconMode = 0x7f0401bc
com.example.uhf:attr/actionOverflowButtonStyle = 0x7f040021
com.example.uhf:string/title_activity_global_set = 0x7f1203af
com.example.uhf:string/not_set = 0x7f1202dd
com.example.uhf:attr/enabled = 0x7f0401b7
com.example.uhf:string/material_timepicker_am = 0x7f12022f
com.example.uhf:attr/trackInsideCornerSize = 0x7f0404f9
com.example.uhf:id/material_timepicker_mode_button = 0x7f0901ac
com.example.uhf:id/action_bar_spinner = 0x7f09005b
com.example.uhf:color/m3_sys_color_dynamic_dark_on_secondary = 0x7f0601a3
com.example.uhf:attr/elevationOverlayColor = 0x7f0401b2
com.example.uhf:attr/layout_constraintWidth_percent = 0x7f0402c6
com.example.uhf:attr/initialActivityCount = 0x7f04025f
com.example.uhf:attr/errorAccessibilityLiveRegion = 0x7f0401c6
com.example.uhf:id/clip_vertical = 0x7f0900e1
com.example.uhf:attr/autoSizeMaxTextSize = 0x7f040047
com.example.uhf:drawable/m3_password_eye = 0x7f0800b6
com.example.uhf:color/abc_primary_text_disable_only_material_light = 0x7f06000a
com.example.uhf:attr/showMotionSpec = 0x7f040400
com.example.uhf:id/mtrl_calendar_months = 0x7f0901c0
com.example.uhf:attr/tabStyle = 0x7f04046e
com.example.uhf:color/material_dynamic_secondary0 = 0x7f060265
com.example.uhf:attr/colorOnTertiary = 0x7f040117
com.example.uhf:styleable/CheckBoxPreference = 0x7f14001f
com.example.uhf:string/fingerprint_btn_save = 0x7f12017d
com.example.uhf:dimen/m3_comp_switch_unselected_hover_state_layer_opacity = 0x7f07019f
com.example.uhf:string/preferences_supplemental_summary = 0x7f120306
com.example.uhf:attr/colorOnSurfaceInverse = 0x7f040115
com.example.uhf:drawable/abc_btn_colored_material = 0x7f080030
com.example.uhf:id/jumpToStart = 0x7f09017c
com.example.uhf:style/Widget.Material3.SideSheet = 0x7f1303f5
com.example.uhf:style/TextAppearance.Material3.HeadlineSmall = 0x7f130217
com.example.uhf:attr/textAppearanceSearchResultSubtitle = 0x7f040494
com.example.uhf:dimen/design_fab_elevation = 0x7f070071
com.example.uhf:attr/colorOnSurface = 0x7f040114
com.example.uhf:color/material_dynamic_primary50 = 0x7f06025e
com.example.uhf:attr/indicatorDirectionLinear = 0x7f04025b
com.example.uhf:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f13030a
com.example.uhf:string/uhf_btn_set_link = 0x7f12043b
com.example.uhf:attr/flow_lastHorizontalBias = 0x7f04020e
com.example.uhf:string/fingerprint_btn_set_PacketSize = 0x7f120181
com.example.uhf:attr/colorOnSecondaryFixed = 0x7f040112
com.example.uhf:id/customPanel = 0x7f0900f3
com.example.uhf:anim/m3_side_sheet_enter_from_right = 0x7f010027
com.example.uhf:id/cbKill = 0x7f0900c7
com.example.uhf:attr/layout_anchor = 0x7f040294
com.example.uhf:dimen/network_list_default_size = 0x7f070313
com.example.uhf:attr/colorOnPrimaryFixedVariant = 0x7f04010e
com.example.uhf:attr/checkedIconVisible = 0x7f0400c4
com.example.uhf:array/fileEndingAudio = 0x7f030036
com.example.uhf:attr/showAsAction = 0x7f0403fc
com.example.uhf:drawable/mtrl_switch_thumb = 0x7f0800e1
com.example.uhf:color/m3_ref_palette_dynamic_primary80 = 0x7f0600f3
com.example.uhf:attr/colorOutlineVariant = 0x7f04011c
com.example.uhf:color/primary_dark_material_light = 0x7f060311
com.example.uhf:attr/trackCornerRadius = 0x7f0404f4
com.example.uhf:string/upload_title = 0x7f1204ab
com.example.uhf:attr/colorOnContainerUnchecked = 0x7f040108
com.example.uhf:string/desfire_title_value_credit = 0x7f12013f
com.example.uhf:style/Theme.AppCompat.Dialog.Alert = 0x7f130240
com.example.uhf:drawable/abc_text_cursor_material = 0x7f08006e
com.example.uhf:layout/mtrl_calendar_day_of_week = 0x7f0c005a
com.example.uhf:dimen/mtrl_slider_label_padding = 0x7f0702eb
com.example.uhf:dimen/mtrl_slider_label_radius = 0x7f0702ec
com.example.uhf:string/battery_tips_STATUS_DISCHARGING = 0x7f12007b
com.example.uhf:attr/defaultDuration = 0x7f04017a
com.example.uhf:string/nfc_msg_no_device = 0x7f1202cf
com.example.uhf:color/m3_ref_palette_dynamic_neutral30 = 0x7f0600c2
com.example.uhf:style/Base.TextAppearance.AppCompat.Tooltip = 0x7f130035
com.example.uhf:style/TextAppearance.Compat.Notification.Line2 = 0x7f1301f0
com.example.uhf:string/msg_login_logout = 0x7f12025e
com.example.uhf:style/Widget.MaterialComponents.BottomNavigationView.PrimarySurface = 0x7f130425
com.example.uhf:id/etPtr_filter_wt = 0x7f09013f
com.example.uhf:color/material_on_surface_emphasis_medium = 0x7f060292
com.example.uhf:string/cancel = 0x7f1200ec
com.example.uhf:attr/contentPaddingStart = 0x7f04014f
com.example.uhf:style/Base.Widget.AppCompat.Spinner.Underlined = 0x7f1300fc
com.example.uhf:attr/collapseContentDescription = 0x7f0400f1
com.example.uhf:string/workTime = 0x7f1204b9
com.example.uhf:attr/autoCompleteMode = 0x7f040044
com.example.uhf:color/abc_primary_text_material_dark = 0x7f06000b
com.example.uhf:dimen/m3_navigation_item_icon_padding = 0x7f0701c6
com.example.uhf:layout/mtrl_calendar_months = 0x7f0c0060
com.example.uhf:attr/chipEndPadding = 0x7f0400c9
com.example.uhf:integer/m3_sys_motion_duration_extra_long1 = 0x7f0a000f
com.example.uhf:color/viewfinder_laser = 0x7f060344
com.example.uhf:style/Theme.MaterialComponents.DayNight.Bridge = 0x7f130276
com.example.uhf:style/Base.Widget.AppCompat.Button.Borderless = 0x7f1300d3
com.example.uhf:style/Widget.MaterialComponents.BottomNavigationView = 0x7f130423
com.example.uhf:attr/autoCompleteTextViewStyle = 0x7f040045
com.example.uhf:color/mtrl_tabs_legacy_text_color_selector = 0x7f060302
com.example.uhf:style/ThemeOverlay.Material3.DynamicColors.Light = 0x7f1302c8
com.example.uhf:attr/buttonStyle = 0x7f04009d
com.example.uhf:style/Widget.AppCompat.SeekBar = 0x7f130351
com.example.uhf:id/decelerate = 0x7f0900f6
com.example.uhf:integer/mtrl_badge_max_character_count = 0x7f0a002d
com.example.uhf:attr/colorOnPrimaryFixed = 0x7f04010d
com.example.uhf:attr/brightness = 0x7f040090
com.example.uhf:id/design_bottom_sheet = 0x7f0900ff
com.example.uhf:anim/abc_fade_in = 0x7f010000
com.example.uhf:string/title_ping_pingresult = 0x7f1203e4
com.example.uhf:attr/textAllCaps = 0x7f040476
com.example.uhf:color/m3_ref_palette_secondary50 = 0x7f060156
com.example.uhf:attr/clearTop = 0x7f0400e2
com.example.uhf:dimen/text_size_11 = 0x7f070334
com.example.uhf:color/m3_sys_color_dark_on_tertiary = 0x7f060185
com.example.uhf:attr/badgeVerticalPadding = 0x7f040062
com.example.uhf:attr/motionDurationShort2 = 0x7f04034f
com.example.uhf:attr/contentPaddingBottom = 0x7f04014b
com.example.uhf:style/Widget.Material3.MaterialCalendar.DayTextView = 0x7f1303ca
com.example.uhf:dimen/m3_alert_dialog_corner_size = 0x7f0700a4
com.example.uhf:color/mtrl_btn_stroke_color_selector = 0x7f0602d5
com.example.uhf:id/month_navigation_fragment_toggle = 0x7f0901b6
com.example.uhf:attr/fabSize = 0x7f0401ee
com.example.uhf:id/action_image = 0x7f090061
com.example.uhf:attr/buttonGravity = 0x7f040097
com.example.uhf:string/rfid_title_continuous_write_fail_count = 0x7f12035d
com.example.uhf:attr/circularflow_angles = 0x7f0400dd
com.example.uhf:string/btGetPrar = 0x7f1200b2
com.example.uhf:string/download_msg_up_fail = 0x7f120155
com.example.uhf:attr/textBackgroundRotate = 0x7f04049f
com.example.uhf:integer/m3_sys_shape_corner_large_corner_family = 0x7f0a0023
com.example.uhf:dimen/abc_edit_text_inset_bottom_material = 0x7f07002c
com.example.uhf:attr/content = 0x7f040142
com.example.uhf:array/i760name = 0x7f03003d
com.example.uhf:string/bd_title_Time = 0x7f12008f
com.example.uhf:style/AppBaseTheme = 0x7f13000d
com.example.uhf:dimen/mtrl_calendar_day_corner = 0x7f07027b
com.example.uhf:attr/itemShapeAppearanceOverlay = 0x7f040276
com.example.uhf:attr/region_heightMoreThan = 0x7f0403d0
com.example.uhf:macro/m3_comp_extended_fab_primary_label_text_type = 0x7f0d002f
com.example.uhf:id/fitEnd = 0x7f090150
com.example.uhf:macro/m3_comp_radio_button_unselected_pressed_icon_color = 0x7f0d00e4
com.example.uhf:macro/m3_comp_checkbox_selected_error_icon_color = 0x7f0d000a
com.example.uhf:dimen/material_cursor_inset = 0x7f070235
com.example.uhf:color/pink = 0x7f06030d
com.example.uhf:integer/m3_badge_max_number = 0x7f0a0009
com.example.uhf:id/tvSearchRange = 0x7f0902c6
com.example.uhf:attr/initialExpandedChildrenCount = 0x7f040260
com.example.uhf:attr/tabMode = 0x7f040464
com.example.uhf:string/msg_rfid_set = 0x7f120271
com.example.uhf:attr/actionModeSplitBackground = 0x7f04001d
com.example.uhf:string/mtrl_picker_range_header_only_start_selected = 0x7f1202a3
com.example.uhf:attr/chipBackgroundColor = 0x7f0400c7
com.example.uhf:animator/fragment_open_enter = 0x7f020007
com.example.uhf:styleable/ActivityChooserView = 0x7f140005
com.example.uhf:string/tvCount_Data = 0x7f120400
com.example.uhf:style/Widget.AppCompat.PopupWindow = 0x7f130349
com.example.uhf:color/mtrl_choice_chip_text_color = 0x7f0602e5
com.example.uhf:macro/m3_comp_primary_navigation_tab_active_pressed_state_layer_color = 0x7f0d00ca
com.example.uhf:color/m3_sys_color_dynamic_dark_secondary_container = 0x7f0601ae
com.example.uhf:attr/actionMenuTextColor = 0x7f040011
com.example.uhf:dimen/m3_comp_date_picker_modal_header_container_height = 0x7f07010a
com.example.uhf:layout/image_frame = 0x7f0c003a
com.example.uhf:attr/materialClockStyle = 0x7f040314
com.example.uhf:attr/flow_horizontalAlign = 0x7f04020a
com.example.uhf:attr/checkedIconEnabled = 0x7f0400bf
com.example.uhf:attr/checkBoxPreferenceStyle = 0x7f0400b7
com.example.uhf:attr/endIconCheckable = 0x7f0401b8
com.example.uhf:string/msg_load_image_fail = 0x7f120257
com.example.uhf:attr/flow_firstVerticalStyle = 0x7f040209
com.example.uhf:string/mtrl_picker_text_input_year_abbr = 0x7f1202ae
com.example.uhf:attr/bottomSheetDragHandleStyle = 0x7f040083
com.example.uhf:style/Base.TextAppearance.MaterialComponents.Badge = 0x7f130048
com.example.uhf:id/right = 0x7f090239
com.example.uhf:attr/badgeWithTextShapeAppearanceOverlay = 0x7f040068
com.example.uhf:macro/m3_comp_outlined_card_pressed_outline_color = 0x7f0d00af
com.example.uhf:style/Widget.MaterialComponents.ChipGroup = 0x7f13043a
com.example.uhf:attr/dropDownBackgroundTint = 0x7f0401a6
com.example.uhf:attr/imagePanY = 0x7f040252
com.example.uhf:array/action2 = 0x7f030001
com.example.uhf:string/Fixed_Frequency_915MHz = 0x7f120009
com.example.uhf:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__0 = 0x7f08001b
com.example.uhf:attr/autoSizeMinTextSize = 0x7f040048
com.example.uhf:style/Base.TextAppearance.AppCompat.Medium = 0x7f130029
com.example.uhf:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x1 = 0x7f0701fb
com.example.uhf:array/arrayLink = 0x7f030016
com.example.uhf:dimen/m3_comp_outlined_text_field_outline_width = 0x7f07015e
com.example.uhf:attr/materialCalendarHeaderConfirmButton = 0x7f040304
com.example.uhf:color/m3_sys_color_dynamic_light_on_secondary = 0x7f0601c5
com.example.uhf:attr/cardPreventCornerOverlap = 0x7f0400a6
com.example.uhf:layout/mtrl_alert_select_dialog_singlechoice = 0x7f0c0057
com.example.uhf:attr/springMass = 0x7f040420
com.example.uhf:attr/layout_behavior = 0x7f040296
com.example.uhf:string/fontGrayScaleRange = 0x7f1201b8
com.example.uhf:attr/cardMaxElevation = 0x7f0400a5
com.example.uhf:dimen/mtrl_btn_z = 0x7f070275
com.example.uhf:string/button_share_contact = 0x7f1200de
com.example.uhf:array/arrayMemoryBankValue = 0x7f03001a
com.example.uhf:color/mtrl_scrim_color = 0x7f0602fa
com.example.uhf:style/Base.Widget.AppCompat.RatingBar = 0x7f1300f4
com.example.uhf:color/material_personalized_color_control_activated = 0x7f060297
com.example.uhf:drawable/abc_textfield_default_mtrl_alpha = 0x7f080073
com.example.uhf:animator/design_fab_show_motion_spec = 0x7f020002
com.example.uhf:style/Base.Widget.MaterialComponents.Slider = 0x7f13011f
com.example.uhf:style/Base.Theme.Material3.Light = 0x7f130063
com.example.uhf:color/m3_card_stroke_color = 0x7f060089
com.example.uhf:layout/custom_dialog = 0x7f0c001e
com.example.uhf:attr/materialCardViewStyle = 0x7f040312
com.example.uhf:color/m3_ref_palette_dynamic_tertiary95 = 0x7f06010f
com.example.uhf:id/rtl = 0x7f09023f
com.example.uhf:attr/cardForegroundColor = 0x7f0400a4
com.example.uhf:attr/fontProviderAuthority = 0x7f04021b
com.example.uhf:attr/cardBackgroundColor = 0x7f0400a1
com.example.uhf:dimen/m3_comp_input_chip_unselected_outline_width = 0x7f070137
com.example.uhf:color/m3_timepicker_secondary_text_button_text_color = 0x7f06022c
com.example.uhf:styleable/MockView = 0x7f140068
com.example.uhf:color/design_default_color_on_error = 0x7f060048
com.example.uhf:array/arrayBank = 0x7f030006
com.example.uhf:color/m3_sys_color_dark_tertiary_container = 0x7f060197
com.example.uhf:dimen/abc_text_size_menu_header_material = 0x7f07004a
com.example.uhf:attr/actionMenuTextAppearance = 0x7f040010
com.example.uhf:color/mtrl_navigation_bar_ripple_color = 0x7f0602f1
com.example.uhf:array/arrayFreHop = 0x7f03000f
com.example.uhf:attr/verticalOffset = 0x7f04050f
com.example.uhf:dimen/mtrl_btn_stroke_size = 0x7f070270
com.example.uhf:color/m3_sys_color_on_primary_fixed_variant = 0x7f06020b
com.example.uhf:string/rfid_msg_write_succ = 0x7f120355
com.example.uhf:attr/font = 0x7f040219
com.example.uhf:array/arrayUartBaud = 0x7f030029
com.example.uhf:attr/carousel_touchUp_dampeningFactor = 0x7f0400b2
com.example.uhf:dimen/list_padding = 0x7f0700a1
com.example.uhf:attr/carousel_touchUpMode = 0x7f0400b1
com.example.uhf:color/material_personalized_primary_inverse_text_disable_only = 0x7f0602c6
com.example.uhf:attr/marginLeftSystemWindowInsets = 0x7f0402f4
com.example.uhf:dimen/design_snackbar_text_size = 0x7f07008a
com.example.uhf:color/m3_tabs_text_color = 0x7f06021a
com.example.uhf:string/title_activity_download = 0x7f1203ab
com.example.uhf:dimen/m3_comp_bottom_app_bar_container_elevation = 0x7f070106
com.example.uhf:dimen/m3_sys_motion_easing_standard_decelerate_control_x2 = 0x7f07021c
com.example.uhf:animator/mtrl_fab_transformation_sheet_expand_spec = 0x7f020021
com.example.uhf:string/m3_ref_typeface_plain_medium = 0x7f120213
com.example.uhf:attr/carousel_previousState = 0x7f0400b0
com.example.uhf:attr/expandedTitleGravity = 0x7f0401d6
com.example.uhf:color/m3_sys_color_dynamic_light_outline_variant = 0x7f0601cc
com.example.uhf:drawable/m3_bottom_sheet_drag_handle = 0x7f0800b5
com.example.uhf:layout/abc_screen_content_include = 0x7f0c0014
com.example.uhf:id/spPower = 0x7f090271
com.example.uhf:attr/buttonBarStyle = 0x7f040095
com.example.uhf:id/forever = 0x7f090157
com.example.uhf:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense = 0x7f13040c
com.example.uhf:attr/buttonBarButtonStyle = 0x7f040091
com.example.uhf:integer/mtrl_tab_indicator_anim_duration_ms = 0x7f0a003e
com.example.uhf:attr/materialCalendarHeaderLayout = 0x7f040306
com.example.uhf:style/Base.Widget.AppCompat.Button.Borderless.Colored = 0x7f1300d4
com.example.uhf:attr/boxStrokeWidthFocused = 0x7f04008f
com.example.uhf:attr/layout_constrainedWidth = 0x7f04029a
com.example.uhf:string/copy = 0x7f120101
com.example.uhf:color/material_personalized_color_on_surface_inverse = 0x7f0602a4
com.example.uhf:style/TextAppearance.Design.Placeholder = 0x7f1301f9
com.example.uhf:macro/m3_comp_navigation_drawer_active_hover_icon_color = 0x7f0d007b
com.example.uhf:attr/isMaterial3Theme = 0x7f040264
com.example.uhf:color/mtrl_error = 0x7f0602e6
com.example.uhf:attr/fabAnchorMode = 0x7f0401e8
com.example.uhf:color/mtrl_on_surface_ripple_color = 0x7f0602f6
com.example.uhf:dimen/mtrl_snackbar_margin = 0x7f0702f8
com.example.uhf:attr/cornerSizeBottomLeft = 0x7f04015d
com.example.uhf:attr/dialogCornerRadius = 0x7f040184
com.example.uhf:attr/boxCornerRadiusTopEnd = 0x7f04008a
com.example.uhf:attr/colorTertiaryFixed = 0x7f040137
com.example.uhf:id/withText = 0x7f0902db
com.example.uhf:attr/boxCornerRadiusBottomEnd = 0x7f040088
com.example.uhf:color/m3_sys_color_dynamic_dark_on_surface = 0x7f0601a5
com.example.uhf:anim/mtrl_bottom_sheet_slide_out = 0x7f01002b
com.example.uhf:dimen/mtrl_fab_min_touch_target = 0x7f0702bc
com.example.uhf:string/mtrl_switch_track_path = 0x7f1202bb
com.example.uhf:array/arrayAntiQ = 0x7f030005
com.example.uhf:attr/fastScrollVerticalTrackDrawable = 0x7f0401f3
com.example.uhf:color/material_timepicker_clockface = 0x7f0602d1
com.example.uhf:dimen/m3_comp_navigation_bar_icon_size = 0x7f070141
com.example.uhf:attr/actionModeCloseButtonStyle = 0x7f040013
com.example.uhf:attr/bottomAppBarStyle = 0x7f04007f
com.example.uhf:id/rb_Others = 0x7f09022d
com.example.uhf:attr/floatingActionButtonSmallSurfaceStyle = 0x7f040201
com.example.uhf:macro/m3_comp_extended_fab_surface_container_color = 0x7f0d0032
com.example.uhf:attr/circularflow_defaultRadius = 0x7f0400df
com.example.uhf:attr/background = 0x7f04004e
com.example.uhf:attr/borderWidth = 0x7f04007d
com.example.uhf:id/barrier = 0x7f09007f
com.example.uhf:dimen/abc_progress_bar_height_material = 0x7f070035
com.example.uhf:color/m3_ref_palette_error10 = 0x7f060112
com.example.uhf:attr/textAppearanceHeadlineSmall = 0x7f040489
com.example.uhf:attr/colorSurfaceVariant = 0x7f040133
com.example.uhf:attr/clickAction = 0x7f0400e4
com.example.uhf:color/m3_sys_color_dark_tertiary = 0x7f060196
com.example.uhf:attr/behavior_halfExpandedRatio = 0x7f040073
com.example.uhf:id/etLen_light_filter = 0x7f090134
com.example.uhf:array/arrayKeyType = 0x7f030014
com.example.uhf:layout/abc_select_dialog_material = 0x7f0c001a
com.example.uhf:attr/tabSelectedTextAppearance = 0x7f04046c
com.example.uhf:attr/motionDurationMedium1 = 0x7f04034a
com.example.uhf:style/Widget.AppCompat.Light.ActionBar.Solid = 0x7f13032d
com.example.uhf:attr/chipStrokeWidth = 0x7f0400d8
com.example.uhf:attr/backgroundInsetTop = 0x7f040053
com.example.uhf:string/up_msg_file_not_exist = 0x7f120498
com.example.uhf:id/navigation_bar_item_large_label_view = 0x7f0901d7
com.example.uhf:dimen/mtrl_btn_disabled_z = 0x7f070261
com.example.uhf:style/Base.TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f13002d
com.example.uhf:attr/behavior_hideable = 0x7f040074
com.example.uhf:attr/barrierMargin = 0x7f04006d
com.example.uhf:style/Base.TextAppearance.MaterialComponents.Headline6 = 0x7f13004a
com.example.uhf:color/notification_action_color_filter = 0x7f06030a
com.example.uhf:id/search_edit_frame = 0x7f09024e
com.example.uhf:attr/colorPrimaryFixed = 0x7f040120
com.example.uhf:string/preferences_decode_Data_Matrix_title = 0x7f1202f6
com.example.uhf:string/button_google_shopper = 0x7f1200d2
com.example.uhf:attr/barrierDirection = 0x7f04006c
com.example.uhf:attr/paddingBottomSystemWindowInsets = 0x7f040389
com.example.uhf:style/ThemeOverlay.Material3.BottomNavigationView = 0x7f1302b3
com.example.uhf:string/file_title_sel_file = 0x7f120173
com.example.uhf:dimen/mtrl_btn_text_btn_icon_padding = 0x7f070271
com.example.uhf:attr/layout_constraintHorizontal_bias = 0x7f0402b0
com.example.uhf:attr/nestedScrollFlags = 0x7f040377
com.example.uhf:string/bd_title_bd = 0x7f120090
com.example.uhf:color/background_floating_material_light = 0x7f06001e
com.example.uhf:string/desfire_ms_pwd_not_null = 0x7f120109
com.example.uhf:id/mtrl_picker_header_title_and_selection = 0x7f0901cb
com.example.uhf:color/m3_sys_color_dynamic_light_secondary_container = 0x7f0601d0
com.example.uhf:attr/colorOnPrimarySurface = 0x7f04010f
com.example.uhf:attr/floatingActionButtonSmallTertiaryStyle = 0x7f040202
com.example.uhf:string/msg_tag_hex = 0x7f12027e
com.example.uhf:attr/fontVariationSettings = 0x7f040223
com.example.uhf:string/lp_btn_close = 0x7f1201fa
com.example.uhf:drawable/ic_launcher_foreground = 0x7f0800a7
com.example.uhf:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f130027
com.example.uhf:id/time = 0x7f0902ab
com.example.uhf:id/material_clock_period_am_button = 0x7f0901a1
com.example.uhf:attr/closeIconTint = 0x7f0400ee
com.example.uhf:string/psam_msg_upgrade_succ = 0x7f12030e
com.example.uhf:attr/strokeColor = 0x7f040439
com.example.uhf:style/Base.Widget.AppCompat.ActionBar.TabBar = 0x7f1300c9
com.example.uhf:attr/state_collapsed = 0x7f04042e
com.example.uhf:macro/m3_comp_navigation_drawer_inactive_hover_state_layer_color = 0x7f0d008b
com.example.uhf:string/tvKill_Lock = 0x7f12040e
com.example.uhf:string/fingerprint_msg_set_Baudrate_fail = 0x7f120196
com.example.uhf:array/arrayPower = 0x7f030021
com.example.uhf:string/btBack1 = 0x7f1200a8
com.example.uhf:attr/badgeWidePadding = 0x7f040063
com.example.uhf:style/TextAppearance.AppCompat.Menu = 0x7f1301d1
com.example.uhf:dimen/abc_dialog_padding_material = 0x7f070024
com.example.uhf:color/m3_sys_color_light_surface_container_highest = 0x7f060203
com.example.uhf:attr/layout_constraintCircle = 0x7f0402a2
com.example.uhf:color/abc_tint_switch_track = 0x7f060018
com.example.uhf:id/snapMargins = 0x7f090267
com.example.uhf:style/TextAppearance.MaterialComponents.Body2 = 0x7f130224
com.example.uhf:anim/btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f
com.example.uhf:attr/backgroundInsetBottom = 0x7f040050
com.example.uhf:style/MaterialAlertDialog.Material3.Title.Icon = 0x7f13012d
com.example.uhf:id/launch_product_query = 0x7f09017e
com.example.uhf:attr/backgroundColor = 0x7f04004f
com.example.uhf:attr/height = 0x7f040231
com.example.uhf:dimen/mtrl_calendar_selection_text_baseline_to_bottom = 0x7f070295
com.example.uhf:styleable/SideSheetBehavior_Layout = 0x7f14008c
com.example.uhf:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f1302fe
com.example.uhf:attr/backHandlingEnabled = 0x7f04004d
com.example.uhf:color/ripple_material_dark = 0x7f060325
com.example.uhf:string/uhf_msg_tab_lock = 0x7f120470
com.example.uhf:attr/autoTransition = 0x7f04004c
com.example.uhf:attr/autoSizeStepGranularity = 0x7f04004a
com.example.uhf:attr/menu = 0x7f040331
com.example.uhf:attr/prefixTextAppearance = 0x7f0403b8
com.example.uhf:color/mtrl_switch_track_decoration_tint = 0x7f0602fd
com.example.uhf:anim/design_bottom_sheet_slide_in = 0x7f010018
com.example.uhf:id/llSession = 0x7f090192
com.example.uhf:color/mtrl_tabs_ripple_color = 0x7f060303
com.example.uhf:string/desfire_title_file_num = 0x7f12012b
com.example.uhf:attr/badgeWithTextShapeAppearance = 0x7f040067
com.example.uhf:attr/carousel_touchUp_velocityThreshold = 0x7f0400b3
com.example.uhf:animator/m3_btn_elevated_btn_state_list_anim = 0x7f02000a
com.example.uhf:color/design_default_color_primary = 0x7f06004c
com.example.uhf:drawable/abc_ratingbar_material = 0x7f08005c
com.example.uhf:styleable/ViewBackgroundHelper = 0x7f1400a9
com.example.uhf:styleable/FontFamily = 0x7f14003c
com.example.uhf:color/m3_tabs_ripple_color = 0x7f060218
com.example.uhf:attr/animateCircleAngleTo = 0x7f040038
com.example.uhf:attr/selectableItemBackground = 0x7f0403e8
com.example.uhf:string/yid_msg_scan_timeout = 0x7f1204be
com.example.uhf:color/material_dynamic_neutral_variant40 = 0x7f060250
com.example.uhf:attr/splitTrack = 0x7f04041d
com.example.uhf:dimen/mtrl_alert_dialog_picker_background_inset = 0x7f07024f
com.example.uhf:macro/m3_comp_fab_tertiary_container_color = 0x7f0d003f
com.example.uhf:id/mtrl_picker_text_input_range_start = 0x7f0901cf
com.example.uhf:drawable/$ic_launcher_foreground__0 = 0x7f080006
com.example.uhf:style/RtlOverlay.Widget.AppCompat.ActionBar.TitleItem = 0x7f130170
com.example.uhf:bool/enable_system_foreground_service_default = 0x7f050004
com.example.uhf:id/north = 0x7f0901e1
com.example.uhf:attr/alertDialogCenterButtons = 0x7f04002d
com.example.uhf:attr/errorContentDescription = 0x7f0401c7
com.example.uhf:string/network_msg_wifi_not_enable = 0x7f1202ca
com.example.uhf:attr/actionModeCloseDrawable = 0x7f040015
com.example.uhf:array/arrayPower2 = 0x7f030022
com.example.uhf:attr/activityName = 0x7f040029
com.example.uhf:string/fingerprint_tab_acquisition = 0x7f1201a1
com.example.uhf:attr/behavior_peekHeight = 0x7f040076
com.example.uhf:id/showTitle = 0x7f09025f
com.example.uhf:dimen/m3_btn_padding_bottom = 0x7f0700de
com.example.uhf:dimen/abc_dialog_list_padding_bottom_no_buttons = 0x7f070020
com.example.uhf:attr/SharedValue = 0x7f040000
com.example.uhf:styleable/MotionEffect = 0x7f14006a
com.example.uhf:dimen/m3_timepicker_display_stroke_width = 0x7f070223
com.example.uhf:style/Base.Widget.Material3.ExtendedFloatingActionButton = 0x7f13010b
com.example.uhf:attr/allowDividerAbove = 0x7f040030
com.example.uhf:color/material_dynamic_secondary100 = 0x7f060267
com.example.uhf:dimen/hint_pressed_alpha_material_light = 0x7f07009c
com.example.uhf:color/m3_ref_palette_dynamic_neutral_variant90 = 0x7f0600e3
com.example.uhf:attr/colorOnContainer = 0x7f040107
com.example.uhf:string/uhf_msg_set_fail = 0x7f12045f
com.example.uhf:attr/endIconTint = 0x7f0401be
com.example.uhf:macro/m3_comp_slider_inactive_track_color = 0x7f0d0110
com.example.uhf:color/design_dark_default_color_on_error = 0x7f06003b
com.example.uhf:dimen/mtrl_exposed_dropdown_menu_popup_vertical_padding = 0x7f0702aa
com.example.uhf:dimen/m3_comp_date_picker_modal_range_selection_header_container_height = 0x7f07010b
com.example.uhf:macro/m3_comp_time_picker_period_selector_selected_pressed_state_layer_color = 0x7f0d0159
com.example.uhf:macro/m3_comp_top_app_bar_large_headline_color = 0x7f0d016b
com.example.uhf:style/ShapeAppearance.Material3.Corner.Full = 0x7f13019b
com.example.uhf:string/button_email = 0x7f1200cf
com.example.uhf:dimen/slidingmenu_offset = 0x7f070331
com.example.uhf:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f13003a
com.example.uhf:attr/materialCalendarDayOfWeekLabel = 0x7f040301
com.example.uhf:string/m1_title_write = 0x7f12020f
com.example.uhf:attr/endIconMinSize = 0x7f0401bb
com.example.uhf:attr/viewTransitionMode = 0x7f040512
com.example.uhf:array/arrayFringeprintBaud = 0x7f030011
com.example.uhf:id/easeIn = 0x7f090115
com.example.uhf:attr/itemTextAppearanceActiveBoldEnabled = 0x7f040281
com.example.uhf:id/wrap_content_constrained = 0x7f0902e0
com.example.uhf:attr/tabTextAppearance = 0x7f04046f
com.example.uhf:attr/motionDurationShort4 = 0x7f040351
com.example.uhf:style/Theme.MaterialComponents.Dialog = 0x7f130284
com.example.uhf:array/arrayChannelSpc0 = 0x7f03000a
com.example.uhf:attr/dialogPreferenceStyle = 0x7f040188
com.example.uhf:attr/marginTopSystemWindowInsets = 0x7f0402f6
com.example.uhf:color/androidx_core_ripple_material_light = 0x7f06001b
com.example.uhf:attr/actionBarDivider = 0x7f040002
com.example.uhf:dimen/m3_comp_switch_selected_hover_state_layer_opacity = 0x7f07019a
com.example.uhf:drawable/btn_radio_on_mtrl = 0x7f080082
com.example.uhf:attr/tabIndicator = 0x7f04045a
com.example.uhf:string/uhf_msg_length_error = 0x7f120452
com.example.uhf:color/m3_ref_palette_dynamic_tertiary40 = 0x7f060109
com.example.uhf:string/fingerprint_tab_history = 0x7f1201a2
com.example.uhf:string/abc_activitychooserview_choose_application = 0x7f120026
com.example.uhf:id/disableHome = 0x7f090107
com.example.uhf:style/ThemeOverlay.Material3.NavigationRailView = 0x7f1302dc
com.example.uhf:dimen/mtrl_min_touch_target_size = 0x7f0702c7
com.example.uhf:style/Base.TextAppearance.AppCompat.Title.Inverse = 0x7f130034
com.example.uhf:color/m3_sys_color_dynamic_primary_fixed_dim = 0x7f0601e3
com.example.uhf:style/ThemeOverlay.Material3.Button.ElevatedButton = 0x7f1302b6
com.example.uhf:dimen/m3_btn_icon_only_min_width = 0x7f0700db
com.example.uhf:attr/colorSecondaryVariant = 0x7f040129
com.example.uhf:dimen/m3_extended_fab_end_padding = 0x7f0701b5
com.example.uhf:string/msg_login_fail = 0x7f12025d
com.example.uhf:color/viewfinder_mask = 0x7f060345
com.example.uhf:layout/preference_category = 0x7f0c007a
com.example.uhf:attr/circularProgressIndicatorStyle = 0x7f0400dc
com.example.uhf:styleable/MaterialButton = 0x7f140056
com.example.uhf:attr/flow_lastVerticalBias = 0x7f040210
com.example.uhf:color/m3_button_ripple_color_selector = 0x7f060084
com.example.uhf:attr/actionBarWidgetTheme = 0x7f04000c
com.example.uhf:attr/flow_firstHorizontalBias = 0x7f040206
com.example.uhf:dimen/preference_seekbar_padding_vertical = 0x7f07032c
com.example.uhf:drawable/material_ic_calendar_black_24dp = 0x7f0800bf
com.example.uhf:attr/layout_constraintCircleRadius = 0x7f0402a4
com.example.uhf:attr/exampleDimension = 0x7f0401d0
com.example.uhf:attr/dividerHorizontal = 0x7f040190
com.example.uhf:color/m3_selection_control_ripple_color_selector = 0x7f06016b
com.example.uhf:dimen/m3_comp_outlined_text_field_disabled_input_text_opacity = 0x7f07015a
com.example.uhf:attr/materialCalendarStyle = 0x7f04030c
com.example.uhf:style/Base.Widget.Material3.FloatingActionButton.Small = 0x7f13010f
com.example.uhf:color/secondary_text_default_material_light = 0x7f06032e
com.example.uhf:color/m3_ref_palette_neutral0 = 0x7f06011e
com.example.uhf:attr/behavior_autoHide = 0x7f04006e
com.example.uhf:attr/arcMode = 0x7f04003f
com.example.uhf:styleable/MaterialCalendarItem = 0x7f140059
com.example.uhf:color/material_slider_thumb_color = 0x7f0602cd
com.example.uhf:drawable/mtrl_switch_thumb_pressed_checked = 0x7f0800e6
com.example.uhf:attr/SharedValueId = 0x7f040001
com.example.uhf:attr/selectorSize = 0x7f0403eb
com.example.uhf:id/TvTagRssi = 0x7f09002a
com.example.uhf:attr/centerIfNoTextEnabled = 0x7f0400b4
com.example.uhf:id/rbUser_filter_deact = 0x7f090226
com.example.uhf:attr/titleMarginBottom = 0x7f0404d9
com.example.uhf:string/fingerprint_title_show_img = 0x7f1201b4
com.example.uhf:color/help_view = 0x7f060073
com.example.uhf:dimen/notification_large_icon_height = 0x7f07031e
com.example.uhf:attr/itemTextAppearance = 0x7f04027f
com.example.uhf:string/desfire_title_card_properties = 0x7f120123
com.example.uhf:styleable/Badge = 0x7f140016
com.example.uhf:color/material_personalized_color_on_secondary = 0x7f0602a1
com.example.uhf:string/uhf_msg_addr_must_decimal = 0x7f120441
com.example.uhf:string/uhf_msg_set_filter_fail2 = 0x7f120461
com.example.uhf:color/mtrl_textinput_hovered_box_stroke_color = 0x7f060309
com.example.uhf:attr/backgroundInsetStart = 0x7f040052
com.example.uhf:attr/min = 0x7f040335
com.example.uhf:styleable/ActionBarLayout = 0x7f140001
com.example.uhf:dimen/m3_comp_fab_primary_small_container_height = 0x7f070124
com.example.uhf:string/rfid_mgs_error_veri_fail = 0x7f12033b
com.example.uhf:array/arrayMode = 0x7f03001b
com.example.uhf:attr/lineHeight = 0x7f0402dd
com.example.uhf:animator/mtrl_btn_state_list_anim = 0x7f020015
com.example.uhf:id/cut = 0x7f0900f4
com.example.uhf:array/fileEndingWebText = 0x7f03003a
com.example.uhf:string/title_other_devices = 0x7f1203dd
com.example.uhf:style/Widget.Material3.FloatingActionButton.Small.Tertiary = 0x7f1303bd
com.example.uhf:bool/enable_system_job_service_default = 0x7f050005
com.example.uhf:attr/actionOverflowMenuStyle = 0x7f040022
com.example.uhf:attr/colorBackgroundFloating = 0x7f0400fe
com.example.uhf:color/m3_dynamic_default_color_primary_text = 0x7f06009d
com.example.uhf:anim/btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014
com.example.uhf:animator/m3_chip_state_list_anim = 0x7f02000e
com.example.uhf:attr/buttonIconDimen = 0x7f040099
com.example.uhf:color/m3_ref_palette_primary0 = 0x7f060143
com.example.uhf:color/m3_ref_palette_tertiary0 = 0x7f06015d
com.example.uhf:animator/m3_elevated_chip_state_list_anim = 0x7f02000f
com.example.uhf:attr/marginRightSystemWindowInsets = 0x7f0402f5
com.example.uhf:anim/mtrl_card_lowers_interpolator = 0x7f01002c
com.example.uhf:attr/colorPrimaryDark = 0x7f04011f
com.example.uhf:id/x_right = 0x7f0902e2
com.example.uhf:dimen/m3_comp_radio_button_disabled_unselected_icon_opacity = 0x7f07016b
com.example.uhf:color/abc_decor_view_status_guard_light = 0x7f060006
com.example.uhf:style/ShapeAppearance.Material3.Corner.None = 0x7f13019e
com.example.uhf:animator/mtrl_fab_show_motion_spec = 0x7f02001f
com.example.uhf:macro/m3_comp_snackbar_supporting_text_color = 0x7f0d0115
com.example.uhf:attr/multiChoiceItemLayout = 0x7f04036f
com.example.uhf:id/scrollable = 0x7f090249
com.example.uhf:integer/material_motion_duration_medium_2 = 0x7f0a0029
com.example.uhf:attr/colorPrimaryInverse = 0x7f040122
com.example.uhf:color/encode_view = 0x7f060060
com.example.uhf:macro/m3_comp_switch_unselected_hover_icon_color = 0x7f0d0136
com.example.uhf:id/action_context_bar = 0x7f09005f
com.example.uhf:style/Base.V21.ThemeOverlay.Material3.BottomSheetDialog = 0x7f1300af
com.example.uhf:style/Widget.MaterialComponents.BottomSheet = 0x7f130426
com.example.uhf:style/Base.Widget.AppCompat.Toolbar = 0x7f1300ff
com.example.uhf:attr/isMaterial3DynamicColorApplied = 0x7f040263
com.example.uhf:style/RtlOverlay.Widget.AppCompat.Search.DropDown = 0x7f130178
com.example.uhf:dimen/m3_card_elevated_hovered_z = 0x7f0700ee
com.example.uhf:string/uhf_msg_inventory_open_fail = 0x7f12044e
com.example.uhf:dimen/m3_appbar_expanded_title_margin_bottom = 0x7f0700a9
com.example.uhf:style/CardView.Dark = 0x7f130126
com.example.uhf:color/contents_text = 0x7f060034
com.example.uhf:color/bright_foreground_material_dark = 0x7f06002a
com.example.uhf:array/allcls = 0x7f030002
com.example.uhf:dimen/m3_btn_elevation = 0x7f0700d5
com.example.uhf:attr/titleTextEllipsize = 0x7f0404e1
com.example.uhf:string/bluetooth_title_file = 0x7f12009c
com.example.uhf:dimen/m3_carousel_gone_size = 0x7f0700f4
com.example.uhf:string/gps_title_GpsStatus = 0x7f1201c9
com.example.uhf:style/Widget.Material3.Button.TextButton.Dialog = 0x7f130385
com.example.uhf:animator/m3_appbar_state_list_animator = 0x7f020009
com.example.uhf:layout/preference_dropdown_material = 0x7f0c007e
com.example.uhf:id/cbTagFocus = 0x7f0900ca
com.example.uhf:attr/dayStyle = 0x7f040178
com.example.uhf:id/mtrl_calendar_days_of_week = 0x7f0901bd
com.example.uhf:string/ping_msg_fail = 0x7f1202e6
com.example.uhf:style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar = 0x7f130341
com.example.uhf:anim/linear_indeterminate_line1_head_interpolator = 0x7f01001e
com.example.uhf:dimen/m3_sys_motion_easing_legacy_decelerate_control_x2 = 0x7f07020c
com.example.uhf:animator/fragment_open_exit = 0x7f020008
com.example.uhf:attr/itemFillColor = 0x7f040269
com.example.uhf:attr/motionEasingAccelerated = 0x7f040352
com.example.uhf:string/strPermissionDeny = 0x7f120380
com.example.uhf:animator/fragment_fade_enter = 0x7f020005
com.example.uhf:dimen/design_fab_translation_z_hovered_focused = 0x7f070075
com.example.uhf:dimen/item_touch_helper_max_drag_scroll_per_frame = 0x7f07009d
com.example.uhf:integer/material_motion_duration_long_2 = 0x7f0a0027
com.example.uhf:drawable/material_ic_keyboard_arrow_left_black_24dp = 0x7f0800c2
com.example.uhf:array/arrayHop = 0x7f030012
com.example.uhf:color/possible_result_points = 0x7f06030e
com.example.uhf:color/m3_icon_button_icon_color_selector = 0x7f0600aa
com.example.uhf:attr/expandedTitleMarginBottom = 0x7f0401d8
com.example.uhf:animator/fragment_fade_exit = 0x7f020006
com.example.uhf:style/TextAppearance.AppCompat.Widget.Button = 0x7f1301e4
com.example.uhf:attr/checkedIcon = 0x7f0400be
com.example.uhf:id/bestChoice = 0x7f090083
com.example.uhf:animator/fragment_close_enter = 0x7f020003
com.example.uhf:style/Widget.MaterialComponents.TabLayout.PrimarySurface = 0x7f130474
com.example.uhf:color/text_gray = 0x7f06033f
com.example.uhf:color/error_color_material_light = 0x7f060062
com.example.uhf:attr/expandedTitleTextAppearance = 0x7f0401dc
com.example.uhf:macro/m3_comp_switch_selected_focus_state_layer_color = 0x7f0d0122
com.example.uhf:attr/largeFontVerticalOffsetAdjustment = 0x7f04028d
com.example.uhf:style/MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f130137
com.example.uhf:id/fill = 0x7f09014b
com.example.uhf:attr/activeIndicatorLabelPadding = 0x7f040026
com.example.uhf:dimen/m3_comp_primary_navigation_tab_inactive_focus_state_layer_opacity = 0x7f070163
com.example.uhf:anim/btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012
com.example.uhf:color/m3_sys_color_dynamic_on_secondary_fixed_variant = 0x7f0601df
com.example.uhf:color/m3_ref_palette_dynamic_neutral17 = 0x7f0600be
com.example.uhf:string/tvTagUii_Read = 0x7f12042e
com.example.uhf:color/dim_foreground_material_light = 0x7f06005f
com.example.uhf:color/abc_search_url_text_normal = 0x7f06000e
com.example.uhf:array/country_codes = 0x7f030030
com.example.uhf:string/title_ping_background = 0x7f1203df
com.example.uhf:attr/alpha = 0x7f040034
com.example.uhf:attr/transitionPathRotate = 0x7f040502
com.example.uhf:string/title_succ_rate = 0x7f1203f2
com.example.uhf:color/m3_sys_color_on_primary_fixed = 0x7f06020a
com.example.uhf:string/app_menu_surelogout = 0x7f120058
com.example.uhf:color/m3_slider_inactive_track_color = 0x7f060170
com.example.uhf:id/fragment_container_view_tag = 0x7f090158
com.example.uhf:color/mtrl_on_primary_text_btn_text_color_selector = 0x7f0602f5
com.example.uhf:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f13003c
com.example.uhf:string/uhf_title_lock = 0x7f120485
com.example.uhf:id/dimensions = 0x7f090105
com.example.uhf:layout/uhf_kill_fragment = 0x7f0c0090
com.example.uhf:macro/m3_comp_search_bar_hover_supporting_text_color = 0x7f0d00e8
com.example.uhf:attr/listChoiceIndicatorSingleAnimated = 0x7f0402e2
com.example.uhf:anim/mtrl_bottom_sheet_slide_in = 0x7f01002a
com.example.uhf:attr/cardElevation = 0x7f0400a3
com.example.uhf:dimen/m3_btn_text_btn_padding_left = 0x7f0700e5
com.example.uhf:dimen/m3_btn_text_btn_icon_padding_right = 0x7f0700e4
com.example.uhf:drawable/ic_search_black_24 = 0x7f0800af
com.example.uhf:attr/switchPreferenceStyle = 0x7f040450
com.example.uhf:id/dragClockwise = 0x7f09010d
com.example.uhf:anim/m3_side_sheet_exit_to_right = 0x7f010029
com.example.uhf:drawable/folder = 0x7f080098
com.example.uhf:attr/materialThemeOverlay = 0x7f040322
com.example.uhf:anim/m3_side_sheet_enter_from_left = 0x7f010026
com.example.uhf:string/btAddD = 0x7f1200a5
com.example.uhf:id/save_overlay_view = 0x7f090241
com.example.uhf:drawable/m3_avd_show_password = 0x7f0800b4
com.example.uhf:attr/autoAdjustToWithinGrandparentBounds = 0x7f040043
com.example.uhf:attr/drawPath = 0x7f040199
com.example.uhf:id/up = 0x7f0902cf
com.example.uhf:color/m3_ref_palette_neutral90 = 0x7f06012f
com.example.uhf:attr/actionBarTabStyle = 0x7f040009
com.example.uhf:string/material_timepicker_select_time = 0x7f120234
com.example.uhf:attr/allowDividerBelow = 0x7f040032
com.example.uhf:macro/m3_comp_navigation_bar_inactive_pressed_state_layer_color = 0x7f0d0076
com.example.uhf:drawable/ic_m3_chip_close = 0x7f0800aa
com.example.uhf:attr/checkMarkTintMode = 0x7f0400ba
com.example.uhf:color/m3_chip_background_color = 0x7f06008d
com.example.uhf:style/ThemeOverlay.Material3.Dialog.Alert = 0x7f1302c4
com.example.uhf:id/spacer = 0x7f090273
com.example.uhf:attr/behavior_overlapTop = 0x7f040075
com.example.uhf:anim/btn_checkbox_to_checked_icon_null_animation = 0x7f01000e
com.example.uhf:id/autoCompleteToStart = 0x7f09007e
com.example.uhf:attr/tooltipForegroundColor = 0x7f0404e8
com.example.uhf:attr/gestureInsetBottomIgnored = 0x7f04022b
com.example.uhf:style/Preference.SwitchPreference.Material = 0x7f130163
com.example.uhf:array/arrayMemoryBank = 0x7f030019
com.example.uhf:anim/btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d
com.example.uhf:interpolator/m3_sys_motion_easing_emphasized_accelerate = 0x7f0b0008
com.example.uhf:attr/actionBarSplitStyle = 0x7f040006
com.example.uhf:macro/m3_comp_radio_button_selected_focus_state_layer_color = 0x7f0d00d9
com.example.uhf:dimen/design_navigation_padding_bottom = 0x7f07007e
com.example.uhf:attr/checkboxStyle = 0x7f0400bb
com.example.uhf:id/bounceEnd = 0x7f090088
com.example.uhf:color/m3_ref_palette_dynamic_neutral60 = 0x7f0600c7
