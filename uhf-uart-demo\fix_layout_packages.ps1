# PowerShell script to fix package names in layout files

Write-Host "Fixing package names in layout files..."

# Get all XML layout files
$layoutFiles = Get-ChildItem -Path "app\src\main\res\layout" -Filter "*.xml"

$totalFiles = $layoutFiles.Count
$currentFile = 0

foreach ($file in $layoutFiles) {
    $currentFile++
    Write-Host "[$currentFile/$totalFiles] Processing: $($file.Name)"
    
    try {
        # Read file content
        $content = Get-Content $file.FullName -Raw -ErrorAction Stop
        
        if ($content) {
            # Track if any changes were made
            $originalContent = $content
            
            # Update package references in layout files
            $content = $content -replace "com\.example\.uhf\.view\.", "com.maspects.uhftool.view."
            
            # Only write if changes were made
            if ($content -ne $originalContent) {
                Set-Content -Path $file.FullName -Value $content -NoNewline -ErrorAction Stop
                Write-Host "  Updated package references"
            } else {
                Write-Host "  No package references found"
            }
        }
    }
    catch {
        Write-Host "  Error processing file: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`nLayout package fixes completed!" -ForegroundColor Green
