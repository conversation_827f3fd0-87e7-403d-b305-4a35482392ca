// Generated by view binder compiler. Do not edit!
package com.example.uhf.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.ScrollView;
import android.widget.Spinner;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.uhf.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class UhfLockFragmentBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final EditText EtAccessPwdLock;

  @NonNull
  public final Button btnGBLock;

  @NonNull
  public final Button btnLock;

  @NonNull
  public final CheckBox cbFilterLock;

  @NonNull
  public final EditText etDataFilterLock;

  @NonNull
  public final EditText etGBAccessPwd;

  @NonNull
  public final EditText etLenFilterLock;

  @NonNull
  public final EditText etLockCode;

  @NonNull
  public final EditText etPtrFilterLock;

  @NonNull
  public final LinearLayout layoutUserAreaNumber;

  @NonNull
  public final RadioButton rbEPCFilterLock;

  @NonNull
  public final RadioButton rbTIDFilterLock;

  @NonNull
  public final RadioButton rbUserFilterLock;

  @NonNull
  public final Spinner spGBAction;

  @NonNull
  public final Spinner spGBConfig;

  @NonNull
  public final Spinner spGBStorageArea;

  @NonNull
  public final Spinner spGBUserAreaNumber;

  private UhfLockFragmentBinding(@NonNull ScrollView rootView, @NonNull EditText EtAccessPwdLock,
      @NonNull Button btnGBLock, @NonNull Button btnLock, @NonNull CheckBox cbFilterLock,
      @NonNull EditText etDataFilterLock, @NonNull EditText etGBAccessPwd,
      @NonNull EditText etLenFilterLock, @NonNull EditText etLockCode,
      @NonNull EditText etPtrFilterLock, @NonNull LinearLayout layoutUserAreaNumber,
      @NonNull RadioButton rbEPCFilterLock, @NonNull RadioButton rbTIDFilterLock,
      @NonNull RadioButton rbUserFilterLock, @NonNull Spinner spGBAction,
      @NonNull Spinner spGBConfig, @NonNull Spinner spGBStorageArea,
      @NonNull Spinner spGBUserAreaNumber) {
    this.rootView = rootView;
    this.EtAccessPwdLock = EtAccessPwdLock;
    this.btnGBLock = btnGBLock;
    this.btnLock = btnLock;
    this.cbFilterLock = cbFilterLock;
    this.etDataFilterLock = etDataFilterLock;
    this.etGBAccessPwd = etGBAccessPwd;
    this.etLenFilterLock = etLenFilterLock;
    this.etLockCode = etLockCode;
    this.etPtrFilterLock = etPtrFilterLock;
    this.layoutUserAreaNumber = layoutUserAreaNumber;
    this.rbEPCFilterLock = rbEPCFilterLock;
    this.rbTIDFilterLock = rbTIDFilterLock;
    this.rbUserFilterLock = rbUserFilterLock;
    this.spGBAction = spGBAction;
    this.spGBConfig = spGBConfig;
    this.spGBStorageArea = spGBStorageArea;
    this.spGBUserAreaNumber = spGBUserAreaNumber;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static UhfLockFragmentBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static UhfLockFragmentBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.uhf_lock_fragment, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static UhfLockFragmentBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.EtAccessPwd_Lock;
      EditText EtAccessPwdLock = ViewBindings.findChildViewById(rootView, id);
      if (EtAccessPwdLock == null) {
        break missingId;
      }

      id = R.id.btnGBLock;
      Button btnGBLock = ViewBindings.findChildViewById(rootView, id);
      if (btnGBLock == null) {
        break missingId;
      }

      id = R.id.btnLock;
      Button btnLock = ViewBindings.findChildViewById(rootView, id);
      if (btnLock == null) {
        break missingId;
      }

      id = R.id.cb_filter_lock;
      CheckBox cbFilterLock = ViewBindings.findChildViewById(rootView, id);
      if (cbFilterLock == null) {
        break missingId;
      }

      id = R.id.etData_filter_lock;
      EditText etDataFilterLock = ViewBindings.findChildViewById(rootView, id);
      if (etDataFilterLock == null) {
        break missingId;
      }

      id = R.id.etGBAccessPwd;
      EditText etGBAccessPwd = ViewBindings.findChildViewById(rootView, id);
      if (etGBAccessPwd == null) {
        break missingId;
      }

      id = R.id.etLen_filter_lock;
      EditText etLenFilterLock = ViewBindings.findChildViewById(rootView, id);
      if (etLenFilterLock == null) {
        break missingId;
      }

      id = R.id.etLockCode;
      EditText etLockCode = ViewBindings.findChildViewById(rootView, id);
      if (etLockCode == null) {
        break missingId;
      }

      id = R.id.etPtr_filter_lock;
      EditText etPtrFilterLock = ViewBindings.findChildViewById(rootView, id);
      if (etPtrFilterLock == null) {
        break missingId;
      }

      id = R.id.layoutUserAreaNumber;
      LinearLayout layoutUserAreaNumber = ViewBindings.findChildViewById(rootView, id);
      if (layoutUserAreaNumber == null) {
        break missingId;
      }

      id = R.id.rbEPC_filter_lock;
      RadioButton rbEPCFilterLock = ViewBindings.findChildViewById(rootView, id);
      if (rbEPCFilterLock == null) {
        break missingId;
      }

      id = R.id.rbTID_filter_lock;
      RadioButton rbTIDFilterLock = ViewBindings.findChildViewById(rootView, id);
      if (rbTIDFilterLock == null) {
        break missingId;
      }

      id = R.id.rbUser_filter_lock;
      RadioButton rbUserFilterLock = ViewBindings.findChildViewById(rootView, id);
      if (rbUserFilterLock == null) {
        break missingId;
      }

      id = R.id.spGBAction;
      Spinner spGBAction = ViewBindings.findChildViewById(rootView, id);
      if (spGBAction == null) {
        break missingId;
      }

      id = R.id.spGBConfig;
      Spinner spGBConfig = ViewBindings.findChildViewById(rootView, id);
      if (spGBConfig == null) {
        break missingId;
      }

      id = R.id.spGBStorageArea;
      Spinner spGBStorageArea = ViewBindings.findChildViewById(rootView, id);
      if (spGBStorageArea == null) {
        break missingId;
      }

      id = R.id.spGBUserAreaNumber;
      Spinner spGBUserAreaNumber = ViewBindings.findChildViewById(rootView, id);
      if (spGBUserAreaNumber == null) {
        break missingId;
      }

      return new UhfLockFragmentBinding((ScrollView) rootView, EtAccessPwdLock, btnGBLock, btnLock,
          cbFilterLock, etDataFilterLock, etGBAccessPwd, etLenFilterLock, etLockCode,
          etPtrFilterLock, layoutUserAreaNumber, rbEPCFilterLock, rbTIDFilterLock, rbUserFilterLock,
          spGBAction, spGBConfig, spGBStorageArea, spGBUserAreaNumber);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
