// Generated by view binder compiler. Do not edit!
package com.example.uhf.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.uhf.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class UhfDialogFrequencyBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView ivDismissDialog;

  @NonNull
  public final ListView listViewFrequency;

  private UhfDialogFrequencyBinding(@NonNull LinearLayout rootView,
      @NonNull ImageView ivDismissDialog, @NonNull ListView listViewFrequency) {
    this.rootView = rootView;
    this.ivDismissDialog = ivDismissDialog;
    this.listViewFrequency = listViewFrequency;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static UhfDialogFrequencyBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static UhfDialogFrequencyBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.uhf_dialog_frequency, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static UhfDialogFrequencyBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.iv_dismissDialog;
      ImageView ivDismissDialog = ViewBindings.findChildViewById(rootView, id);
      if (ivDismissDialog == null) {
        break missingId;
      }

      id = R.id.listView_frequency;
      ListView listViewFrequency = ViewBindings.findChildViewById(rootView, id);
      if (listViewFrequency == null) {
        break missingId;
      }

      return new UhfDialogFrequencyBinding((LinearLayout) rootView, ivDismissDialog,
          listViewFrequency);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
