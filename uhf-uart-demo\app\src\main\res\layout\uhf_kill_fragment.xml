<?xml version="1.0" encoding="UTF-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:scrollbars="vertical">

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <CheckBox
            android:id="@+id/cb_filter"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#cccccc"
            android:text="@string/uhf_title_filter"
            android:textSize="19sp" />

        <LinearLayout
            android:id="@+id/llFilter"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/rectangle_bg"
            android:orientation="vertical"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/tvPtr" />

                <EditText
                    android:id="@+id/etPtr_filter"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:inputType="numberSigned"
                    android:text="32" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="5dp"
                    android:text="(bit)" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/tvLen" />

                <EditText
                    android:id="@+id/etLen_filter"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:inputType="numberSigned"
                    android:text="96" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="(bit)" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/tvData_Read" />

                <EditText
                    android:id="@+id/etData_filter"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="hexadecimal data"
                    android:inputType="textNoSuggestions"
                    android:singleLine="true" />
            </LinearLayout>

            <RadioGroup
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <RadioButton
                    android:id="@+id/rbEPC_filter"
                    android:layout_width="0dp"
                    android:layout_height="30dp"
                    android:layout_weight="1"
                    android:background="@drawable/rb_bg"
                    android:button="@null"
                    android:checked="true"
                    android:gravity="center"
                    android:text="EPC" />

                <RadioButton
                    android:id="@+id/rbTID_filter"
                    android:layout_width="0dp"
                    android:layout_height="30dp"
                    android:layout_marginLeft="10dp"
                    android:layout_weight="1"
                    android:background="@drawable/rb_bg"
                    android:button="@null"
                    android:checked="false"
                    android:gravity="center"
                    android:text="TID" />

                <RadioButton
                    android:id="@+id/rbUser_filter"
                    android:layout_width="0dp"
                    android:layout_height="30dp"
                    android:layout_marginLeft="10dp"
                    android:layout_weight="1"
                    android:background="@drawable/rb_bg"
                    android:button="@null"
                    android:checked="false"
                    android:gravity="center"
                    android:text="USER" />
            </RadioGroup>
        </LinearLayout>

        <!--<RelativeLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content">

            <CheckBox
                android:id="@+id/CkWithUii_Write"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/ckWithUii" />

            <Button
                android:id="@+id/BtUii_Write"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:text="@string/btUii"
                android:textSize="@dimen/text_size_14"
                android:visibility="gone" />
        </RelativeLayout>

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tvTagUii_Write" />

            <EditText
                android:id="@+id/EtTagUii_Write"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:hint="" />
        </LinearLayout>-->

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:padding="4dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tvAccessPwd" />

            <EditText
                android:id="@+id/EtAccessPwd_Write"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:hint="@string/rfid_mgs_killpwdtip" />
        </LinearLayout>

        <Button
            android:id="@+id/btnKill"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:text="@string/uhf_msg_tab_kill"
            android:background="@drawable/button_bg"
            android:textColor="@color/white"
            android:layout_marginLeft="4dp"
            android:layout_marginRight="4dp"/>

    </LinearLayout>
</ScrollView>