<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:id="@android:id/background">
        <shape>
            <corners android:radius="10dp"/>
            <solid android:color="#DBEDF4"/>
        </shape>
        <!--    背景颜色-->
        <color android:color="#CCCCCC"/>
    </item>

    <item android:id="@android:id/progress">
        <clip
            android:clipOrientation="horizontal"
            android:gravity="left">
            <shape>
                <corners android:radius="10dp"/>
                <!--  开始颜色，中途颜色，最后颜色-->
                <gradient
                    android:startColor="#FF0000"
                    android:centerColor="#FFFF00"
                    android:endColor="#00FF00"/>
            </shape>
        </clip>
    </item>
</layer-list>