<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="uhf_dialog_lock_code" modulePackage="com.example.uhf" filePath="app\src\main\res\layout\uhf_dialog_lock_code.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/uhf_dialog_lock_code_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="67" endOffset="14"/></Target><Target id="@+id/rgFileType" view="RadioGroup"><Expressions/><location startLine="8" startOffset="4" endLine="31" endOffset="16"/></Target><Target id="@+id/rbOpen" view="RadioButton"><Expressions/><location startLine="15" startOffset="8" endLine="20" endOffset="51"/></Target><Target id="@+id/rbLock" view="RadioButton"><Expressions/><location startLine="21" startOffset="8" endLine="25" endOffset="51"/></Target><Target id="@+id/cbPerm" view="CheckBox"><Expressions/><location startLine="26" startOffset="8" endLine="30" endOffset="51"/></Target><Target id="@+id/cbKill" view="CheckBox"><Expressions/><location startLine="40" startOffset="8" endLine="44" endOffset="38"/></Target><Target id="@+id/cbAccess" view="CheckBox"><Expressions/><location startLine="45" startOffset="8" endLine="49" endOffset="40"/></Target><Target id="@+id/cbEPC" view="CheckBox"><Expressions/><location startLine="50" startOffset="8" endLine="54" endOffset="37"/></Target><Target id="@+id/cbTid" view="CheckBox"><Expressions/><location startLine="55" startOffset="8" endLine="59" endOffset="37"/></Target><Target id="@+id/cbUser" view="CheckBox"><Expressions/><location startLine="60" startOffset="8" endLine="64" endOffset="38"/></Target></Targets></Layout>