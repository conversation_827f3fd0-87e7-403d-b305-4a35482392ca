<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_deactivate" modulePackage="com.example.uhf" filePath="app\src\main\res\layout\fragment_deactivate.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_deactivate_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="196" endOffset="12"/></Target><Target id="@+id/cb_filter_deact" view="CheckBox"><Expressions/><location startLine="22" startOffset="12" endLine="29" endOffset="17"/></Target><Target id="@+id/etPtr_filter_deact" view="EditText"><Expressions/><location startLine="40" startOffset="16" endLine="46" endOffset="39"/></Target><Target id="@+id/etLen_filter_deact" view="EditText"><Expressions/><location startLine="57" startOffset="16" endLine="63" endOffset="38"/></Target><Target id="@+id/etData_filter_deact" view="EditText"><Expressions/><location startLine="80" startOffset="16" endLine="86" endOffset="47"/></Target><Target id="@+id/rbEPC_filter_deact" view="RadioButton"><Expressions/><location startLine="95" startOffset="16" endLine="105" endOffset="68"/></Target><Target id="@+id/rbTID_filter_deact" view="RadioButton"><Expressions/><location startLine="107" startOffset="16" endLine="118" endOffset="68"/></Target><Target id="@+id/rbUser_filter_deact" view="RadioButton"><Expressions/><location startLine="119" startOffset="16" endLine="130" endOffset="68"/></Target><Target id="@+id/EtAccessPwd_deactivate" view="EditText"><Expressions/><location startLine="150" startOffset="12" endLine="155" endOffset="41"/></Target><Target id="@+id/Etcmd" view="EditText"><Expressions/><location startLine="170" startOffset="12" endLine="176" endOffset="41"/></Target><Target id="@+id/btn_deactivate" view="Button"><Expressions/><location startLine="183" startOffset="12" endLine="191" endOffset="51"/></Target></Targets></Layout>