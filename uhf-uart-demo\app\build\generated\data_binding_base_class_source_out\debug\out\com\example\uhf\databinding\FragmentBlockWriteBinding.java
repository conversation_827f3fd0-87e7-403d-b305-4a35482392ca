// Generated by view binder compiler. Do not edit!
package com.example.uhf.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.RadioButton;
import android.widget.ScrollView;
import android.widget.Spinner;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.uhf.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentBlockWriteBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button BtWrite;

  @NonNull
  public final EditText EtAccessPwdWrite;

  @NonNull
  public final EditText EtDataWrite;

  @NonNull
  public final EditText EtLenWrite;

  @NonNull
  public final EditText EtPtrWrite;

  @NonNull
  public final Spinner SpinnerBankWrite;

  @NonNull
  public final CheckBox cbFilterWt;

  @NonNull
  public final EditText etDataFilterWt;

  @NonNull
  public final EditText etLenFilterWt;

  @NonNull
  public final EditText etPtrFilterWt;

  @NonNull
  public final RadioButton rbEPCFilterWt;

  @NonNull
  public final RadioButton rbTIDFilterWt;

  @NonNull
  public final RadioButton rbUserFilterWt;

  private FragmentBlockWriteBinding(@NonNull ScrollView rootView, @NonNull Button BtWrite,
      @NonNull EditText EtAccessPwdWrite, @NonNull EditText EtDataWrite,
      @NonNull EditText EtLenWrite, @NonNull EditText EtPtrWrite, @NonNull Spinner SpinnerBankWrite,
      @NonNull CheckBox cbFilterWt, @NonNull EditText etDataFilterWt,
      @NonNull EditText etLenFilterWt, @NonNull EditText etPtrFilterWt,
      @NonNull RadioButton rbEPCFilterWt, @NonNull RadioButton rbTIDFilterWt,
      @NonNull RadioButton rbUserFilterWt) {
    this.rootView = rootView;
    this.BtWrite = BtWrite;
    this.EtAccessPwdWrite = EtAccessPwdWrite;
    this.EtDataWrite = EtDataWrite;
    this.EtLenWrite = EtLenWrite;
    this.EtPtrWrite = EtPtrWrite;
    this.SpinnerBankWrite = SpinnerBankWrite;
    this.cbFilterWt = cbFilterWt;
    this.etDataFilterWt = etDataFilterWt;
    this.etLenFilterWt = etLenFilterWt;
    this.etPtrFilterWt = etPtrFilterWt;
    this.rbEPCFilterWt = rbEPCFilterWt;
    this.rbTIDFilterWt = rbTIDFilterWt;
    this.rbUserFilterWt = rbUserFilterWt;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentBlockWriteBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentBlockWriteBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_block_write, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentBlockWriteBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.BtWrite;
      Button BtWrite = ViewBindings.findChildViewById(rootView, id);
      if (BtWrite == null) {
        break missingId;
      }

      id = R.id.EtAccessPwd_Write;
      EditText EtAccessPwdWrite = ViewBindings.findChildViewById(rootView, id);
      if (EtAccessPwdWrite == null) {
        break missingId;
      }

      id = R.id.EtData_Write;
      EditText EtDataWrite = ViewBindings.findChildViewById(rootView, id);
      if (EtDataWrite == null) {
        break missingId;
      }

      id = R.id.EtLen_Write;
      EditText EtLenWrite = ViewBindings.findChildViewById(rootView, id);
      if (EtLenWrite == null) {
        break missingId;
      }

      id = R.id.EtPtr_Write;
      EditText EtPtrWrite = ViewBindings.findChildViewById(rootView, id);
      if (EtPtrWrite == null) {
        break missingId;
      }

      id = R.id.SpinnerBank_Write;
      Spinner SpinnerBankWrite = ViewBindings.findChildViewById(rootView, id);
      if (SpinnerBankWrite == null) {
        break missingId;
      }

      id = R.id.cb_filter_wt;
      CheckBox cbFilterWt = ViewBindings.findChildViewById(rootView, id);
      if (cbFilterWt == null) {
        break missingId;
      }

      id = R.id.etData_filter_wt;
      EditText etDataFilterWt = ViewBindings.findChildViewById(rootView, id);
      if (etDataFilterWt == null) {
        break missingId;
      }

      id = R.id.etLen_filter_wt;
      EditText etLenFilterWt = ViewBindings.findChildViewById(rootView, id);
      if (etLenFilterWt == null) {
        break missingId;
      }

      id = R.id.etPtr_filter_wt;
      EditText etPtrFilterWt = ViewBindings.findChildViewById(rootView, id);
      if (etPtrFilterWt == null) {
        break missingId;
      }

      id = R.id.rbEPC_filter_wt;
      RadioButton rbEPCFilterWt = ViewBindings.findChildViewById(rootView, id);
      if (rbEPCFilterWt == null) {
        break missingId;
      }

      id = R.id.rbTID_filter_wt;
      RadioButton rbTIDFilterWt = ViewBindings.findChildViewById(rootView, id);
      if (rbTIDFilterWt == null) {
        break missingId;
      }

      id = R.id.rbUser_filter_wt;
      RadioButton rbUserFilterWt = ViewBindings.findChildViewById(rootView, id);
      if (rbUserFilterWt == null) {
        break missingId;
      }

      return new FragmentBlockWriteBinding((ScrollView) rootView, BtWrite, EtAccessPwdWrite,
          EtDataWrite, EtLenWrite, EtPtrWrite, SpinnerBankWrite, cbFilterWt, etDataFilterWt,
          etLenFilterWt, etPtrFilterWt, rbEPCFilterWt, rbTIDFilterWt, rbUserFilterWt);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
