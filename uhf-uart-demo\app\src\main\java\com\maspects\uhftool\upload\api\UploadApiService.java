package com.maspects.uhftool.upload.api;

import com.maspects.uhftool.upload.models.UploadRequest;
import com.maspects.uhftool.upload.models.UploadResponse;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.Header;
import retrofit2.http.POST;

/**
 * Retrofit API interface for uploading RFID scan data
 * Defines the HTTP endpoints for communicating with the server
 */
public interface UploadApiService {
    
    /**
     * Upload scanned RFID tag data to the server
     * @param authorization Authorization header (Bearer token, API key, etc.)
     * @param uploadRequest The data to upload
     * @return Server response with upload status
     */
    @POST("api/rfid/upload")
    Call<UploadResponse> uploadScanData(
        @Header("Authorization") String authorization,
        @Body UploadRequest uploadRequest
    );
    
    /**
     * Test connection to the server
     * @param authorization Authorization header
     * @return Simple response to verify connectivity
     */
    @GET("api/rfid/ping")
    Call<UploadResponse> testConnection(
        @Header("Authorization") String authorization
    );
    
    /**
     * Upload scanned data with custom endpoint
     * Allows for flexible endpoint configuration
     * @param endpoint Custom endpoint path
     * @param authorization Authorization header
     * @param uploadRequest The data to upload
     * @return Server response with upload status
     */
    @POST
    Call<UploadResponse> uploadToCustomEndpoint(
        @retrofit2.http.Url String endpoint,
        @Header("Authorization") String authorization,
        @Body UploadRequest uploadRequest
    );
}
