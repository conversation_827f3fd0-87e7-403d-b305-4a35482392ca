<?xml version="1.0" encoding="utf-8"?>
 <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:orientation="vertical"
        tools:context=".activity.TestActivity">

        <LinearLayout
            android:id="@+id/layout_filter"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/rectangle_bg"
            android:orientation="horizontal"
            android:paddingLeft="8dp"
            android:paddingRight="8dp">

            <TextView
                android:layout_gravity="center"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingLeft="0dp"
                android:paddingRight="6dp"
                android:text="Time:" />

            <TextView
                android:layout_gravity="center"
                android:id="@+id/tvTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingLeft="0dp"
                android:text="0" />

            <TextView
                android:layout_gravity="center"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingLeft="70dp"
                android:paddingRight="6dp"
                android:text="Speed:" />

            <TextView
                android:layout_gravity="center"
                android:id="@+id/tvSpeed"
                android:textSize="40sp"
                android:textColor="@color/blue"
                android:textStyle="bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingLeft="0dp"
                android:text="0" />

            <TextView
                android:layout_gravity="center"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingLeft="10dp"
                android:paddingRight="6dp"
                android:text="tags/s" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/layout4"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingBottom="5dp"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:paddingTop="5dp">

            <TextView
                android:layout_width="0dip"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/tvTagUii"
                android:textSize="15sp" />

            <TextView
                android:id="@+id/tv_count"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:text="0"
                android:textColor="@color/red1"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_total"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:text="0"
                android:textColor="@color/red1"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dip"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/tvTagLen"
                android:visibility="gone" />

            <TextView
                android:layout_width="0dip"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/tvTagCount"
                android:textSize="15sp" />

            <TextView
                android:layout_width="0dip"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="RSSI"
                android:textSize="15sp" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="@color/gray1" />

        <ListView
            android:id="@+id/LvTags"
            android:layout_width="fill_parent"
            android:layout_height="0dp"
            android:layout_weight="1"/>

    <LinearLayout
        android:visibility="gone"
        android:id="@+id/layout12"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="5dp">

        <Button
            android:id="@+id/BtInventory"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5dp"
            android:layout_marginRight="5dp"
            android:layout_weight="1"
            android:background="@drawable/button_bg"
            android:text="@string/btInventory"
            android:textColor="@color/white" />

        <Button
            android:id="@+id/BtClear"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="5dp"
            android:background="@drawable/button_bg"
            android:text="@string/btClear"
            android:textColor="@color/white"
            android:textSize="15sp" />
    </LinearLayout>


</LinearLayout>