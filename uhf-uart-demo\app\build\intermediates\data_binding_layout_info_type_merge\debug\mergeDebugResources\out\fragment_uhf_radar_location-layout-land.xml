<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_uhf_radar_location" modulePackage="com.example.uhf" filePath="app\src\main\res\layout-land\fragment_uhf_radar_location.xml" directory="layout-land" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout-land/fragment_uhf_radar_location_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="118" endOffset="51"/></Target><Target id="@+id/epcLayout" view="LinearLayout"><Expressions/><location startLine="10" startOffset="4" endLine="29" endOffset="18"/></Target><Target id="@+id/etRadarEPC" view="EditText"><Expressions/><location startLine="25" startOffset="8" endLine="28" endOffset="50"/></Target><Target id="@+id/radarView" view="com.example.uhf.view.RadarView"><Expressions/><location startLine="32" startOffset="4" endLine="41" endOffset="51"/></Target><Target id="@+id/seekBarPower" view="com.example.uhf.view.CircleSeekBar"><Expressions/><location startLine="59" startOffset="8" endLine="69" endOffset="60"/></Target><Target id="@+id/tvSearchRange" view="TextView"><Expressions/><location startLine="80" startOffset="4" endLine="91" endOffset="62"/></Target><Target id="@+id/llButton" view="LinearLayout"><Expressions/><location startLine="93" startOffset="4" endLine="117" endOffset="18"/></Target><Target id="@+id/btRadarStart" view="Button"><Expressions/><location startLine="104" startOffset="8" endLine="109" endOffset="51"/></Target><Target id="@+id/btRadarStop" view="Button"><Expressions/><location startLine="111" startOffset="8" endLine="116" endOffset="50"/></Target></Targets></Layout>