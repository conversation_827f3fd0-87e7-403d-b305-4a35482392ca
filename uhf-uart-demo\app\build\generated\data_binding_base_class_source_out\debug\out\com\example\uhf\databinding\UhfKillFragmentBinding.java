// Generated by view binder compiler. Do not edit!
package com.example.uhf.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.ScrollView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.uhf.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class UhfKillFragmentBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final EditText EtAccessPwdWrite;

  @NonNull
  public final Button btnKill;

  @NonNull
  public final CheckBox cbFilter;

  @NonNull
  public final EditText etDataFilter;

  @NonNull
  public final EditText etLenFilter;

  @NonNull
  public final EditText etPtrFilter;

  @NonNull
  public final LinearLayout llFilter;

  @NonNull
  public final RadioButton rbEPCFilter;

  @NonNull
  public final RadioButton rbTIDFilter;

  @NonNull
  public final RadioButton rbUserFilter;

  private UhfKillFragmentBinding(@NonNull ScrollView rootView, @NonNull EditText EtAccessPwdWrite,
      @NonNull Button btnKill, @NonNull CheckBox cbFilter, @NonNull EditText etDataFilter,
      @NonNull EditText etLenFilter, @NonNull EditText etPtrFilter, @NonNull LinearLayout llFilter,
      @NonNull RadioButton rbEPCFilter, @NonNull RadioButton rbTIDFilter,
      @NonNull RadioButton rbUserFilter) {
    this.rootView = rootView;
    this.EtAccessPwdWrite = EtAccessPwdWrite;
    this.btnKill = btnKill;
    this.cbFilter = cbFilter;
    this.etDataFilter = etDataFilter;
    this.etLenFilter = etLenFilter;
    this.etPtrFilter = etPtrFilter;
    this.llFilter = llFilter;
    this.rbEPCFilter = rbEPCFilter;
    this.rbTIDFilter = rbTIDFilter;
    this.rbUserFilter = rbUserFilter;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static UhfKillFragmentBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static UhfKillFragmentBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.uhf_kill_fragment, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static UhfKillFragmentBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.EtAccessPwd_Write;
      EditText EtAccessPwdWrite = ViewBindings.findChildViewById(rootView, id);
      if (EtAccessPwdWrite == null) {
        break missingId;
      }

      id = R.id.btnKill;
      Button btnKill = ViewBindings.findChildViewById(rootView, id);
      if (btnKill == null) {
        break missingId;
      }

      id = R.id.cb_filter;
      CheckBox cbFilter = ViewBindings.findChildViewById(rootView, id);
      if (cbFilter == null) {
        break missingId;
      }

      id = R.id.etData_filter;
      EditText etDataFilter = ViewBindings.findChildViewById(rootView, id);
      if (etDataFilter == null) {
        break missingId;
      }

      id = R.id.etLen_filter;
      EditText etLenFilter = ViewBindings.findChildViewById(rootView, id);
      if (etLenFilter == null) {
        break missingId;
      }

      id = R.id.etPtr_filter;
      EditText etPtrFilter = ViewBindings.findChildViewById(rootView, id);
      if (etPtrFilter == null) {
        break missingId;
      }

      id = R.id.llFilter;
      LinearLayout llFilter = ViewBindings.findChildViewById(rootView, id);
      if (llFilter == null) {
        break missingId;
      }

      id = R.id.rbEPC_filter;
      RadioButton rbEPCFilter = ViewBindings.findChildViewById(rootView, id);
      if (rbEPCFilter == null) {
        break missingId;
      }

      id = R.id.rbTID_filter;
      RadioButton rbTIDFilter = ViewBindings.findChildViewById(rootView, id);
      if (rbTIDFilter == null) {
        break missingId;
      }

      id = R.id.rbUser_filter;
      RadioButton rbUserFilter = ViewBindings.findChildViewById(rootView, id);
      if (rbUserFilter == null) {
        break missingId;
      }

      return new UhfKillFragmentBinding((ScrollView) rootView, EtAccessPwdWrite, btnKill, cbFilter,
          etDataFilter, etLenFilter, etPtrFilter, llFilter, rbEPCFilter, rbTIDFilter, rbUserFilter);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
