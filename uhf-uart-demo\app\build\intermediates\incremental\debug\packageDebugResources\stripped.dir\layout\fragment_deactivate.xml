<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:scrollbars="vertical" >

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="5dp" >
        <TextView
            android:background="#AAAAAA"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/uhf_title_filter" />
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/rectangle_bg"
            android:orientation="vertical"
            android:padding="8dp">
            <CheckBox
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:id="@+id/cb_filter_deact"
                android:text="@string/button_enable"
                android:textSize="19sp"
                android:textColor="@drawable/check_text_color"
                />
            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                >

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/tvPtr" />

                <EditText
                    android:id="@+id/etPtr_filter_deact"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:inputType="numberSigned"
                    android:text="32" />
                <TextView
                    android:layout_marginRight="30dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="(bit)" />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="长度:" />

                <EditText
                    android:id="@+id/etLen_filter_deact"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:inputType="numberSigned"
                    android:text="0" />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="(bit)" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                >

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/tvData_Read" />

                <EditText
                    android:id="@+id/etData_filter_deact"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:hint=""
                    android:inputType="textNoSuggestions"
                    android:singleLine="true" />
            </LinearLayout>

            <RadioGroup
                android:layout_width="match_parent"
                android:layout_height="wrap_content"

                android:orientation="horizontal">

                <RadioButton
                    android:id="@+id/rbEPC_filter_deact"
                    android:layout_width="0dp"
                    android:layout_height="30dp"
                    android:layout_weight="1"
                    android:background="@drawable/rb_bg"
                    android:button="@null"
                    android:checked="true"
                    android:gravity="center"
                    android:text="EPC"
                    android:textColor="@drawable/check_text_color" />

                <RadioButton
                    android:id="@+id/rbTID_filter_deact"
                    android:layout_width="0dp"
                    android:layout_height="30dp"
                    android:layout_marginLeft="10dp"
                    android:layout_weight="1"
                    android:background="@drawable/rb_bg"
                    android:button="@null"
                    android:checked="false"
                    android:gravity="center"
                    android:text="TID"
                    android:textColor="@drawable/check_text_color" />
                <RadioButton
                    android:id="@+id/rbUser_filter_deact"
                    android:layout_width="0dp"
                    android:layout_height="30dp"
                    android:layout_marginLeft="10dp"
                    android:layout_weight="1"
                    android:background="@drawable/rb_bg"
                    android:button="@null"
                    android:checked="false"
                    android:gravity="center"
                    android:text="USER"
                    android:textColor="@drawable/check_text_color" />

            </RadioGroup>


        </LinearLayout>


        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:padding="5dp" >

            <TextView
                android:layout_width="100dp"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="Kill Pwd:"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/EtAccessPwd_deactivate"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:hint="00000000"
                android:textSize="15sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:padding="5dp" >

            <TextView
                android:layout_width="100dp"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="命令"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/Etcmd"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:hint=""
                android:text="E003"
                android:textSize="15sp" />
        </LinearLayout>
        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:paddingTop="10dp" >

            <Button
                android:id="@+id/btn_deactivate"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:text="Deactivate /re-activation"
                android:background="@drawable/button_bg"
                android:textColor="@color/white"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp" />
        </LinearLayout>

    </LinearLayout>

</ScrollView>