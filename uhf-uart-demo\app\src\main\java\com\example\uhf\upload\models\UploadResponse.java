package com.example.uhf.upload.models;

import com.google.gson.annotations.SerializedName;

/**
 * Response model for upload API calls
 * Contains server response information including success status and messages
 */
public class UploadResponse {
    
    @SerializedName("success")
    private boolean success;
    
    @SerializedName("message")
    private String message;
    
    @SerializedName("uploadId")
    private String uploadId;
    
    @SerializedName("timestamp")
    private String timestamp;
    
    @SerializedName("processedTagCount")
    private int processedTagCount;
    
    @SerializedName("errorCode")
    private String errorCode;
    
    @SerializedName("errorDetails")
    private String errorDetails;
    
    // Default constructor
    public UploadResponse() {}
    
    // Constructor for successful response
    public UploadResponse(boolean success, String message, String uploadId, 
                         String timestamp, int processedTagCount) {
        this.success = success;
        this.message = message;
        this.uploadId = uploadId;
        this.timestamp = timestamp;
        this.processedTagCount = processedTagCount;
    }
    
    // Constructor for error response
    public UploadResponse(boolean success, String message, String errorCode, String errorDetails) {
        this.success = success;
        this.message = message;
        this.errorCode = errorCode;
        this.errorDetails = errorDetails;
    }
    
    // Getters and Setters
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public String getUploadId() {
        return uploadId;
    }
    
    public void setUploadId(String uploadId) {
        this.uploadId = uploadId;
    }
    
    public String getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }
    
    public int getProcessedTagCount() {
        return processedTagCount;
    }
    
    public void setProcessedTagCount(int processedTagCount) {
        this.processedTagCount = processedTagCount;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }
    
    public String getErrorDetails() {
        return errorDetails;
    }
    
    public void setErrorDetails(String errorDetails) {
        this.errorDetails = errorDetails;
    }
    
    @Override
    public String toString() {
        return "UploadResponse{" +
                "success=" + success +
                ", message='" + message + '\'' +
                ", uploadId='" + uploadId + '\'' +
                ", timestamp='" + timestamp + '\'' +
                ", processedTagCount=" + processedTagCount +
                ", errorCode='" + errorCode + '\'' +
                ", errorDetails='" + errorDetails + '\'' +
                '}';
    }
}
