{"logs": [{"outputFile": "com.example.uhf.app-mergeDebugResources-2:/values-night-v8/values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8a82ccfd0a18eaa7ab7c918e5f6cc0f6\\transformed\\appcompat-1.7.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "6,7,8,9,10,11,12,39", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "174,244,328,412,508,610,712,3694", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "239,323,407,503,605,707,801,3778"}}, {"source": "C:\\Maspects\\ChainWayC72\\uhf-uart-demo\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "100", "endLines": "5", "endColumns": "12", "endOffsets": "318"}, "to": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "169"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aef17b985e4c925ecf4549f65f319b6b\\transformed\\material-1.12.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,241,330,431,538,645,744,851,954,1081,1169,1293,1395,1497,1613,1715,1829,1957,2073,2195,2331,2451,2585,2705,2817,2943,3060,3184,3314,3436,3574,3708,3824", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "125,236,325,426,533,640,739,846,949,1076,1164,1288,1390,1492,1608,1710,1824,1952,2068,2190,2326,2446,2580,2700,2812,2938,3055,3179,3309,3431,3569,3703,3819,3939"}, "to": {"startLines": "13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "806,881,992,1081,1182,1289,1396,1495,1602,1705,1832,1920,2044,2146,2248,2364,2466,2580,2708,2824,2946,3082,3202,3336,3456,3568,3783,3900,4024,4154,4276,4414,4548,4664", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "876,987,1076,1177,1284,1391,1490,1597,1700,1827,1915,2039,2141,2243,2359,2461,2575,2703,2819,2941,3077,3197,3331,3451,3563,3689,3895,4019,4149,4271,4409,4543,4659,4779"}}]}]}