package com.maspects.uhftool.upload.models;

import com.google.gson.annotations.SerializedName;

/**
 * Data model for individual RFID tag information to be uploaded
 * Represents a single scanned tag with all its properties
 */
public class TagData {
    
    @SerializedName("epc")
    private String epc;
    
    @SerializedName("tid")
    private String tid;
    
    @SerializedName("userData")
    private String userData;
    
    @SerializedName("rssi")
    private String rssi;
    
    @SerializedName("phase")
    private String phase;
    
    @SerializedName("count")
    private int count;
    
    @SerializedName("firstSeen")
    private String firstSeen;
    
    @SerializedName("lastSeen")
    private String lastSeen;
    
    // Default constructor
    public TagData() {}
    
    // Constructor with all fields
    public TagData(String epc, String tid, String userData, String rssi, 
                   String phase, int count, String firstSeen, String lastSeen) {
        this.epc = epc;
        this.tid = tid;
        this.userData = userData;
        this.rssi = rssi;
        this.phase = phase;
        this.count = count;
        this.firstSeen = firstSeen;
        this.lastSeen = lastSeen;
    }
    
    // Getters and Setters
    public String getEpc() {
        return epc;
    }
    
    public void setEpc(String epc) {
        this.epc = epc;
    }
    
    public String getTid() {
        return tid;
    }
    
    public void setTid(String tid) {
        this.tid = tid;
    }
    
    public String getUserData() {
        return userData;
    }
    
    public void setUserData(String userData) {
        this.userData = userData;
    }
    
    public String getRssi() {
        return rssi;
    }
    
    public void setRssi(String rssi) {
        this.rssi = rssi;
    }
    
    public String getPhase() {
        return phase;
    }
    
    public void setPhase(String phase) {
        this.phase = phase;
    }
    
    public int getCount() {
        return count;
    }
    
    public void setCount(int count) {
        this.count = count;
    }
    
    public String getFirstSeen() {
        return firstSeen;
    }
    
    public void setFirstSeen(String firstSeen) {
        this.firstSeen = firstSeen;
    }
    
    public String getLastSeen() {
        return lastSeen;
    }
    
    public void setLastSeen(String lastSeen) {
        this.lastSeen = lastSeen;
    }
    
    @Override
    public String toString() {
        return "TagData{" +
                "epc='" + epc + '\'' +
                ", tid='" + tid + '\'' +
                ", userData='" + userData + '\'' +
                ", rssi='" + rssi + '\'' +
                ", phase='" + phase + '\'' +
                ", count=" + count +
                ", firstSeen='" + firstSeen + '\'' +
                ", lastSeen='" + lastSeen + '\'' +
                '}';
    }
}
