# PowerShell script to fix R class imports

Write-Host "Fixing R class imports..."

# Get all Java files in the project
$javaFiles = Get-ChildItem -Path "app\src\main\java\com\maspects\uhftool" -Recurse -Filter "*.java"

$totalFiles = $javaFiles.Count
$currentFile = 0

foreach ($file in $javaFiles) {
    $currentFile++
    Write-Host "[$currentFile/$totalFiles] Processing: $($file.Name)"
    
    try {
        # Read file content
        $content = Get-Content $file.FullName -Raw -ErrorAction Stop
        
        if ($content) {
            # Track if any changes were made
            $originalContent = $content
            
            # Fix R class imports - remove the full package path
            $content = $content -replace "import com\.maspects\.uhftool\.R;", ""
            
            # Only write if changes were made
            if ($content -ne $originalContent) {
                Set-Content -Path $file.FullName -Value $content -NoNewline -ErrorAction Stop
                Write-Host "  Updated R import"
            } else {
                Write-Host "  No changes needed"
            }
        }
    }
    catch {
        Write-Host "  Error processing file: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`nR import fixes completed!" -ForegroundColor Green
