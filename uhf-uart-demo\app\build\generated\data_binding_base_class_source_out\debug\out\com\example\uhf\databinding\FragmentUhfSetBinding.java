// Generated by view binder compiler. Do not edit!
package com.example.uhf.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.ScrollView;
import android.widget.Spinner;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.uhf.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentUhfSetBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Spinner SpinnerAgreement;

  @NonNull
  public final Button btnFactoryReset;

  @NonNull
  public final Button btnGetFastInventory;

  @NonNull
  public final Button btnGetFrequency;

  @NonNull
  public final Button btnGetLinkParams;

  @NonNull
  public final Button btnGetMemoryBank;

  @NonNull
  public final Button btnGetPower;

  @NonNull
  public final Button btnGetSession;

  @NonNull
  public final Button btnSetFastInventory;

  @NonNull
  public final Button btnSetFreHop;

  @NonNull
  public final Button btnSetFrequency;

  @NonNull
  public final Button btnSetLinkParams;

  @NonNull
  public final Button btnSetMemoryBank;

  @NonNull
  public final Button btnSetPower;

  @NonNull
  public final Button btnSetProtocol;

  @NonNull
  public final Button btnSetSession;

  @NonNull
  public final CheckBox cbFastID;

  @NonNull
  public final CheckBox cbTagFocus;

  @NonNull
  public final EditText etLength;

  @NonNull
  public final EditText etOffset;

  @NonNull
  public final LinearLayout llFreHop;

  @NonNull
  public final LinearLayout llMemoryBankParams;

  @NonNull
  public final LinearLayout llSession;

  @NonNull
  public final RadioButton rbAmerica;

  @NonNull
  public final RadioButton rbChina;

  @NonNull
  public final RadioButton rbEurope;

  @NonNull
  public final RadioButton rbFastInventoryClose;

  @NonNull
  public final RadioButton rbFastInventoryOpen;

  @NonNull
  public final RadioButton rbOthers;

  @NonNull
  public final Spinner spFreHop;

  @NonNull
  public final Spinner spFrequency;

  @NonNull
  public final Spinner spInventoried;

  @NonNull
  public final Spinner spMemoryBank;

  @NonNull
  public final Spinner spPower;

  @NonNull
  public final Spinner spSessionID;

  @NonNull
  public final Spinner splinkParams;

  private FragmentUhfSetBinding(@NonNull ScrollView rootView, @NonNull Spinner SpinnerAgreement,
      @NonNull Button btnFactoryReset, @NonNull Button btnGetFastInventory,
      @NonNull Button btnGetFrequency, @NonNull Button btnGetLinkParams,
      @NonNull Button btnGetMemoryBank, @NonNull Button btnGetPower, @NonNull Button btnGetSession,
      @NonNull Button btnSetFastInventory, @NonNull Button btnSetFreHop,
      @NonNull Button btnSetFrequency, @NonNull Button btnSetLinkParams,
      @NonNull Button btnSetMemoryBank, @NonNull Button btnSetPower, @NonNull Button btnSetProtocol,
      @NonNull Button btnSetSession, @NonNull CheckBox cbFastID, @NonNull CheckBox cbTagFocus,
      @NonNull EditText etLength, @NonNull EditText etOffset, @NonNull LinearLayout llFreHop,
      @NonNull LinearLayout llMemoryBankParams, @NonNull LinearLayout llSession,
      @NonNull RadioButton rbAmerica, @NonNull RadioButton rbChina, @NonNull RadioButton rbEurope,
      @NonNull RadioButton rbFastInventoryClose, @NonNull RadioButton rbFastInventoryOpen,
      @NonNull RadioButton rbOthers, @NonNull Spinner spFreHop, @NonNull Spinner spFrequency,
      @NonNull Spinner spInventoried, @NonNull Spinner spMemoryBank, @NonNull Spinner spPower,
      @NonNull Spinner spSessionID, @NonNull Spinner splinkParams) {
    this.rootView = rootView;
    this.SpinnerAgreement = SpinnerAgreement;
    this.btnFactoryReset = btnFactoryReset;
    this.btnGetFastInventory = btnGetFastInventory;
    this.btnGetFrequency = btnGetFrequency;
    this.btnGetLinkParams = btnGetLinkParams;
    this.btnGetMemoryBank = btnGetMemoryBank;
    this.btnGetPower = btnGetPower;
    this.btnGetSession = btnGetSession;
    this.btnSetFastInventory = btnSetFastInventory;
    this.btnSetFreHop = btnSetFreHop;
    this.btnSetFrequency = btnSetFrequency;
    this.btnSetLinkParams = btnSetLinkParams;
    this.btnSetMemoryBank = btnSetMemoryBank;
    this.btnSetPower = btnSetPower;
    this.btnSetProtocol = btnSetProtocol;
    this.btnSetSession = btnSetSession;
    this.cbFastID = cbFastID;
    this.cbTagFocus = cbTagFocus;
    this.etLength = etLength;
    this.etOffset = etOffset;
    this.llFreHop = llFreHop;
    this.llMemoryBankParams = llMemoryBankParams;
    this.llSession = llSession;
    this.rbAmerica = rbAmerica;
    this.rbChina = rbChina;
    this.rbEurope = rbEurope;
    this.rbFastInventoryClose = rbFastInventoryClose;
    this.rbFastInventoryOpen = rbFastInventoryOpen;
    this.rbOthers = rbOthers;
    this.spFreHop = spFreHop;
    this.spFrequency = spFrequency;
    this.spInventoried = spInventoried;
    this.spMemoryBank = spMemoryBank;
    this.spPower = spPower;
    this.spSessionID = spSessionID;
    this.splinkParams = splinkParams;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentUhfSetBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentUhfSetBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_uhf_set, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentUhfSetBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.SpinnerAgreement;
      Spinner SpinnerAgreement = ViewBindings.findChildViewById(rootView, id);
      if (SpinnerAgreement == null) {
        break missingId;
      }

      id = R.id.btnFactoryReset;
      Button btnFactoryReset = ViewBindings.findChildViewById(rootView, id);
      if (btnFactoryReset == null) {
        break missingId;
      }

      id = R.id.btnGetFastInventory;
      Button btnGetFastInventory = ViewBindings.findChildViewById(rootView, id);
      if (btnGetFastInventory == null) {
        break missingId;
      }

      id = R.id.btnGetFrequency;
      Button btnGetFrequency = ViewBindings.findChildViewById(rootView, id);
      if (btnGetFrequency == null) {
        break missingId;
      }

      id = R.id.btnGetLinkParams;
      Button btnGetLinkParams = ViewBindings.findChildViewById(rootView, id);
      if (btnGetLinkParams == null) {
        break missingId;
      }

      id = R.id.btnGetMemoryBank;
      Button btnGetMemoryBank = ViewBindings.findChildViewById(rootView, id);
      if (btnGetMemoryBank == null) {
        break missingId;
      }

      id = R.id.btnGetPower;
      Button btnGetPower = ViewBindings.findChildViewById(rootView, id);
      if (btnGetPower == null) {
        break missingId;
      }

      id = R.id.btnGetSession;
      Button btnGetSession = ViewBindings.findChildViewById(rootView, id);
      if (btnGetSession == null) {
        break missingId;
      }

      id = R.id.btnSetFastInventory;
      Button btnSetFastInventory = ViewBindings.findChildViewById(rootView, id);
      if (btnSetFastInventory == null) {
        break missingId;
      }

      id = R.id.btnSetFreHop;
      Button btnSetFreHop = ViewBindings.findChildViewById(rootView, id);
      if (btnSetFreHop == null) {
        break missingId;
      }

      id = R.id.btnSetFrequency;
      Button btnSetFrequency = ViewBindings.findChildViewById(rootView, id);
      if (btnSetFrequency == null) {
        break missingId;
      }

      id = R.id.btnSetLinkParams;
      Button btnSetLinkParams = ViewBindings.findChildViewById(rootView, id);
      if (btnSetLinkParams == null) {
        break missingId;
      }

      id = R.id.btnSetMemoryBank;
      Button btnSetMemoryBank = ViewBindings.findChildViewById(rootView, id);
      if (btnSetMemoryBank == null) {
        break missingId;
      }

      id = R.id.btnSetPower;
      Button btnSetPower = ViewBindings.findChildViewById(rootView, id);
      if (btnSetPower == null) {
        break missingId;
      }

      id = R.id.btnSetProtocol;
      Button btnSetProtocol = ViewBindings.findChildViewById(rootView, id);
      if (btnSetProtocol == null) {
        break missingId;
      }

      id = R.id.btnSetSession;
      Button btnSetSession = ViewBindings.findChildViewById(rootView, id);
      if (btnSetSession == null) {
        break missingId;
      }

      id = R.id.cbFastID;
      CheckBox cbFastID = ViewBindings.findChildViewById(rootView, id);
      if (cbFastID == null) {
        break missingId;
      }

      id = R.id.cbTagFocus;
      CheckBox cbTagFocus = ViewBindings.findChildViewById(rootView, id);
      if (cbTagFocus == null) {
        break missingId;
      }

      id = R.id.etLength;
      EditText etLength = ViewBindings.findChildViewById(rootView, id);
      if (etLength == null) {
        break missingId;
      }

      id = R.id.etOffset;
      EditText etOffset = ViewBindings.findChildViewById(rootView, id);
      if (etOffset == null) {
        break missingId;
      }

      id = R.id.ll_freHop;
      LinearLayout llFreHop = ViewBindings.findChildViewById(rootView, id);
      if (llFreHop == null) {
        break missingId;
      }

      id = R.id.llMemoryBankParams;
      LinearLayout llMemoryBankParams = ViewBindings.findChildViewById(rootView, id);
      if (llMemoryBankParams == null) {
        break missingId;
      }

      id = R.id.llSession;
      LinearLayout llSession = ViewBindings.findChildViewById(rootView, id);
      if (llSession == null) {
        break missingId;
      }

      id = R.id.rb_America;
      RadioButton rbAmerica = ViewBindings.findChildViewById(rootView, id);
      if (rbAmerica == null) {
        break missingId;
      }

      id = R.id.rb_china;
      RadioButton rbChina = ViewBindings.findChildViewById(rootView, id);
      if (rbChina == null) {
        break missingId;
      }

      id = R.id.rb_Europe;
      RadioButton rbEurope = ViewBindings.findChildViewById(rootView, id);
      if (rbEurope == null) {
        break missingId;
      }

      id = R.id.rbFastInventoryClose;
      RadioButton rbFastInventoryClose = ViewBindings.findChildViewById(rootView, id);
      if (rbFastInventoryClose == null) {
        break missingId;
      }

      id = R.id.rbFastInventoryOpen;
      RadioButton rbFastInventoryOpen = ViewBindings.findChildViewById(rootView, id);
      if (rbFastInventoryOpen == null) {
        break missingId;
      }

      id = R.id.rb_Others;
      RadioButton rbOthers = ViewBindings.findChildViewById(rootView, id);
      if (rbOthers == null) {
        break missingId;
      }

      id = R.id.spFreHop;
      Spinner spFreHop = ViewBindings.findChildViewById(rootView, id);
      if (spFreHop == null) {
        break missingId;
      }

      id = R.id.spFrequency;
      Spinner spFrequency = ViewBindings.findChildViewById(rootView, id);
      if (spFrequency == null) {
        break missingId;
      }

      id = R.id.spInventoried;
      Spinner spInventoried = ViewBindings.findChildViewById(rootView, id);
      if (spInventoried == null) {
        break missingId;
      }

      id = R.id.spMemoryBank;
      Spinner spMemoryBank = ViewBindings.findChildViewById(rootView, id);
      if (spMemoryBank == null) {
        break missingId;
      }

      id = R.id.spPower;
      Spinner spPower = ViewBindings.findChildViewById(rootView, id);
      if (spPower == null) {
        break missingId;
      }

      id = R.id.spSessionID;
      Spinner spSessionID = ViewBindings.findChildViewById(rootView, id);
      if (spSessionID == null) {
        break missingId;
      }

      id = R.id.splinkParams;
      Spinner splinkParams = ViewBindings.findChildViewById(rootView, id);
      if (splinkParams == null) {
        break missingId;
      }

      return new FragmentUhfSetBinding((ScrollView) rootView, SpinnerAgreement, btnFactoryReset,
          btnGetFastInventory, btnGetFrequency, btnGetLinkParams, btnGetMemoryBank, btnGetPower,
          btnGetSession, btnSetFastInventory, btnSetFreHop, btnSetFrequency, btnSetLinkParams,
          btnSetMemoryBank, btnSetPower, btnSetProtocol, btnSetSession, cbFastID, cbTagFocus,
          etLength, etOffset, llFreHop, llMemoryBankParams, llSession, rbAmerica, rbChina, rbEurope,
          rbFastInventoryClose, rbFastInventoryOpen, rbOthers, spFreHop, spFrequency, spInventoried,
          spMemoryBank, spPower, spSessionID, splinkParams);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
