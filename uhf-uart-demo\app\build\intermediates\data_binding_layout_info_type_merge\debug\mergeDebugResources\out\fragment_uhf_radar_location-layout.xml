<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_uhf_radar_location" modulePackage="com.example.uhf" filePath="app\src\main\res\layout\fragment_uhf_radar_location.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_uhf_radar_location_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="112" endOffset="51"/></Target><Target id="@+id/epcLayout" view="LinearLayout"><Expressions/><location startLine="9" startOffset="4" endLine="27" endOffset="18"/></Target><Target id="@+id/etRadarEPC" view="EditText"><Expressions/><location startLine="23" startOffset="8" endLine="26" endOffset="50"/></Target><Target id="@+id/radarView" view="com.example.uhf.view.RadarView"><Expressions/><location startLine="30" startOffset="4" endLine="36" endOffset="45"/></Target><Target id="@+id/_llSearchRange" view="LinearLayout"><Expressions/><location startLine="38" startOffset="4" endLine="73" endOffset="18"/></Target><Target id="@+id/seekBarPower" view="com.example.uhf.view.CircleSeekBar"><Expressions/><location startLine="54" startOffset="8" endLine="64" endOffset="60"/></Target><Target id="@+id/tvSearchRange" view="TextView"><Expressions/><location startLine="75" startOffset="4" endLine="85" endOffset="43"/></Target><Target id="@+id/llButton" view="LinearLayout"><Expressions/><location startLine="87" startOffset="4" endLine="111" endOffset="18"/></Target><Target id="@+id/btRadarStart" view="Button"><Expressions/><location startLine="98" startOffset="8" endLine="103" endOffset="51"/></Target><Target id="@+id/btRadarStop" view="Button"><Expressions/><location startLine="105" startOffset="8" endLine="110" endOffset="50"/></Target></Targets></Layout>