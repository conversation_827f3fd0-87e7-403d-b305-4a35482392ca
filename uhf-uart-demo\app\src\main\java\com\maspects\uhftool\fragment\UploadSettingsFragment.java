package com.maspects.uhftool.fragment;

import android.app.AlertDialog;
import android.app.ProgressDialog;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import androidx.fragment.app.Fragment;

import com.maspects.uhftool.R;
import com.maspects.uhftool.upload.models.UploadResponse;
import com.maspects.uhftool.upload.service.UploadService;
import com.maspects.uhftool.upload.utils.UploadStatusTracker;

/**
 * Fragment for configuring upload settings
 * Allows users to set server URL, API key, and upload preferences
 */
public class UploadSettingsFragment extends Fragment {
    
    private static final String TAG = "UploadSettingsFragment";
    
    private EditText etServerUrl;
    private EditText etApiKey;
    private EditText etUploadEndpoint;
    private CheckBox cbAutoUpload;
    private Button btnTestConnection;
    private Button btnSaveSettings;
    private TextView tvDeviceId;
    private TextView tvConnectionStatus;
    private TextView tvUploadStats;
    private TextView tvLastUpload;
    
    private UploadService uploadService;
    
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_upload_settings, container, false);
        
        initializeViews(view);
        initializeUploadService();
        loadCurrentSettings();
        setupClickListeners();
        
        return view;
    }
    
    private void initializeViews(View view) {
        etServerUrl = view.findViewById(R.id.etServerUrl);
        etApiKey = view.findViewById(R.id.etApiKey);
        etUploadEndpoint = view.findViewById(R.id.etUploadEndpoint);
        cbAutoUpload = view.findViewById(R.id.cbAutoUpload);
        btnTestConnection = view.findViewById(R.id.btnTestConnection);
        btnSaveSettings = view.findViewById(R.id.btnSaveSettings);
        tvDeviceId = view.findViewById(R.id.tvDeviceId);
        tvConnectionStatus = view.findViewById(R.id.tvConnectionStatus);
        tvUploadStats = view.findViewById(R.id.tvUploadStats);
        tvLastUpload = view.findViewById(R.id.tvLastUpload);
    }
    
    private void initializeUploadService() {
        uploadService = new UploadService(getContext());
    }
    
    private void loadCurrentSettings() {
        etServerUrl.setText(uploadService.getServerUrl());
        etApiKey.setText(uploadService.getApiKey());
        etUploadEndpoint.setText(uploadService.getUploadEndpoint());
        cbAutoUpload.setChecked(uploadService.isAutoUploadEnabled());
        tvDeviceId.setText("Device ID: " + uploadService.getDeviceId());

        updateConnectionStatus();
        updateUploadStatistics();
    }
    
    private void setupClickListeners() {
        btnTestConnection.setOnClickListener(v -> testConnection());
        btnSaveSettings.setOnClickListener(v -> saveSettings());
    }
    
    private void saveSettings() {
        String serverUrl = etServerUrl.getText().toString().trim();
        String apiKey = etApiKey.getText().toString().trim();
        String endpoint = etUploadEndpoint.getText().toString().trim();
        boolean autoUpload = cbAutoUpload.isChecked();
        
        // Validate inputs
        if (TextUtils.isEmpty(serverUrl)) {
            etServerUrl.setError("Server URL is required");
            etServerUrl.requestFocus();
            return;
        }
        
        if (!serverUrl.startsWith("http://") && !serverUrl.startsWith("https://")) {
            etServerUrl.setError("Server URL must start with http:// or https://");
            etServerUrl.requestFocus();
            return;
        }
        
        if (!serverUrl.endsWith("/")) {
            serverUrl += "/";
        }
        
        if (TextUtils.isEmpty(apiKey)) {
            etApiKey.setError("API Key is required");
            etApiKey.requestFocus();
            return;
        }
        
        if (TextUtils.isEmpty(endpoint)) {
            endpoint = "api/rfid/upload";
        }
        
        // Save settings
        uploadService.setServerUrl(serverUrl);
        uploadService.setApiKey(apiKey);
        uploadService.setUploadEndpoint(endpoint);
        uploadService.setAutoUpload(autoUpload);
        
        updateConnectionStatus();
        updateUploadStatistics();

        Toast.makeText(getContext(), "Settings saved successfully", Toast.LENGTH_SHORT).show();
    }
    
    private void testConnection() {
        if (!validateBasicSettings()) {
            return;
        }
        
        // Save current settings temporarily for testing
        String serverUrl = etServerUrl.getText().toString().trim();
        String apiKey = etApiKey.getText().toString().trim();
        String endpoint = etUploadEndpoint.getText().toString().trim();
        
        if (!serverUrl.endsWith("/")) {
            serverUrl += "/";
        }
        
        if (TextUtils.isEmpty(endpoint)) {
            endpoint = "api/rfid/upload";
        }
        
        uploadService.setServerUrl(serverUrl);
        uploadService.setApiKey(apiKey);
        uploadService.setUploadEndpoint(endpoint);
        
        ProgressDialog progressDialog = new ProgressDialog(getContext());
        progressDialog.setMessage("Testing connection...");
        progressDialog.setCancelable(false);
        progressDialog.show();
        
        uploadService.testConnection(new UploadService.UploadCallback() {
            @Override
            public void onUploadSuccess(UploadResponse response) {
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        progressDialog.dismiss();
                        tvConnectionStatus.setText("Status: Connected ✓");
                        tvConnectionStatus.setTextColor(getResources().getColor(android.R.color.holo_green_dark));
                        
                        new AlertDialog.Builder(getContext())
                                .setTitle("Connection Test")
                                .setMessage("Connection successful!\n\n" + 
                                          (response != null ? response.getMessage() : "Server responded successfully"))
                                .setPositiveButton("OK", null)
                                .show();
                    });
                }
            }
            
            @Override
            public void onUploadError(String error) {
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        progressDialog.dismiss();
                        tvConnectionStatus.setText("Status: Connection Failed ✗");
                        tvConnectionStatus.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
                        
                        new AlertDialog.Builder(getContext())
                                .setTitle("Connection Test Failed")
                                .setMessage("Failed to connect to server:\n\n" + error)
                                .setPositiveButton("OK", null)
                                .show();
                    });
                }
            }
            
            @Override
            public void onUploadProgress(int progress) {
                // Not used for connection test
            }
        });
    }
    
    private boolean validateBasicSettings() {
        String serverUrl = etServerUrl.getText().toString().trim();
        String apiKey = etApiKey.getText().toString().trim();
        
        if (TextUtils.isEmpty(serverUrl)) {
            etServerUrl.setError("Server URL is required for testing");
            etServerUrl.requestFocus();
            return false;
        }
        
        if (!serverUrl.startsWith("http://") && !serverUrl.startsWith("https://")) {
            etServerUrl.setError("Server URL must start with http:// or https://");
            etServerUrl.requestFocus();
            return false;
        }
        
        if (TextUtils.isEmpty(apiKey)) {
            etApiKey.setError("API Key is required for testing");
            etApiKey.requestFocus();
            return false;
        }
        
        return true;
    }
    
    private void updateConnectionStatus() {
        if (uploadService.isConfigured()) {
            tvConnectionStatus.setText("Status: Configured (Test connection to verify)");
            tvConnectionStatus.setTextColor(getResources().getColor(android.R.color.holo_orange_dark));
        } else {
            tvConnectionStatus.setText("Status: Not Configured");
            tvConnectionStatus.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
        }
    }

    private void updateUploadStatistics() {
        if (uploadService != null) {
            UploadStatusTracker.UploadStatistics stats = uploadService.getStatusTracker().getUploadStatistics();

            String statsText = String.format("Total: %d | Success: %d | Failed: %d | Rate: %.1f%%",
                    stats.totalUploads, stats.successfulUploads, stats.failedUploads, stats.getSuccessRate());
            tvUploadStats.setText(statsText);

            tvLastUpload.setText("Last Upload: " + stats.getLastUploadTimeFormatted());
        }
    }
}
