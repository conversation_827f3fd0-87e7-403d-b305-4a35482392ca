# PowerShell script to completely remove R class imports

Write-Host "Removing all R class imports..."

# Get all Java files in the project
$javaFiles = Get-ChildItem -Path "app\src\main\java\com\maspects\uhftool" -Recurse -Filter "*.java"

$totalFiles = $javaFiles.Count
$currentFile = 0

foreach ($file in $javaFiles) {
    $currentFile++
    Write-Host "[$currentFile/$totalFiles] Processing: $($file.Name)"
    
    try {
        # Read file content
        $content = Get-Content $file.FullName -Raw -ErrorAction Stop
        
        if ($content) {
            # Track if any changes were made
            $originalContent = $content
            
            # Remove all R class imports completely
            $content = $content -replace "import com\.maspects\.uhftool\.R;\r?\n", ""
            $content = $content -replace "import com\.maspects\.uhftool\.R;", ""
            
            # Only write if changes were made
            if ($content -ne $originalContent) {
                Set-Content -Path $file.FullName -Value $content -NoNewline -ErrorAction Stop
                Write-Host "  Removed R import"
            } else {
                Write-Host "  No R import found"
            }
        }
    }
    catch {
        Write-Host "  Error processing file: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`nR import removal completed!" -ForegroundColor Green
