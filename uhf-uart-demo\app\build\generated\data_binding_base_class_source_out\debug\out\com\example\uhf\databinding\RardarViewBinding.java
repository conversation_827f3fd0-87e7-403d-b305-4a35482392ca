// Generated by view binder compiler. Do not edit!
package com.example.uhf.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.uhf.R;
import com.example.uhf.view.RadarBackgroundView;
import com.example.uhf.view.RadarPanelView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class RardarViewBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageView CenterImage;

  @NonNull
  public final RadarPanelView LabelPanelView;

  @NonNull
  public final RadarBackgroundView RadarBackgroundView;

  private RardarViewBinding(@NonNull ConstraintLayout rootView, @NonNull ImageView CenterImage,
      @NonNull RadarPanelView LabelPanelView, @NonNull RadarBackgroundView RadarBackgroundView) {
    this.rootView = rootView;
    this.CenterImage = CenterImage;
    this.LabelPanelView = LabelPanelView;
    this.RadarBackgroundView = RadarBackgroundView;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static RardarViewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static RardarViewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.rardar_view, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static RardarViewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id._centerImage;
      ImageView CenterImage = ViewBindings.findChildViewById(rootView, id);
      if (CenterImage == null) {
        break missingId;
      }

      id = R.id._labelPanelView;
      RadarPanelView LabelPanelView = ViewBindings.findChildViewById(rootView, id);
      if (LabelPanelView == null) {
        break missingId;
      }

      id = R.id._radarBackgroundView;
      RadarBackgroundView RadarBackgroundView = ViewBindings.findChildViewById(rootView, id);
      if (RadarBackgroundView == null) {
        break missingId;
      }

      return new RardarViewBinding((ConstraintLayout) rootView, CenterImage, LabelPanelView,
          RadarBackgroundView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
