<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:scrollbars="vertical">

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#AAAAAA"
            android:padding="4dp"
            android:text="@string/uhf_title_filter"
            android:textSize="16sp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/rectangle_bg"
            android:orientation="vertical"
            android:padding="8dp">

            <CheckBox
                android:id="@+id/cb_filter_lock"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/button_enable"
                android:textColor="@drawable/check_text_color"
                android:textSize="19sp" />

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/tvPtr" />

                <EditText
                    android:id="@+id/etPtr_filter_lock"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:inputType="numberSigned"
                    android:text="32" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="30dp"
                    android:text="(bit)" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="长度:" />

                <EditText
                    android:id="@+id/etLen_filter_lock"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:inputType="numberSigned"
                    android:text="0" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="(bit)" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/tvData_Read" />

                <EditText
                    android:id="@+id/etData_filter_lock"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:hint=""
                    android:inputType="textNoSuggestions"
                    android:singleLine="true" />
            </LinearLayout>

            <RadioGroup
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <RadioButton
                    android:id="@+id/rbEPC_filter_lock"
                    android:layout_width="0dp"
                    android:layout_height="30dp"
                    android:layout_weight="1"
                    android:background="@drawable/rb_bg"
                    android:button="@null"
                    android:checked="true"
                    android:gravity="center"
                    android:text="EPC"
                    android:textColor="@drawable/check_text_color" />

                <RadioButton
                    android:id="@+id/rbTID_filter_lock"
                    android:layout_width="0dp"
                    android:layout_height="30dp"
                    android:layout_marginLeft="10dp"
                    android:layout_weight="1"
                    android:background="@drawable/rb_bg"
                    android:button="@null"
                    android:checked="false"
                    android:gravity="center"
                    android:text="TID"
                    android:textColor="@drawable/check_text_color" />

                <RadioButton
                    android:id="@+id/rbUser_filter_lock"
                    android:layout_width="0dp"
                    android:layout_height="30dp"
                    android:layout_marginLeft="10dp"
                    android:layout_weight="1"
                    android:background="@drawable/rb_bg"
                    android:button="@null"
                    android:checked="false"
                    android:gravity="center"
                    android:text="USER"
                    android:textColor="@drawable/check_text_color" />
            </RadioGroup>
        </LinearLayout>


        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="4dp"
            app:cardCornerRadius="3dp"
            app:cardElevation="3dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="#eeeeee"
                    android:gravity="center"
                    android:padding="2dp"
                    android:text="ISO18000-6C Lock"
                    android:textColor="@color/lightblue"
                    android:textSize="18sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="2dp">

                    <TextView
                        android:layout_width="96dp"
                        android:layout_height="wrap_content"
                        android:text="@string/tvAccessPwd" />

                    <EditText
                        android:id="@+id/EtAccessPwd_Lock"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/rfid_mgs_lockpwdtip"
                        android:textColorHint="@color/red_qian" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="2dp">

                    <TextView
                        android:layout_width="96dp"
                        android:layout_height="wrap_content"
                        android:text="@string/tvLockCode" />

                    <EditText
                        android:id="@+id/etLockCode"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:focusable="false"
                        android:focusableInTouchMode="true" />
                </LinearLayout>

                <Button
                    android:id="@+id/btnLock"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="4dp"
                    android:background="@drawable/button_bg"
                    android:text="@string/uhf_msg_tab_lock"
                    android:textColor="@color/white" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="2dp"
                    android:gravity="left"
                    android:text="@string/rfid_mgs_locktip"
                    android:textColor="@color/green" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="4dp"
            app:cardCornerRadius="3dp"
            app:cardElevation="3dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="#eeeeee"
                    android:gravity="center"
                    android:padding="2dp"
                    android:text="@string/GB_tag_lock"
                    android:textColor="@color/lightblue"
                    android:textSize="18sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="2dp">

                    <TextView
                        android:layout_width="96dp"
                        android:layout_height="wrap_content"
                        android:text="@string/tvAccessPwd" />

                    <EditText
                        android:id="@+id/etGBAccessPwd"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:hint="@string/rfid_mgs_lockpwdtip"
                        android:textColorHint="@color/red_qian" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="2dp">

                    <TextView
                        android:layout_width="96dp"
                        android:layout_height="wrap_content"
                        android:text="@string/storageArea" />

                    <Spinner
                        android:id="@+id/spGBStorageArea"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:entries="@array/storageArea" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/layoutUserAreaNumber"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="2dp"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="96dp"
                        android:layout_height="wrap_content"
                        android:text="@string/user_area_number" />

                    <Spinner
                        android:id="@+id/spGBUserAreaNumber"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:entries="@array/userAreaNumbers" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="2dp">

                    <TextView
                        android:layout_width="96dp"
                        android:layout_height="wrap_content"
                        android:text="@string/configure" />

                    <Spinner
                        android:id="@+id/spGBConfig"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:entries="@array/config" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="2dp">

                    <TextView
                        android:layout_width="96dp"
                        android:layout_height="wrap_content"
                        android:text="@string/action" />

                    <Spinner
                        android:id="@+id/spGBAction"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:entries="@array/action1" />
                </LinearLayout>

                <Button
                    android:id="@+id/btnGBLock"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="4dp"
                    android:background="@drawable/button_bg"
                    android:text="@string/uhf_msg_tab_lock"
                    android:textColor="@color/white" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>
    </LinearLayout>
</ScrollView>