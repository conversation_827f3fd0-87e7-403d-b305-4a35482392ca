<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_uhflocation" modulePackage="com.example.uhf" filePath="app\src\main\res\layout\fragment_uhflocation.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.FrameLayout"><Targets><Target tag="layout/fragment_uhflocation_0" view="FrameLayout"><Expressions/><location startLine="1" startOffset="0" endLine="107" endOffset="13"/></Target><Target id="@+id/etEPC" view="EditText"><Expressions/><location startLine="19" startOffset="12" endLine="22" endOffset="54"/></Target><Target id="@+id/llChart" view="com.example.uhf.view.UhfLocationCanvasView"><Expressions/><location startLine="29" startOffset="12" endLine="36" endOffset="17"/></Target><Target id="@+id/seekBarPower" view="com.example.uhf.view.CircleSeekBar"><Expressions/><location startLine="53" startOffset="12" endLine="63" endOffset="64"/></Target><Target id="@+id/tvSearchRange" view="TextView"><Expressions/><location startLine="74" startOffset="8" endLine="83" endOffset="47"/></Target><Target id="@+id/btStart" view="Button"><Expressions/><location startLine="92" startOffset="16" endLine="97" endOffset="58"/></Target><Target id="@+id/btStop" view="Button"><Expressions/><location startLine="98" startOffset="16" endLine="103" endOffset="58"/></Target></Targets></Layout>