<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.example.uhf" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.fragment.app.FragmentTabHost" rootNodeViewId="@android:id/tabhost"><Targets><Target id="@android:id/tabhost" tag="layout/activity_main_0" view="androidx.fragment.app.FragmentTabHost"><Expressions/><location startLine="0" startOffset="0" endLine="40" endOffset="40"/></Target><Target id="@+id/hScroller_mytabhostactivity" view="HorizontalScrollView"><Expressions/><location startLine="10" startOffset="8" endLine="22" endOffset="30"/></Target><Target id="@android:id/tabs" view="TabWidget"><Expressions/><location startLine="17" startOffset="12" endLine="21" endOffset="50"/></Target><Target id="@android:id/tabcontent" view="FrameLayout"><Expressions/><location startLine="26" startOffset="8" endLine="30" endOffset="39"/></Target><Target id="@+id/realtabcontent" view="FrameLayout"><Expressions/><location startLine="32" startOffset="8" endLine="36" endOffset="39"/></Target></Targets></Layout>