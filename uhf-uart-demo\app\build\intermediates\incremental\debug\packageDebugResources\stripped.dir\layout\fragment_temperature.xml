<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.example.uhf.fragment.temperatureFragment">
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:padding="5dp" >
    <LinearLayout
        android:visibility="invisible"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:padding="5dp" >

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/tvBank"
            android:textSize="16sp" />

        <Spinner
            android:id="@+id/SpinnerBank_T"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:entries="@array/arrayBank" />
    </LinearLayout>
        <LinearLayout
            android:visibility="invisible"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:padding="5dp" >

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="掩码的地址"
                android:textSize="16sp" />

            <EditText
                android:inputType="number"
                android:id="@+id/etMSA"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"/>
        </LinearLayout>
        <LinearLayout
            android:visibility="invisible"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:padding="5dp" >

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="掩码长度"
                android:textSize="16sp" />

            <EditText
                android:inputType="number"
                android:id="@+id/etMDL"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content" />
        </LinearLayout>
        <LinearLayout
            android:visibility="invisible"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:padding="5dp" >

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="数据"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/etData"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content" />
        </LinearLayout>
        <Button
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="获取温度"
            android:id="@+id/btOK"/>
        <TextView
            android:id="@+id/tvData"
            android:layout_gravity="center"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text=""
            android:textSize="16sp" />

</LinearLayout>
</FrameLayout>
