// Generated by view binder compiler. Do not edit!
package com.example.uhf.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.uhf.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentUhfupgradeBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnBrowser;

  @NonNull
  public final Button btnUpgrade;

  @NonNull
  public final EditText etFile;

  @NonNull
  public final RadioButton rbEx10;

  @NonNull
  public final RadioButton rbUhfModule;

  @NonNull
  public final TextView tvMsg;

  private FragmentUhfupgradeBinding(@NonNull LinearLayout rootView, @NonNull Button btnBrowser,
      @NonNull Button btnUpgrade, @NonNull EditText etFile, @NonNull RadioButton rbEx10,
      @NonNull RadioButton rbUhfModule, @NonNull TextView tvMsg) {
    this.rootView = rootView;
    this.btnBrowser = btnBrowser;
    this.btnUpgrade = btnUpgrade;
    this.etFile = etFile;
    this.rbEx10 = rbEx10;
    this.rbUhfModule = rbUhfModule;
    this.tvMsg = tvMsg;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentUhfupgradeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentUhfupgradeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_uhfupgrade, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentUhfupgradeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnBrowser;
      Button btnBrowser = ViewBindings.findChildViewById(rootView, id);
      if (btnBrowser == null) {
        break missingId;
      }

      id = R.id.btnUpgrade;
      Button btnUpgrade = ViewBindings.findChildViewById(rootView, id);
      if (btnUpgrade == null) {
        break missingId;
      }

      id = R.id.et_file;
      EditText etFile = ViewBindings.findChildViewById(rootView, id);
      if (etFile == null) {
        break missingId;
      }

      id = R.id.rb_ex10;
      RadioButton rbEx10 = ViewBindings.findChildViewById(rootView, id);
      if (rbEx10 == null) {
        break missingId;
      }

      id = R.id.rb_uhf_module;
      RadioButton rbUhfModule = ViewBindings.findChildViewById(rootView, id);
      if (rbUhfModule == null) {
        break missingId;
      }

      id = R.id.tvMsg;
      TextView tvMsg = ViewBindings.findChildViewById(rootView, id);
      if (tvMsg == null) {
        break missingId;
      }

      return new FragmentUhfupgradeBinding((LinearLayout) rootView, btnBrowser, btnUpgrade, etFile,
          rbEx10, rbUhfModule, tvMsg);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
