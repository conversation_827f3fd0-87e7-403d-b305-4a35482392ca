// Generated by view binder compiler. Do not edit!
package com.example.uhf.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.Spinner;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.uhf.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentTemperatureBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final Spinner SpinnerBankT;

  @NonNull
  public final Button btOK;

  @NonNull
  public final EditText etData;

  @NonNull
  public final EditText etMDL;

  @NonNull
  public final EditText etMSA;

  @NonNull
  public final TextView tvData;

  private FragmentTemperatureBinding(@NonNull FrameLayout rootView, @NonNull Spinner SpinnerBankT,
      @NonNull Button btOK, @NonNull EditText etData, @NonNull EditText etMDL,
      @NonNull EditText etMSA, @NonNull TextView tvData) {
    this.rootView = rootView;
    this.SpinnerBankT = SpinnerBankT;
    this.btOK = btOK;
    this.etData = etData;
    this.etMDL = etMDL;
    this.etMSA = etMSA;
    this.tvData = tvData;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentTemperatureBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentTemperatureBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_temperature, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentTemperatureBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.SpinnerBank_T;
      Spinner SpinnerBankT = ViewBindings.findChildViewById(rootView, id);
      if (SpinnerBankT == null) {
        break missingId;
      }

      id = R.id.btOK;
      Button btOK = ViewBindings.findChildViewById(rootView, id);
      if (btOK == null) {
        break missingId;
      }

      id = R.id.etData;
      EditText etData = ViewBindings.findChildViewById(rootView, id);
      if (etData == null) {
        break missingId;
      }

      id = R.id.etMDL;
      EditText etMDL = ViewBindings.findChildViewById(rootView, id);
      if (etMDL == null) {
        break missingId;
      }

      id = R.id.etMSA;
      EditText etMSA = ViewBindings.findChildViewById(rootView, id);
      if (etMSA == null) {
        break missingId;
      }

      id = R.id.tvData;
      TextView tvData = ViewBindings.findChildViewById(rootView, id);
      if (tvData == null) {
        break missingId;
      }

      return new FragmentTemperatureBinding((FrameLayout) rootView, SpinnerBankT, btOK, etData,
          etMDL, etMSA, tvData);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
