package com.example.uhf.upload.models;

import com.google.gson.annotations.SerializedName;
import java.util.List;

/**
 * Main upload request model that contains all data to be sent to the server
 * This is the root object that gets serialized to JSON
 */
public class UploadRequest {
    
    @SerializedName("deviceId")
    private String deviceId;
    
    @SerializedName("scanSession")
    private ScanSession scanSession;
    
    @SerializedName("tags")
    private List<TagData> tags;
    
    @SerializedName("uploadTimestamp")
    private String uploadTimestamp;
    
    @SerializedName("totalTagCount")
    private int totalTagCount;
    
    @SerializedName("uniqueTagCount")
    private int uniqueTagCount;
    
    // Default constructor
    public UploadRequest() {}
    
    // Constructor with required fields
    public UploadRequest(String deviceId, ScanSession scanSession, List<TagData> tags) {
        this.deviceId = deviceId;
        this.scanSession = scanSession;
        this.tags = tags;
        this.totalTagCount = calculateTotalCount(tags);
        this.uniqueTagCount = tags != null ? tags.size() : 0;
    }
    
    // Constructor with all fields
    public UploadRequest(String deviceId, ScanSession scanSession, List<TagData> tags, 
                        String uploadTimestamp, int totalTagCount, int uniqueTagCount) {
        this.deviceId = deviceId;
        this.scanSession = scanSession;
        this.tags = tags;
        this.uploadTimestamp = uploadTimestamp;
        this.totalTagCount = totalTagCount;
        this.uniqueTagCount = uniqueTagCount;
    }
    
    // Helper method to calculate total count from all tags
    private int calculateTotalCount(List<TagData> tags) {
        if (tags == null) return 0;
        int total = 0;
        for (TagData tag : tags) {
            total += tag.getCount();
        }
        return total;
    }
    
    // Getters and Setters
    public String getDeviceId() {
        return deviceId;
    }
    
    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }
    
    public ScanSession getScanSession() {
        return scanSession;
    }
    
    public void setScanSession(ScanSession scanSession) {
        this.scanSession = scanSession;
    }
    
    public List<TagData> getTags() {
        return tags;
    }
    
    public void setTags(List<TagData> tags) {
        this.tags = tags;
        this.totalTagCount = calculateTotalCount(tags);
        this.uniqueTagCount = tags != null ? tags.size() : 0;
    }
    
    public String getUploadTimestamp() {
        return uploadTimestamp;
    }
    
    public void setUploadTimestamp(String uploadTimestamp) {
        this.uploadTimestamp = uploadTimestamp;
    }
    
    public int getTotalTagCount() {
        return totalTagCount;
    }
    
    public void setTotalTagCount(int totalTagCount) {
        this.totalTagCount = totalTagCount;
    }
    
    public int getUniqueTagCount() {
        return uniqueTagCount;
    }
    
    public void setUniqueTagCount(int uniqueTagCount) {
        this.uniqueTagCount = uniqueTagCount;
    }
    
    @Override
    public String toString() {
        return "UploadRequest{" +
                "deviceId='" + deviceId + '\'' +
                ", scanSession=" + scanSession +
                ", tags=" + (tags != null ? tags.size() + " tags" : "null") +
                ", uploadTimestamp='" + uploadTimestamp + '\'' +
                ", totalTagCount=" + totalTagCount +
                ", uniqueTagCount=" + uniqueTagCount +
                '}';
    }
}
