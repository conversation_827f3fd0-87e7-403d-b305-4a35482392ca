<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_upload_settings" modulePackage="com.example.uhf" filePath="app\src\main\res\layout\fragment_upload_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_upload_settings_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="189" endOffset="12"/></Target><Target id="@+id/etServerUrl" view="EditText"><Expressions/><location startLine="37" startOffset="12" endLine="42" endOffset="57"/></Target><Target id="@+id/etApiKey" view="EditText"><Expressions/><location startLine="53" startOffset="12" endLine="57" endOffset="50"/></Target><Target id="@+id/etUploadEndpoint" view="EditText"><Expressions/><location startLine="68" startOffset="12" endLine="73" endOffset="48"/></Target><Target id="@+id/cbAutoUpload" view="CheckBox"><Expressions/><location startLine="88" startOffset="8" endLine="93" endOffset="48"/></Target><Target id="@+id/tvDeviceId" view="TextView"><Expressions/><location startLine="106" startOffset="8" endLine="112" endOffset="47"/></Target><Target id="@+id/tvConnectionStatus" view="TextView"><Expressions/><location startLine="115" startOffset="8" endLine="121" endOffset="48"/></Target><Target id="@+id/tvUploadStats" view="TextView"><Expressions/><location startLine="134" startOffset="8" endLine="140" endOffset="47"/></Target><Target id="@+id/tvLastUpload" view="TextView"><Expressions/><location startLine="143" startOffset="8" endLine="149" endOffset="48"/></Target><Target id="@+id/btnTestConnection" view="Button"><Expressions/><location startLine="158" startOffset="12" endLine="165" endOffset="71"/></Target><Target id="@+id/btnSaveSettings" view="Button"><Expressions/><location startLine="167" startOffset="12" endLine="173" endOffset="50"/></Target></Targets></Layout>