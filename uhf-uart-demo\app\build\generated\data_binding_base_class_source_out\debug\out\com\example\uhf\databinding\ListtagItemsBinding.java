// Generated by view binder compiler. Do not edit!
package com.example.uhf.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.uhf.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ListtagItemsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView TvPhase;

  @NonNull
  public final TextView TvTagCount;

  @NonNull
  public final TextView TvTagRssi;

  @NonNull
  public final TextView TvTagUii;

  private ListtagItemsBinding(@NonNull LinearLayout rootView, @NonNull TextView TvPhase,
      @NonNull TextView TvTagCount, @NonNull TextView TvTagRssi, @NonNull TextView TvTagUii) {
    this.rootView = rootView;
    this.TvPhase = TvPhase;
    this.TvTagCount = TvTagCount;
    this.TvTagRssi = TvTagRssi;
    this.TvTagUii = TvTagUii;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ListtagItemsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ListtagItemsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.listtag_items, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ListtagItemsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.TvPhase;
      TextView TvPhase = ViewBindings.findChildViewById(rootView, id);
      if (TvPhase == null) {
        break missingId;
      }

      id = R.id.TvTagCount;
      TextView TvTagCount = ViewBindings.findChildViewById(rootView, id);
      if (TvTagCount == null) {
        break missingId;
      }

      id = R.id.TvTagRssi;
      TextView TvTagRssi = ViewBindings.findChildViewById(rootView, id);
      if (TvTagRssi == null) {
        break missingId;
      }

      id = R.id.TvTagUii;
      TextView TvTagUii = ViewBindings.findChildViewById(rootView, id);
      if (TvTagUii == null) {
        break missingId;
      }

      return new ListtagItemsBinding((LinearLayout) rootView, TvPhase, TvTagCount, TvTagRssi,
          TvTagUii);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
